<?php
// Database configuration for Receipt and Barcode System
class Database {
    private $host = "localhost";
    private $db_name = "iraqi_delivery_system";
    private $username = "root";
    private $password = "";
    private $charset = "utf8mb4";
    public $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
            // Create database if it doesn't exist
            $this->createDatabaseIfNotExists();
            
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }
    
    private function createDatabaseIfNotExists() {
        try {
            // Connect without database name to create it
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $tempConn = new PDO($dsn, $this->username, $this->password);
            
            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` 
                    CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $tempConn->exec($sql);
            
            // Use the database
            $tempConn->exec("USE `" . $this->db_name . "`");
            
            // Create tables if they don't exist
            $this->createTables($tempConn);
            
        } catch(PDOException $exception) {
            echo "Database creation error: " . $exception->getMessage();
        }
    }
    
    private function createTables($conn) {
        // Receipt numbers table
        $receiptTable = "CREATE TABLE IF NOT EXISTS `receipt_numbers` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `receipt_number` VARCHAR(20) NOT NULL UNIQUE,
            `order_id` VARCHAR(20) DEFAULT NULL,
            `format_type` VARCHAR(50) DEFAULT 'IQ-YYYY-XXXXXX',
            `sequence_number` INT NOT NULL,
            `year_created` INT NOT NULL,
            `status` ENUM('active', 'used', 'cancelled', 'archived') DEFAULT 'active',
            `barcode_type` VARCHAR(20) DEFAULT 'code128',
            `barcode_data` TEXT DEFAULT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `created_by` VARCHAR(50) DEFAULT NULL,
            `notes` TEXT DEFAULT NULL,
            INDEX `idx_receipt_number` (`receipt_number`),
            INDEX `idx_order_id` (`order_id`),
            INDEX `idx_year_created` (`year_created`),
            INDEX `idx_status` (`status`),
            INDEX `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        // Barcode history table
        $barcodeTable = "CREATE TABLE IF NOT EXISTS `barcode_history` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `receipt_number` VARCHAR(20) NOT NULL,
            `barcode_type` VARCHAR(20) NOT NULL,
            `barcode_data` TEXT NOT NULL,
            `generated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `generated_by` VARCHAR(50) DEFAULT NULL,
            `settings` JSON DEFAULT NULL,
            INDEX `idx_receipt_number` (`receipt_number`),
            INDEX `idx_barcode_type` (`barcode_type`),
            INDEX `idx_generated_at` (`generated_at`),
            FOREIGN KEY (`receipt_number`) REFERENCES `receipt_numbers`(`receipt_number`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        // Receipt archives table (for deleted/archived receipts)
        $archiveTable = "CREATE TABLE IF NOT EXISTS `receipt_archives` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `original_id` INT NOT NULL,
            `receipt_number` VARCHAR(20) NOT NULL,
            `order_id` VARCHAR(20) DEFAULT NULL,
            `format_type` VARCHAR(50) DEFAULT NULL,
            `sequence_number` INT NOT NULL,
            `year_created` INT NOT NULL,
            `original_status` VARCHAR(20) DEFAULT NULL,
            `archived_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `archived_by` VARCHAR(50) DEFAULT NULL,
            `archive_reason` TEXT DEFAULT NULL,
            INDEX `idx_receipt_number` (`receipt_number`),
            INDEX `idx_archived_at` (`archived_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        // Receipt statistics table (for performance optimization)
        $statsTable = "CREATE TABLE IF NOT EXISTS `receipt_statistics` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `stat_date` DATE NOT NULL UNIQUE,
            `total_generated` INT DEFAULT 0,
            `total_used` INT DEFAULT 0,
            `total_cancelled` INT DEFAULT 0,
            `total_archived` INT DEFAULT 0,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX `idx_stat_date` (`stat_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        // Execute table creation
        $tables = [$receiptTable, $barcodeTable, $archiveTable, $statsTable];
        
        foreach ($tables as $table) {
            try {
                $conn->exec($table);
            } catch(PDOException $e) {
                echo "Table creation error: " . $e->getMessage();
            }
        }
        
        // Insert sample data if tables are empty
        $this->insertSampleData($conn);
    }
    
    private function insertSampleData($conn) {
        // Check if receipt_numbers table has data
        $checkQuery = "SELECT COUNT(*) FROM receipt_numbers";
        $stmt = $conn->prepare($checkQuery);
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // Insert sample receipt numbers
            $sampleReceipts = [
                ['IQ-2024-000001', 'ORD-2024-001', 'IQ-{YEAR}-{SEQUENCE}', 1, 2024, 'used', 'code128', 'system'],
                ['IQ-2024-000002', 'ORD-2024-002', 'IQ-{YEAR}-{SEQUENCE}', 2, 2024, 'used', 'code128', 'system'],
                ['IQ-2024-000003', 'ORD-2024-003', 'IQ-{YEAR}-{SEQUENCE}', 3, 2024, 'active', 'code128', 'system'],
                ['IQ-2024-000004', 'ORD-2024-004', 'IQ-{YEAR}-{SEQUENCE}', 4, 2024, 'active', 'qr', 'system'],
                ['IQ-2024-000005', null, 'IQ-{YEAR}-{SEQUENCE}', 5, 2024, 'active', 'code128', 'system']
            ];
            
            $insertQuery = "INSERT INTO receipt_numbers 
                           (receipt_number, order_id, format_type, sequence_number, year_created, status, barcode_type, created_by) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($insertQuery);
            
            foreach ($sampleReceipts as $receipt) {
                try {
                    $stmt->execute($receipt);
                } catch(PDOException $e) {
                    // Ignore duplicate entry errors
                    if ($e->getCode() != 23000) {
                        echo "Sample data insertion error: " . $e->getMessage();
                    }
                }
            }
        }
        
        // Insert today's statistics if not exists
        $today = date('Y-m-d');
        $checkStatsQuery = "SELECT COUNT(*) FROM receipt_statistics WHERE stat_date = ?";
        $stmt = $conn->prepare($checkStatsQuery);
        $stmt->execute([$today]);
        $statsCount = $stmt->fetchColumn();
        
        if ($statsCount == 0) {
            $insertStatsQuery = "INSERT INTO receipt_statistics (stat_date, total_generated, total_used) VALUES (?, 5, 2)";
            $stmt = $conn->prepare($insertStatsQuery);
            $stmt->execute([$today]);
        }
    }
}

// Utility functions for receipt system
class ReceiptUtils {
    public static function validateReceiptFormat($receiptNumber) {
        // Validate format: IQ-YYYY-XXXXXX or similar patterns
        return preg_match('/^[A-Z]{2,3}-\d{4}-\d{6}$/', $receiptNumber);
    }
    
    public static function generateReceiptNumber($format = 'IQ-{YEAR}-{SEQUENCE}', $sequence = 1) {
        $year = date('Y');
        return str_replace(
            ['{YEAR}', '{SEQUENCE}'],
            [$year, str_pad($sequence, 6, '0', STR_PAD_LEFT)],
            $format
        );
    }
    
    public static function extractSequenceFromReceipt($receiptNumber) {
        $parts = explode('-', $receiptNumber);
        return isset($parts[2]) ? intval($parts[2]) : 0;
    }
    
    public static function extractYearFromReceipt($receiptNumber) {
        $parts = explode('-', $receiptNumber);
        return isset($parts[1]) ? intval($parts[1]) : date('Y');
    }
    
    public static function sanitizeInput($input) {
        return htmlspecialchars(strip_tags(trim($input)));
    }
    
    public static function convertArabicToEnglishNumbers($text) {
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        return str_replace($arabicNumbers, $englishNumbers, $text);
    }
    
    public static function logActivity($action, $receiptNumber, $userId = 'system', $details = '') {
        // Log activity to file or database
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'receipt_number' => $receiptNumber,
            'user_id' => $userId,
            'details' => $details
        ];
        
        $logFile = __DIR__ . '/logs/receipt_activity.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    }
}

// Error handling
function handleError($errno, $errstr, $errfile, $errline) {
    $error = [
        'error' => $errstr,
        'file' => $errfile,
        'line' => $errline,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    error_log(json_encode($error, JSON_UNESCAPED_UNICODE));
    
    if (ini_get('display_errors')) {
        echo json_encode(['success' => false, 'error' => $errstr], JSON_UNESCAPED_UNICODE);
    }
}

set_error_handler('handleError');

// Set timezone
date_default_timezone_set('Asia/Baghdad');

// Enable error reporting for development
if (defined('DEVELOPMENT') && DEVELOPMENT) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}
?>
