// Regions Management System
class RegionsManager {
    constructor() {
        this.regions = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            search: '',
            status: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'regions-search') {
                this.filters.search = e.target.value;
                this.filterRegions();
            }
        });

        // Filter by status
        document.addEventListener('change', (e) => {
            if (e.target.id === 'status-filter') {
                this.filters.status = e.target.value;
                this.filterRegions();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-region-btn') || e.target.closest('.add-region-btn')) {
                e.preventDefault();
                this.showAddRegionModal();
            }
            if (e.target.matches('.edit-region-btn') || e.target.closest('.edit-region-btn')) {
                e.preventDefault();
                const regionId = e.target.closest('.edit-region-btn').getAttribute('data-region-id');
                this.showEditRegionModal(regionId);
            }
            if (e.target.matches('.delete-region-btn') || e.target.closest('.delete-region-btn')) {
                e.preventDefault();
                const regionId = e.target.closest('.delete-region-btn').getAttribute('data-region-id');
                this.deleteRegion(regionId);
            }
            if (e.target.matches('.toggle-status-btn') || e.target.closest('.toggle-status-btn')) {
                e.preventDefault();
                const regionId = e.target.closest('.toggle-status-btn').getAttribute('data-region-id');
                this.toggleRegionStatus(regionId);
            }
        });

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.getAttribute('data-page'));
                this.currentPage = page;
                this.renderRegions();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const regionsHTML = `
            <div class="regions-header">
                <div class="regions-title">
                    <h1>إدارة المناطق</h1>
                    <p>إدارة مناطق التوصيل والخدمة</p>
                </div>
                <div class="regions-actions">
                    <button class="neu-btn primary add-region-btn">
                        <i class="fas fa-map-marker-alt"></i>
                        إضافة منطقة
                    </button>
                    <button class="neu-btn secondary export-btn">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="regions-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="regions-search" class="neu-input" placeholder="اسم المنطقة...">
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="inactive">غير نشطة</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="regions-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-map"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-regions">0</h3>
                        <p>إجمالي المناطق</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-regions">0</h3>
                        <p>مناطق نشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="inactive-regions">0</h3>
                        <p>مناطق غير نشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="regions-with-drivers">0</h3>
                        <p>مناطق بها مندوبين</p>
                    </div>
                </div>
            </div>

            <div class="regions-table-container">
                <div class="table-header">
                    <h3>قائمة المناطق</h3>
                    <div class="table-actions">
                        <button class="neu-btn small refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table class="regions-table" id="regions-table">
                        <thead>
                            <tr>
                                <th>المنطقة</th>
                                <th>المحافظة</th>
                                <th>رسوم التوصيل</th>
                                <th>المندوبين</th>
                                <th>الطلبات</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="regions-tbody">
                            <!-- Regions will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination" id="regions-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = regionsHTML;
        this.renderRegions();
        this.updateStats();
    }

    loadSampleData() {
        this.regions = [
            {
                id: 1,
                name: 'الكرخ',
                city: 'بغداد',
                province: 'بغداد',
                deliveryFee: 5000,
                isActive: true,
                driversCount: 3,
                ordersCount: 25,
                createdAt: '2024-01-15',
                description: 'منطقة الكرخ في بغداد'
            },
            {
                id: 2,
                name: 'الرصافة',
                city: 'بغداد',
                province: 'بغداد',
                deliveryFee: 5000,
                isActive: true,
                driversCount: 2,
                ordersCount: 18,
                createdAt: '2024-01-15',
                description: 'منطقة الرصافة في بغداد'
            },
            {
                id: 3,
                name: 'الصدر',
                city: 'بغداد',
                province: 'بغداد',
                deliveryFee: 6000,
                isActive: true,
                driversCount: 1,
                ordersCount: 12,
                createdAt: '2024-02-01',
                description: 'مدينة الصدر في بغداد'
            },
            {
                id: 4,
                name: 'الكاظمية',
                city: 'بغداد',
                province: 'بغداد',
                deliveryFee: 5500,
                isActive: true,
                driversCount: 1,
                ordersCount: 8,
                createdAt: '2024-02-15',
                description: 'منطقة الكاظمية في بغداد'
            },
            {
                id: 5,
                name: 'الأعظمية',
                city: 'بغداد',
                province: 'بغداد',
                deliveryFee: 5500,
                isActive: true,
                driversCount: 0,
                ordersCount: 5,
                createdAt: '2024-03-01',
                description: 'منطقة الأعظمية في بغداد'
            },
            {
                id: 6,
                name: 'البصرة المركز',
                city: 'البصرة',
                province: 'البصرة',
                deliveryFee: 8000,
                isActive: false,
                driversCount: 0,
                ordersCount: 0,
                createdAt: '2024-03-15',
                description: 'مركز محافظة البصرة'
            }
        ];
    }

    renderRegions() {
        const tbody = document.getElementById('regions-tbody');
        if (!tbody) return;

        const filteredRegions = this.getFilteredRegions();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const regionsToShow = filteredRegions.slice(startIndex, endIndex);

        tbody.innerHTML = regionsToShow.map(region => `
            <tr>
                <td>
                    <div class="region-info">
                        <div class="region-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="region-details">
                            <strong>${region.name}</strong>
                            <small>${region.description}</small>
                        </div>
                    </div>
                </td>
                <td>${region.province}</td>
                <td>
                    <span class="delivery-fee">${this.formatCurrency(region.deliveryFee)}</span>
                </td>
                <td>
                    <div class="drivers-count">
                        <span class="count">${region.driversCount}</span>
                        <small>مندوب</small>
                    </div>
                </td>
                <td>
                    <div class="orders-count">
                        <span class="count">${region.ordersCount}</span>
                        <small>طلب</small>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${region.isActive ? 'active' : 'inactive'}">
                        ${region.isActive ? 'نشطة' : 'غير نشطة'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-region-btn" data-region-id="${region.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn toggle-status-btn" data-region-id="${region.id}" title="تغيير الحالة">
                            <i class="fas fa-${region.isActive ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="action-btn delete-region-btn" data-region-id="${region.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination(filteredRegions.length);
    }

    getFilteredRegions() {
        return this.regions.filter(region => {
            const matchesSearch = !this.filters.search || 
                region.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                region.province.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                region.description.toLowerCase().includes(this.filters.search.toLowerCase());
            
            const matchesStatus = !this.filters.status || 
                (this.filters.status === 'active' && region.isActive) ||
                (this.filters.status === 'inactive' && !region.isActive);

            return matchesSearch && matchesStatus;
        });
    }

    renderPagination(totalItems) {
        const pagination = document.getElementById('regions-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    updateStats() {
        const totalRegions = document.getElementById('total-regions');
        const activeRegions = document.getElementById('active-regions');
        const inactiveRegions = document.getElementById('inactive-regions');
        const regionsWithDrivers = document.getElementById('regions-with-drivers');

        if (totalRegions) totalRegions.textContent = this.regions.length;
        if (activeRegions) activeRegions.textContent = this.regions.filter(r => r.isActive).length;
        if (inactiveRegions) inactiveRegions.textContent = this.regions.filter(r => !r.isActive).length;
        if (regionsWithDrivers) regionsWithDrivers.textContent = this.regions.filter(r => r.driversCount > 0).length;
    }

    filterRegions() {
        this.currentPage = 1;
        this.renderRegions();
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    showAddRegionModal() {
        if (window.ModalManager) {
            window.ModalManager.showAddRegionModal();
        } else {
            alert('سيتم إضافة نافذة إضافة منطقة جديدة قريباً');
        }
    }

    showEditRegionModal(regionId) {
        if (window.ModalManager) {
            window.ModalManager.showEditRegionModal(regionId);
        } else {
            alert(`سيتم إضافة نافذة تعديل المنطقة ${regionId} قريباً`);
        }
    }

    deleteRegion(regionId) {
        const region = this.regions.find(r => r.id == regionId);
        if (!region) return;

        if (region.ordersCount > 0) {
            alert('لا يمكن حذف منطقة تحتوي على طلبات');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف منطقة "${region.name}"؟`)) {
            this.regions = this.regions.filter(r => r.id != regionId);
            this.renderRegions();
            this.updateStats();
            alert('تم حذف المنطقة بنجاح');
        }
    }

    toggleRegionStatus(regionId) {
        const region = this.regions.find(r => r.id == regionId);
        if (region) {
            region.isActive = !region.isActive;
            this.renderRegions();
            this.updateStats();
            alert(`تم ${region.isActive ? 'تفعيل' : 'إيقاف'} المنطقة`);
        }
    }

    // Get all regions for use in other components
    getAllRegions() {
        return this.regions.filter(r => r.isActive);
    }

    // Get region by ID
    getRegionById(regionId) {
        return this.regions.find(r => r.id == regionId);
    }
}

// Initialize Regions Manager
window.RegionsManager = new RegionsManager();
