// Barcode Generator System
class BarcodeGenerator {
    constructor() {
        this.init();
    }

    init() {
        // Initialize barcode system
    }

    // Generate simple barcode representation
    generateBarcode(receiptNumber) {
        // Create a simple barcode pattern based on the receipt number
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas dimensions
        canvas.width = 200;
        canvas.height = 50;
        
        // Clear canvas
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Generate barcode pattern
        ctx.fillStyle = '#000000';
        
        // Convert receipt number to binary-like pattern
        const pattern = this.numberToPattern(receiptNumber);
        const barWidth = 2;
        let x = 10;
        
        for (let i = 0; i < pattern.length; i++) {
            if (pattern[i] === '1') {
                ctx.fillRect(x, 5, barWidth, 40);
            }
            x += barWidth + 1;
        }
        
        // Add text below barcode
        ctx.fillStyle = '#000000';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(receiptNumber, canvas.width / 2, canvas.height - 2);
        
        return canvas.toDataURL();
    }

    // Convert number to barcode pattern
    numberToPattern(number) {
        let pattern = '';
        const numStr = number.toString();
        
        // Simple encoding: each digit maps to a pattern
        const digitPatterns = {
            '0': '0001101',
            '1': '0011001',
            '2': '0010011',
            '3': '0111101',
            '4': '0100011',
            '5': '0110001',
            '6': '0101111',
            '7': '0111011',
            '8': '0110111',
            '9': '0001011'
        };
        
        // Start pattern
        pattern += '101';
        
        // Add patterns for each digit
        for (let digit of numStr) {
            pattern += digitPatterns[digit] || '0001101';
        }
        
        // End pattern
        pattern += '101';
        
        return pattern;
    }

    // Show barcode modal
    showBarcodeModal(receiptNumber, orderData = {}) {
        const barcodeImage = this.generateBarcode(receiptNumber);
        
        const content = `
            <div class="barcode-container">
                <div class="barcode-header">
                    <h3>وصل التوصيل</h3>
                    <div class="receipt-info">
                        <div class="info-row">
                            <span class="label">رقم الوصل:</span>
                            <span class="value">${receiptNumber}</span>
                        </div>
                        ${orderData.id ? `
                            <div class="info-row">
                                <span class="label">رقم الطلب:</span>
                                <span class="value">${orderData.id}</span>
                            </div>
                        ` : ''}
                        ${orderData.customer ? `
                            <div class="info-row">
                                <span class="label">العميل:</span>
                                <span class="value">${orderData.customer}</span>
                            </div>
                        ` : ''}
                        ${orderData.recipient ? `
                            <div class="info-row">
                                <span class="label">المستلم:</span>
                                <span class="value">${orderData.recipient}</span>
                            </div>
                        ` : ''}
                        ${orderData.totalAmount ? `
                            <div class="info-row">
                                <span class="label">المبلغ:</span>
                                <span class="value">${this.formatCurrency(orderData.totalAmount)}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <div class="barcode-display">
                    <img src="${barcodeImage}" alt="Barcode" class="barcode-image">
                </div>
                
                <div class="barcode-footer">
                    <p class="company-name">شركة التوصيل العراقية</p>
                    <p class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-IQ')}</p>
                </div>
            </div>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إغلاق
            </button>
            <button type="button" class="neu-btn primary" onclick="window.BarcodeGenerator.printBarcode('${receiptNumber}')">
                <i class="fas fa-print"></i>
                طباعة الوصل
            </button>
        `;

        if (window.ModalManager) {
            window.ModalManager.showModal('وصل التوصيل', content, footer);
        }
    }

    // Print barcode
    printBarcode(receiptNumber) {
        const printWindow = window.open('', '_blank');
        const barcodeImage = this.generateBarcode(receiptNumber);
        
        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>وصل التوصيل - ${receiptNumber}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        text-align: center;
                        margin: 20px;
                        background: white;
                    }
                    .receipt {
                        border: 2px solid #000;
                        padding: 20px;
                        max-width: 400px;
                        margin: 0 auto;
                    }
                    .header {
                        border-bottom: 1px solid #000;
                        padding-bottom: 10px;
                        margin-bottom: 15px;
                    }
                    .company-name {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    .receipt-number {
                        font-size: 16px;
                        margin-bottom: 10px;
                    }
                    .barcode-image {
                        margin: 15px 0;
                        max-width: 100%;
                    }
                    .footer {
                        border-top: 1px solid #000;
                        padding-top: 10px;
                        margin-top: 15px;
                        font-size: 12px;
                    }
                    @media print {
                        body { margin: 0; }
                        .receipt { border: none; }
                    }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <div class="company-name">شركة التوصيل العراقية</div>
                        <div class="receipt-number">رقم الوصل: ${receiptNumber}</div>
                    </div>
                    <img src="${barcodeImage}" alt="Barcode" class="barcode-image">
                    <div class="footer">
                        <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-IQ')}</p>
                        <p>يرجى الاحتفاظ بهذا الوصل لحين استلام الطلب</p>
                    </div>
                </div>
            </body>
            </html>
        `;
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // Auto print after loading
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }

    // Generate barcode for existing orders
    generateBarcodeForOrder(orderId) {
        const order = window.OrdersManager?.orders.find(o => o.id === orderId);
        if (!order) {
            if (window.ModalManager) {
                window.ModalManager.showNotification('الطلب غير موجود', 'error');
            }
            return;
        }

        // Generate receipt number if not exists
        if (!order.receiptNumber) {
            order.receiptNumber = this.generateReceiptNumber();
        }

        this.showBarcodeModal(order.receiptNumber, order);
    }

    generateReceiptNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');
        
        return `${year}${month}${day}${hours}${minutes}${random}`;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    // Search by receipt number
    searchByReceiptNumber(receiptNumber) {
        if (!window.OrdersManager) return null;
        
        return window.OrdersManager.orders.find(order => 
            order.receiptNumber === receiptNumber
        );
    }

    // Validate receipt number format
    validateReceiptNumber(receiptNumber) {
        // Receipt number should be 12 digits: YYMMDDHHMMRR
        const pattern = /^\d{12}$/;
        return pattern.test(receiptNumber);
    }
}

// Initialize Barcode Generator
window.BarcodeGenerator = new BarcodeGenerator();
