/* Components and Forms Fixes */
/* إصلاحات المكونات والنماذج */

/* ===== FORM COMPONENTS FIXES ===== */

/* 1. Fix Form Groups */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    text-align: right;
}

/* 2. Fix Input Groups */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.input-group i {
    position: absolute;
    right: 16px;
    color: #7f8c8d;
    font-size: 16px;
    z-index: 2;
    pointer-events: none;
}

.input-group input {
    width: 100%;
    padding: 14px 50px 14px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: #ffffff;
    color: #2c3e50;
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group input::placeholder {
    color: #95a5a6;
    font-size: 14px;
}

/* 3. Fix Select Groups */
.select-group {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.select-group select {
    width: 100%;
    padding: 14px 50px 14px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: #ffffff;
    color: #2c3e50;
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    direction: rtl;
    text-align: right;
}

.select-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-group i {
    position: absolute;
    left: 16px;
    color: #7f8c8d;
    font-size: 14px;
    pointer-events: none;
    z-index: 2;
}

/* 4. Fix Toggle Password Button */
.toggle-password {
    position: absolute;
    left: 16px;
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 3;
    font-size: 14px;
}

.toggle-password:hover {
    background: rgba(0,0,0,0.05);
    color: #2c3e50;
}

/* 5. Fix Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    font-size: 14px;
    flex-wrap: wrap;
    gap: 12px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #7f8c8d;
    gap: 8px;
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
    cursor: pointer;
}

.checkmark {
    display: none;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    text-decoration: underline;
    color: #5a6fd8;
}

/* 6. Fix Login Button */
.login-btn {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    margin-top: 8px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 7. Fix Login Header */
.login-header {
    margin-bottom: 32px;
}

.login-header .logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: #ffffff;
    font-size: 32px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.login-header .logo:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.login-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    text-align: center;
}

.login-header p {
    font-size: 16px;
    color: #7f8c8d;
    text-align: center;
    margin: 0;
}

/* 8. Fix Login Footer */
.login-footer {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e1e5e9;
    text-align: center;
}

.login-footer p {
    font-size: 12px;
    color: #95a5a6;
    margin: 0;
}

/* ===== BUTTON COMPONENTS ===== */

/* 9. Fix General Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    white-space: nowrap;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.btn.secondary {
    background: #ffffff;
    color: #2c3e50;
    border: 1px solid #e1e5e9;
}

.btn.success {
    background: #27ae60;
    color: #ffffff;
}

.btn.warning {
    background: #f39c12;
    color: #ffffff;
}

.btn.danger {
    background: #e74c3c;
    color: #ffffff;
}

.btn.info {
    background: #3498db;
    color: #ffffff;
}

.btn.sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn.lg {
    padding: 16px 28px;
    font-size: 16px;
}

/* ===== CARD COMPONENTS ===== */

/* 10. Fix Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #f1f3f4;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card.clickable:hover {
    border-color: #667eea;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.stat-icon.orders {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.delivered {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.stat-icon.pending {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-icon.postponed {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.stat-icon.returns {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.stat-content p {
    font-size: 16px;
    color: #7f8c8d;
    margin: 0 0 12px 0;
    font-weight: 500;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

.stat-change.warning {
    color: #f39c12;
}

/* ===== TABLE COMPONENTS ===== */

/* 11. Fix Tables */
.table-container {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 1px solid #f1f3f4;
    margin-bottom: 24px;
}

.table-header {
    padding: 24px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.table-header h3 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 16px;
    text-align: right;
    border-bottom: 1px solid #f1f3f4;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    white-space: nowrap;
}

.data-table td {
    color: #7f8c8d;
    font-size: 14px;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* ===== MODAL COMPONENTS ===== */

/* 12. Fix Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 20px;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 24px 24px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 18px;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 0 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* ===== RESPONSIVE FIXES ===== */

/* 13. Mobile Responsive */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-content h3 {
        font-size: 28px;
    }
    
    .table-header {
        padding: 16px;
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .data-table th,
    .data-table td {
        padding: 12px 8px;
        font-size: 12px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .modal-content {
        margin: 10px;
        max-width: none;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 24px 20px;
    }
    
    .login-header .logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .login-header h1 {
        font-size: 24px;
    }
    
    .input-group input,
    .select-group select {
        padding: 12px 40px 12px 12px;
        font-size: 14px;
    }
    
    .login-btn {
        padding: 14px 20px;
        font-size: 14px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 12px;
    }
    
    .stat-card {
        padding: 16px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .stat-content h3 {
        font-size: 24px;
    }
}
