<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة التوصيل العراقية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            direction: rtl;
            overflow: hidden;
        }

        .splash-container {
            text-align: center;
            color: white;
            animation: fadeIn 1s ease-in-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #667eea;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .app-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .loading-container {
            margin-top: 20px;
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            margin: 0 auto 10px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: white;
            border-radius: 2px;
            animation: loading 2s ease-in-out;
        }

        .loading-text {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .version {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            opacity: 0.7;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
            }
            100% {
                width: 100%;
            }
        }

        .features {
            margin-top: 20px;
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .feature-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .feature-icon {
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">
            🚚
        </div>
        
        <h1 class="app-title">نظام إدارة شركة التوصيل العراقية</h1>
        <p class="app-subtitle">Iraqi Delivery Management System</p>
        
        <div class="features">
            <div class="feature-item">
                <span class="feature-icon"></span>
                <span>إدارة الطلبات والمندوبين</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon"></span>
                <span>تتبع الطلبات والتقارير</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon"></span>
                <span>النظام المالي والفواتير</span>
            </div>
        </div>
        
        <div class="loading-container">
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <p class="loading-text">جاري تحميل النظام...</p>
        </div>
    </div>
    
    <div class="version">
        الإصدار 1.0.0 - Desktop Edition
    </div>

    <script>
        // تحديث نص التحميل
        const loadingTexts = [
            'جاري تحميل النظام...',
            'جاري تحضير البيانات...',
            'جاري تهيئة الواجهة...',
            'تم التحميل بنجاح!'
        ];

        let currentTextIndex = 0;
        const loadingTextElement = document.querySelector('.loading-text');

        const updateLoadingText = () => {
            if (currentTextIndex < loadingTexts.length - 1) {
                currentTextIndex++;
                loadingTextElement.textContent = loadingTexts[currentTextIndex];
                setTimeout(updateLoadingText, 500);
            }
        };

        setTimeout(updateLoadingText, 500);

        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', () => {
            const features = document.querySelectorAll('.feature-item');
            features.forEach((feature, index) => {
                setTimeout(() => {
                    feature.style.animation = 'fadeIn 0.5s ease-in-out';
                }, 300 + (index * 200));
            });
        });
    </script>
</body>
</html>
