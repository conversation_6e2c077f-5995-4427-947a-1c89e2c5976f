// Layout and Navigation Fixes
// إصلاحات التخطيط والتنقل

class LayoutManager {
    constructor() {
        this.sidebar = null;
        this.mainContent = null;
        this.mobileMenuToggle = null;
        this.sidebarToggle = null;
        this.userMenuToggle = null;
        this.userMenuDropdown = null;
        this.isCollapsed = false;
        this.isMobileOpen = false;
        
        this.init();
    }

    init() {
        this.setupElements();
        this.setupEventListeners();
        this.setupResponsive();
        this.fixInitialLayout();
        this.setupKeyboardNavigation();
        this.setupAccessibility();
    }

    setupElements() {
        this.sidebar = document.querySelector('.sidebar');
        this.mainContent = document.querySelector('.main-content');
        this.mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        this.sidebarToggle = document.querySelector('.sidebar-toggle');
        this.userMenuToggle = document.querySelector('.user-menu-toggle');
        this.userMenuDropdown = document.querySelector('.user-menu-dropdown');
        
        // Add missing elements if not found
        if (!this.sidebar) {
            console.warn('Sidebar element not found');
            return;
        }
        
        if (!this.mainContent) {
            console.warn('Main content element not found');
            return;
        }
    }

    setupEventListeners() {
        // Mobile menu toggle
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMobileMenu();
            });
        }

        // Sidebar toggle
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            });
        }

        // User menu toggle
        if (this.userMenuToggle) {
            this.userMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleUserMenu();
            });
        }

        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                this.closeUserMenu();
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                !e.target.closest('.sidebar') && 
                !e.target.closest('.mobile-menu-toggle') &&
                this.isMobileOpen) {
                this.closeMobileMenu();
            }
        });

        // Navigation links
        this.setupNavigationLinks();

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Escape key to close menus
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllMenus();
            }
        });
    }

    setupNavigationLinks() {
        const navLinks = document.querySelectorAll('.nav-link[data-page]');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all nav items
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // Add active class to clicked item
                const navItem = link.closest('.nav-item');
                if (navItem) {
                    navItem.classList.add('active');
                }
                
                // Update page title
                const pageTitle = link.querySelector('span')?.textContent || 'لوحة التحكم';
                this.updatePageTitle(pageTitle);
                
                // Close mobile menu if open
                if (window.innerWidth <= 768) {
                    this.closeMobileMenu();
                }
                
                // Trigger page change event
                const pageName = link.getAttribute('data-page');
                this.triggerPageChange(pageName);
            });
        });
    }

    setupResponsive() {
        this.handleResize();
    }

    fixInitialLayout() {
        // Ensure proper initial state
        if (this.sidebar) {
            this.sidebar.classList.remove('mobile-open');
            
            if (window.innerWidth <= 768) {
                this.sidebar.classList.add('mobile');
            } else {
                this.sidebar.classList.remove('mobile');
            }
        }

        // Fix dashboard visibility
        const dashboard = document.getElementById('dashboard');
        const loginPage = document.getElementById('login-page');
        
        if (dashboard && loginPage) {
            // Check if user is logged in (you can modify this logic)
            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
            
            if (isLoggedIn) {
                loginPage.classList.add('hidden');
                dashboard.classList.remove('hidden');
            } else {
                dashboard.classList.add('hidden');
                loginPage.classList.remove('hidden');
            }
        }
    }

    setupKeyboardNavigation() {
        // Tab navigation for sidebar
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach((link, index) => {
            link.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const nextLink = navLinks[index + 1];
                    if (nextLink) nextLink.focus();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prevLink = navLinks[index - 1];
                    if (prevLink) prevLink.focus();
                } else if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    link.click();
                }
            });
        });
    }

    setupAccessibility() {
        // Add ARIA attributes
        if (this.sidebar) {
            this.sidebar.setAttribute('role', 'navigation');
            this.sidebar.setAttribute('aria-label', 'القائمة الرئيسية');
        }

        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.setAttribute('aria-label', 'فتح/إغلاق القائمة');
            this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }

        if (this.userMenuToggle) {
            this.userMenuToggle.setAttribute('aria-label', 'قائمة المستخدم');
            this.userMenuToggle.setAttribute('aria-expanded', 'false');
        }

        // Add skip link
        this.addSkipLink();
    }

    addSkipLink() {
        const skipLink = document.createElement('a');
        skipLink.href = '#page-content';
        skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
        skipLink.className = 'skip-link';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            right: 6px;
            background: #667eea;
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 10001;
            transition: top 0.3s ease;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });

        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    toggleMobileMenu() {
        if (!this.sidebar) return;
        
        this.isMobileOpen = !this.isMobileOpen;
        
        if (this.isMobileOpen) {
            this.sidebar.classList.add('mobile-open');
            document.body.style.overflow = 'hidden';
        } else {
            this.sidebar.classList.remove('mobile-open');
            document.body.style.overflow = '';
        }
        
        // Update ARIA
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.setAttribute('aria-expanded', this.isMobileOpen.toString());
        }
    }

    closeMobileMenu() {
        if (!this.sidebar) return;
        
        this.isMobileOpen = false;
        this.sidebar.classList.remove('mobile-open');
        document.body.style.overflow = '';
        
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }
    }

    toggleSidebar() {
        if (!this.sidebar || !this.mainContent) return;
        
        this.isCollapsed = !this.isCollapsed;
        
        if (this.isCollapsed) {
            this.sidebar.classList.add('collapsed');
        } else {
            this.sidebar.classList.remove('collapsed');
        }
    }

    toggleUserMenu() {
        if (!this.userMenuDropdown) return;
        
        const isOpen = this.userMenuDropdown.classList.contains('show');
        
        if (isOpen) {
            this.closeUserMenu();
        } else {
            this.openUserMenu();
        }
    }

    openUserMenu() {
        if (!this.userMenuDropdown) return;
        
        this.userMenuDropdown.classList.add('show');
        
        if (this.userMenuToggle) {
            this.userMenuToggle.setAttribute('aria-expanded', 'true');
        }
    }

    closeUserMenu() {
        if (!this.userMenuDropdown) return;
        
        this.userMenuDropdown.classList.remove('show');
        
        if (this.userMenuToggle) {
            this.userMenuToggle.setAttribute('aria-expanded', 'false');
        }
    }

    closeAllMenus() {
        this.closeUserMenu();
        if (window.innerWidth <= 768) {
            this.closeMobileMenu();
        }
    }

    handleResize() {
        const width = window.innerWidth;
        
        if (!this.sidebar) return;
        
        if (width <= 768) {
            // Mobile mode
            this.sidebar.classList.add('mobile');
            this.sidebar.classList.remove('collapsed');
            this.isCollapsed = false;
            
            if (!this.isMobileOpen) {
                this.sidebar.classList.remove('mobile-open');
            }
        } else {
            // Desktop mode
            this.sidebar.classList.remove('mobile', 'mobile-open');
            this.isMobileOpen = false;
            document.body.style.overflow = '';
            
            if (this.mobileMenuToggle) {
                this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        }
    }

    updatePageTitle(title) {
        const pageTitle = document.getElementById('page-title');
        if (pageTitle) {
            pageTitle.textContent = title;
        }
        
        // Update document title
        document.title = `${title} - نظام إدارة شركة التوصيل العراقية`;
    }

    triggerPageChange(pageName) {
        // Dispatch custom event for page change
        const event = new CustomEvent('pageChange', {
            detail: { page: pageName }
        });
        document.dispatchEvent(event);
        
        // Call app navigation if available
        if (window.app && window.app.navigateTo) {
            window.app.navigateTo(pageName);
        }
    }

    // Public methods for external use
    showPage(pageName) {
        const navLink = document.querySelector(`[data-page="${pageName}"]`);
        if (navLink) {
            navLink.click();
        }
    }

    setActiveNavItem(pageName) {
        // Remove active from all
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active to specific page
        const navLink = document.querySelector(`[data-page="${pageName}"]`);
        if (navLink) {
            const navItem = navLink.closest('.nav-item');
            if (navItem) {
                navItem.classList.add('active');
            }
        }
    }

    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count.toString();
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    showLoadingState() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
            loadingScreen.style.opacity = '1';
        }
    }

    hideLoadingState() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    showLoginPage() {
        const loginPage = document.getElementById('login-page');
        const dashboard = document.getElementById('dashboard');
        
        if (loginPage) loginPage.classList.remove('hidden');
        if (dashboard) dashboard.classList.add('hidden');
    }

    showDashboard() {
        const loginPage = document.getElementById('login-page');
        const dashboard = document.getElementById('dashboard');
        
        if (loginPage) loginPage.classList.add('hidden');
        if (dashboard) dashboard.classList.remove('hidden');
    }
}

// Form Fixes
class FormManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupPasswordToggle();
        this.setupFormValidation();
        this.setupLoginForm();
    }

    setupPasswordToggle() {
        const toggleButtons = document.querySelectorAll('.toggle-password');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                const input = button.parentElement.querySelector('input[type="password"], input[type="text"]');
                const icon = button.querySelector('i');
                
                if (input && icon) {
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                        button.setAttribute('aria-label', 'إخفاء كلمة المرور');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                        button.setAttribute('aria-label', 'إظهار كلمة المرور');
                    }
                }
            });
        });
    }

    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                
                form.classList.add('was-validated');
            });
            
            // Real-time validation
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('is-invalid')) {
                        this.validateField(input);
                    }
                });
            });
        });
    }

    setupLoginForm() {
        const loginForm = document.getElementById('login-form');
        
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(loginForm);
            });
        }
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            this.removeErrorMessage(field);
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            this.showErrorMessage(field);
        }
        
        return isValid;
    }

    showErrorMessage(field) {
        this.removeErrorMessage(field);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = field.validationMessage || 'هذا الحقل مطلوب';
        
        field.parentNode.appendChild(errorDiv);
    }

    removeErrorMessage(field) {
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    handleLogin(form) {
        const formData = new FormData(form);
        const username = formData.get('username');
        const password = formData.get('password');
        const userType = formData.get('user_type');
        
        // Simple validation (replace with real authentication)
        if (username && password && userType) {
            // Show loading
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
            submitBtn.disabled = true;
            
            // Simulate login process
            setTimeout(() => {
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    username,
                    userType,
                    loginTime: new Date().toISOString()
                }));
                
                // Hide login page and show dashboard
                if (window.layoutManager) {
                    window.layoutManager.showDashboard();
                }
                
                // Reset form
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                form.reset();
                form.classList.remove('was-validated');
                
                // Initialize app if available
                if (window.app && window.app.init) {
                    window.app.init();
                }
            }, 1500);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.layoutManager = new LayoutManager();
    window.formManager = new FormManager();
    
    // Hide loading screen after initialization
    setTimeout(() => {
        if (window.layoutManager) {
            window.layoutManager.hideLoadingState();
        }
    }, 1000);
});

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.LayoutManager = LayoutManager;
    window.FormManager = FormManager;
}
