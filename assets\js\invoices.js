// Comprehensive Invoice Management System
class InvoiceManager {
    constructor() {
        this.invoices = [];
        this.customers = [];
        this.currentInvoice = null;
        this.invoiceCounter = 1;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.create-invoice-btn') || e.target.closest('.create-invoice-btn')) {
                e.preventDefault();
                this.showCreateInvoiceModal();
            }
            if (e.target.matches('.view-invoice-btn') || e.target.closest('.view-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.view-invoice-btn').getAttribute('data-invoice-id');
                this.viewInvoice(invoiceId);
            }
            if (e.target.matches('.print-invoice-btn') || e.target.closest('.print-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.print-invoice-btn').getAttribute('data-invoice-id');
                this.printInvoice(invoiceId);
            }
            if (e.target.matches('.send-invoice-btn') || e.target.closest('.send-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.send-invoice-btn').getAttribute('data-invoice-id');
                this.sendInvoice(invoiceId);
            }
            if (e.target.matches('.mark-paid-btn') || e.target.closest('.mark-paid-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.mark-paid-btn').getAttribute('data-invoice-id');
                this.markAsPaid(invoiceId);
            }
            if (e.target.matches('.duplicate-invoice-btn') || e.target.closest('.duplicate-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.duplicate-invoice-btn').getAttribute('data-invoice-id');
                this.duplicateInvoice(invoiceId);
            }
        });

        // Filter changes
        document.addEventListener('change', (e) => {
            if (e.target.id === 'invoice-status-filter' || e.target.id === 'invoice-customer-filter') {
                this.filterInvoices();
            }
        });

        // Search
        document.addEventListener('input', (e) => {
            if (e.target.id === 'invoice-search') {
                this.searchInvoices(e.target.value);
            }
        });
    }

    loadContent() {
        const content = `
            <div class="page-header">
                <h2><i class="fas fa-file-invoice"></i> إدارة الفواتير</h2>
                <p>إنشاء وإدارة فواتير العملاء</p>
            </div>

            <div class="invoices-container">
                <div class="invoices-header">
                    <div class="invoices-actions">
                        <button class="neu-btn primary create-invoice-btn">
                            <i class="fas fa-plus"></i>
                            إنشاء فاتورة جديدة
                        </button>
                        <button class="neu-btn secondary export-invoices-btn">
                            <i class="fas fa-file-export"></i>
                            تصدير الفواتير
                        </button>
                        <button class="neu-btn info bulk-send-btn">
                            <i class="fas fa-paper-plane"></i>
                            إرسال مجمع
                        </button>
                    </div>
                </div>

                <div class="invoices-filters">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label>البحث:</label>
                            <input type="text" id="invoice-search" class="neu-input" placeholder="رقم الفاتورة أو اسم العميل">
                        </div>
                        <div class="filter-group">
                            <label>الحالة:</label>
                            <select id="invoice-status-filter" class="neu-select">
                                <option value="">جميع الحالات</option>
                                <option value="draft">مسودة</option>
                                <option value="sent">مرسلة</option>
                                <option value="paid">مدفوعة</option>
                                <option value="overdue">متأخرة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>العميل:</label>
                            <select id="invoice-customer-filter" class="neu-select">
                                <option value="">جميع العملاء</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>من تاريخ:</label>
                            <input type="date" id="invoice-date-from" class="neu-input">
                        </div>
                        <div class="filter-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" id="invoice-date-to" class="neu-input">
                        </div>
                    </div>
                </div>

                <div class="invoices-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-invoices">0</h3>
                            <p>إجمالي الفواتير</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-amount">0 د.ع</h3>
                            <p>إجمالي المبلغ</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="paid-invoices">0</h3>
                            <p>فواتير مدفوعة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="overdue-invoices">0</h3>
                            <p>فواتير متأخرة</p>
                        </div>
                    </div>
                </div>

                <div class="invoices-table-container">
                    <table class="invoices-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الإنشاء</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoices-table-body">
                            <!-- Invoices will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        document.getElementById('main-content').innerHTML = content;
        this.loadCustomers();
        this.renderInvoices();
        this.updateStats();
    }

    loadSampleData() {
        this.customers = [
            { id: 1, name: 'أحمد محمد علي', phone: '+964770123456', address: 'بغداد - الكرخ' },
            { id: 2, name: 'فاطمة حسن', phone: '+964771234567', address: 'بغداد - الرصافة' },
            { id: 3, name: 'محمد عبدالله', phone: '+964772345678', address: 'بغداد - الصدر' },
            { id: 4, name: 'زينب أحمد', phone: '+964773456789', address: 'بغداد - الكاظمية' },
            { id: 5, name: 'علي حسين', phone: '+964774567890', address: 'بغداد - الأعظمية' }
        ];

        this.invoices = [
            {
                id: 'INV-2024-001',
                customerId: 1,
                customerName: 'أحمد محمد علي',
                createdDate: '2024-12-20',
                dueDate: '2024-12-30',
                items: [
                    { description: 'توصيل طلبات شهر ديسمبر', quantity: 15, unitPrice: 5000, total: 75000 }
                ],
                subtotal: 75000,
                tax: 0,
                total: 75000,
                status: 'paid',
                notes: 'تم الدفع نقداً'
            },
            {
                id: 'INV-2024-002',
                customerId: 2,
                customerName: 'فاطمة حسن',
                createdDate: '2024-12-22',
                dueDate: '2025-01-05',
                items: [
                    { description: 'توصيل طلبات متنوعة', quantity: 8, unitPrice: 6000, total: 48000 }
                ],
                subtotal: 48000,
                tax: 0,
                total: 48000,
                status: 'sent',
                notes: 'في انتظار الدفع'
            },
            {
                id: 'INV-2024-003',
                customerId: 3,
                customerName: 'محمد عبدالله',
                createdDate: '2024-12-15',
                dueDate: '2024-12-25',
                items: [
                    { description: 'خدمات توصيل سريع', quantity: 12, unitPrice: 7000, total: 84000 }
                ],
                subtotal: 84000,
                tax: 0,
                total: 84000,
                status: 'overdue',
                notes: 'تأخر في الدفع'
            }
        ];
    }

    loadCustomers() {
        const customerFilter = document.getElementById('invoice-customer-filter');
        if (customerFilter) {
            customerFilter.innerHTML = '<option value="">جميع العملاء</option>';
            this.customers.forEach(customer => {
                customerFilter.innerHTML += `<option value="${customer.id}">${customer.name}</option>`;
            });
        }
    }

    renderInvoices() {
        const tbody = document.getElementById('invoices-table-body');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        this.invoices.forEach(invoice => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${invoice.id}</td>
                <td>${invoice.customerName}</td>
                <td>${this.formatDate(invoice.createdDate)}</td>
                <td>${this.formatDate(invoice.dueDate)}</td>
                <td>${this.formatCurrency(invoice.total)}</td>
                <td><span class="status-badge ${invoice.status}">${this.getStatusText(invoice.status)}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-invoice-btn" data-invoice-id="${invoice.id}" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn print-invoice-btn" data-invoice-id="${invoice.id}" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn send-invoice-btn" data-invoice-id="${invoice.id}" title="إرسال">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                        ${invoice.status !== 'paid' ? `
                        <button class="action-btn mark-paid-btn" data-invoice-id="${invoice.id}" title="تحديد كمدفوع">
                            <i class="fas fa-check"></i>
                        </button>
                        ` : ''}
                        <button class="action-btn duplicate-invoice-btn" data-invoice-id="${invoice.id}" title="نسخ">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStats() {
        const totalInvoices = this.invoices.length;
        const totalAmount = this.invoices.reduce((sum, inv) => sum + inv.total, 0);
        const paidInvoices = this.invoices.filter(inv => inv.status === 'paid').length;
        const overdueInvoices = this.invoices.filter(inv => inv.status === 'overdue').length;

        document.getElementById('total-invoices').textContent = this.formatNumber(totalInvoices);
        document.getElementById('total-amount').textContent = this.formatCurrency(totalAmount);
        document.getElementById('paid-invoices').textContent = this.formatNumber(paidInvoices);
        document.getElementById('overdue-invoices').textContent = this.formatNumber(overdueInvoices);
    }

    showCreateInvoiceModal() {
        if (window.ModalManager) {
            window.ModalManager.showCreateInvoiceModal();
        } else {
            alert('سيتم إضافة نافذة إنشاء الفاتورة قريباً');
        }
    }

    viewInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (invoice) {
            this.displayInvoiceDetails(invoice);
        }
    }

    displayInvoiceDetails(invoice) {
        const detailsWindow = window.open('', '_blank', 'width=800,height=600');
        detailsWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.id}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                    .invoice-header { text-align: center; margin-bottom: 30px; }
                    .invoice-details { margin-bottom: 20px; }
                    .invoice-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                    .invoice-table th { background-color: #f5f5f5; }
                    .total-section { text-align: left; margin-top: 20px; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>شركة التوصيل العراقية</h1>
                    <h2>فاتورة رقم: ${invoice.id}</h2>
                </div>

                <div class="invoice-details">
                    <p><strong>العميل:</strong> ${invoice.customerName}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${this.formatDate(invoice.createdDate)}</p>
                    <p><strong>تاريخ الاستحقاق:</strong> ${this.formatDate(invoice.dueDate)}</p>
                    <p><strong>الحالة:</strong> ${this.getStatusText(invoice.status)}</p>
                </div>

                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>${item.description}</td>
                                <td>${item.quantity}</td>
                                <td>${this.formatCurrency(item.unitPrice)}</td>
                                <td>${this.formatCurrency(item.total)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="total-section">
                    <p><strong>المجموع الفرعي: ${this.formatCurrency(invoice.subtotal)}</strong></p>
                    <p><strong>الضريبة: ${this.formatCurrency(invoice.tax)}</strong></p>
                    <p><strong>المجموع الكلي: ${this.formatCurrency(invoice.total)}</strong></p>
                </div>

                ${invoice.notes ? `<div class="notes"><p><strong>ملاحظات:</strong> ${invoice.notes}</p></div>` : ''}

                <div class="no-print" style="margin-top: 30px; text-align: center;">
                    <button onclick="window.print()">طباعة</button>
                    <button onclick="window.close()">إغلاق</button>
                </div>
            </body>
            </html>
        `);
    }

    printInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (invoice) {
            this.displayInvoiceDetails(invoice);
        }
    }

    sendInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (invoice) {
            // Simulate sending invoice
            invoice.status = 'sent';
            this.renderInvoices();
            this.updateStats();
            this.showNotification(`تم إرسال الفاتورة ${invoiceId} بنجاح`, 'success');
        }
    }

    markAsPaid(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (invoice) {
            invoice.status = 'paid';
            invoice.paidDate = new Date().toISOString().split('T')[0];
            this.renderInvoices();
            this.updateStats();
            this.showNotification(`تم تحديد الفاتورة ${invoiceId} كمدفوعة`, 'success');
        }
    }

    duplicateInvoice(invoiceId) {
        const invoice = this.invoices.find(inv => inv.id === invoiceId);
        if (invoice) {
            const newInvoice = {
                ...invoice,
                id: `INV-2024-${String(this.invoiceCounter++).padStart(3, '0')}`,
                createdDate: new Date().toISOString().split('T')[0],
                status: 'draft'
            };
            this.invoices.push(newInvoice);
            this.renderInvoices();
            this.updateStats();
            this.showNotification(`تم نسخ الفاتورة بالرقم ${newInvoice.id}`, 'success');
        }
    }

    filterInvoices() {
        // Implementation for filtering invoices
        this.renderInvoices();
    }

    searchInvoices(query) {
        // Implementation for searching invoices
        this.renderInvoices();
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-EG');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US').format(amount) + ' د.ع';
    }

    formatNumber(number) {
        return new Intl.NumberFormat('en-US').format(number);
    }

    getStatusText(status) {
        const statusMap = {
            'draft': 'مسودة',
            'sent': 'مرسلة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة',
            'cancelled': 'ملغية'
        };
        return statusMap[status] || status;
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Invoice Manager
window.InvoiceManager = new InvoiceManager();
