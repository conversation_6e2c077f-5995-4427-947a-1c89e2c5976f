// Receipt API Client for Enhanced Receipt and Barcode System
class ReceiptAPIClient {
    constructor() {
        this.baseURL = 'api/receipt-api.php';
        this.timeout = 10000; // 10 seconds
        this.retryAttempts = 3;
        this.init();
    }

    init() {
        // Initialize API client
        this.setupInterceptors();
    }

    setupInterceptors() {
        // Add request/response interceptors if needed
        // This can be extended for authentication, logging, etc.
    }

    // Generic API request method
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.timeout
        };

        const requestOptions = { ...defaultOptions, ...options };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);

            const response = await fetch(url, {
                ...requestOptions,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // Generate receipt number
    async generateReceiptNumber(options = {}) {
        const endpoint = '?action=generate';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({
                format: options.format || 'IQ-{YEAR}-{SEQUENCE}',
                manual: options.manual || false,
                customNumber: options.customNumber || '',
                orderId: options.orderId || null,
                createdBy: options.createdBy || 'system'
            })
        };

        try {
            const response = await this.makeRequest(endpoint, requestOptions);
            if (response.success) {
                // Update local storage
                this.updateLocalStorage(response.data);
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to generate receipt number');
            }
        } catch (error) {
            console.error('Generate receipt error:', error);
            throw error;
        }
    }

    // Validate receipt number
    async validateReceiptNumber(receiptNumber) {
        const endpoint = `?action=validate&receipt_number=${encodeURIComponent(receiptNumber)}`;

        try {
            const response = await this.makeRequest(endpoint);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Validation failed');
            }
        } catch (error) {
            console.error('Validate receipt error:', error);
            throw error;
        }
    }

    // Search receipt numbers
    async searchReceiptNumbers(query, options = {}) {
        const params = new URLSearchParams({
            action: 'search',
            q: query,
            ...options
        });
        const endpoint = `?${params.toString()}`;

        try {
            const response = await this.makeRequest(endpoint);
            if (response.success) {
                return response.data.results;
            } else {
                throw new Error(response.error || 'Search failed');
            }
        } catch (error) {
            console.error('Search receipt error:', error);
            throw error;
        }
    }

    // Get receipt numbers list
    async getReceiptNumbers(options = {}) {
        const params = new URLSearchParams({
            action: 'list',
            page: options.page || 1,
            limit: options.limit || 20,
            status: options.status || '',
            year: options.year || '',
            search: options.search || ''
        });
        const endpoint = `?${params.toString()}`;

        try {
            const response = await this.makeRequest(endpoint);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to fetch receipt numbers');
            }
        } catch (error) {
            console.error('Get receipts error:', error);
            throw error;
        }
    }

    // Get statistics
    async getStatistics(year = null) {
        const params = new URLSearchParams({
            action: 'stats'
        });
        
        if (year) {
            params.append('year', year);
        }
        
        const endpoint = `?${params.toString()}`;

        try {
            const response = await this.makeRequest(endpoint);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to fetch statistics');
            }
        } catch (error) {
            console.error('Get statistics error:', error);
            throw error;
        }
    }

    // Update receipt number
    async updateReceiptNumber(receiptNumber, updates) {
        const requestOptions = {
            method: 'PUT',
            body: JSON.stringify({
                receipt_number: receiptNumber,
                ...updates
            })
        };

        try {
            const response = await this.makeRequest('', requestOptions);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to update receipt number');
            }
        } catch (error) {
            console.error('Update receipt error:', error);
            throw error;
        }
    }

    // Archive receipt number
    async archiveReceiptNumber(receiptNumber) {
        const endpoint = `?receipt_number=${encodeURIComponent(receiptNumber)}`;
        const requestOptions = {
            method: 'DELETE'
        };

        try {
            const response = await this.makeRequest(endpoint, requestOptions);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to archive receipt number');
            }
        } catch (error) {
            console.error('Archive receipt error:', error);
            throw error;
        }
    }

    // Batch generate receipt numbers
    async batchGenerateReceiptNumbers(count, options = {}) {
        const endpoint = '?action=batch';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({
                count: count,
                format: options.format || 'IQ-{YEAR}-{SEQUENCE}',
                createdBy: options.createdBy || 'system'
            })
        };

        try {
            const response = await this.makeRequest(endpoint, requestOptions);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to batch generate receipt numbers');
            }
        } catch (error) {
            console.error('Batch generate error:', error);
            throw error;
        }
    }

    // Export receipt numbers
    async exportReceiptNumbers(format = 'csv', options = {}) {
        const params = new URLSearchParams({
            action: 'export',
            format: format,
            ...options
        });
        const endpoint = `?${params.toString()}`;

        try {
            const response = await this.makeRequest(endpoint);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to export receipt numbers');
            }
        } catch (error) {
            console.error('Export error:', error);
            throw error;
        }
    }

    // Import receipt numbers
    async importReceiptNumbers(data, options = {}) {
        const endpoint = '?action=import';
        const requestOptions = {
            method: 'POST',
            body: JSON.stringify({
                data: data,
                format: options.format || 'csv',
                overwrite: options.overwrite || false,
                createdBy: options.createdBy || 'system'
            })
        };

        try {
            const response = await this.makeRequest(endpoint, requestOptions);
            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to import receipt numbers');
            }
        } catch (error) {
            console.error('Import error:', error);
            throw error;
        }
    }

    // Sync with local storage
    updateLocalStorage(receiptData) {
        try {
            const existingReceipts = JSON.parse(localStorage.getItem('receiptNumbers') || '[]');
            
            if (Array.isArray(receiptData)) {
                // Multiple receipts
                receiptData.forEach(receipt => {
                    if (!existingReceipts.includes(receipt.receipt_number)) {
                        existingReceipts.push(receipt.receipt_number);
                    }
                });
            } else {
                // Single receipt
                if (!existingReceipts.includes(receiptData.receipt_number)) {
                    existingReceipts.push(receiptData.receipt_number);
                }
            }
            
            localStorage.setItem('receiptNumbers', JSON.stringify(existingReceipts));
        } catch (error) {
            console.error('Local storage update error:', error);
        }
    }

    // Get cached receipt numbers from local storage
    getCachedReceiptNumbers() {
        try {
            return JSON.parse(localStorage.getItem('receiptNumbers') || '[]');
        } catch (error) {
            console.error('Local storage read error:', error);
            return [];
        }
    }

    // Clear local cache
    clearCache() {
        try {
            localStorage.removeItem('receiptNumbers');
            localStorage.removeItem('receiptStatistics');
        } catch (error) {
            console.error('Cache clear error:', error);
        }
    }

    // Retry mechanism for failed requests
    async retryRequest(requestFunction, maxRetries = this.retryAttempts) {
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await requestFunction();
            } catch (error) {
                lastError = error;
                
                if (attempt < maxRetries) {
                    // Wait before retry (exponential backoff)
                    const delay = Math.pow(2, attempt) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        throw lastError;
    }

    // Health check
    async healthCheck() {
        try {
            const response = await this.makeRequest('?action=health');
            return response.success;
        } catch (error) {
            console.error('Health check failed:', error);
            return false;
        }
    }

    // Get API status
    async getAPIStatus() {
        try {
            const isHealthy = await this.healthCheck();
            const cachedCount = this.getCachedReceiptNumbers().length;
            
            return {
                online: isHealthy,
                cached_receipts: cachedCount,
                last_check: new Date().toISOString()
            };
        } catch (error) {
            return {
                online: false,
                cached_receipts: 0,
                last_check: new Date().toISOString(),
                error: error.message
            };
        }
    }
}

// Initialize API client
window.ReceiptAPIClient = new ReceiptAPIClient();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReceiptAPIClient;
}
