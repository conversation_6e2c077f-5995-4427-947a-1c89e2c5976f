// Enhanced Receipt Number and Barcode System
class ReceiptSystem {
    constructor() {
        this.receiptNumbers = new Set(); // Store all existing receipt numbers
        this.receiptFormat = 'IQ-{YEAR}-{SEQUENCE}'; // Default format
        this.currentYear = new Date().getFullYear();
        this.sequenceLength = 6; // Number of digits in sequence
        this.lastSequence = 0;
        this.barcodeType = 'code128'; // Default barcode type
        this.init();
    }

    init() {
        this.loadExistingReceipts();
        this.setupEventListeners();
        this.initializeBarcodeLibrary();
    }

    setupEventListeners() {
        // Receipt generation mode toggle
        document.addEventListener('change', (e) => {
            if (e.target.id === 'receipt-mode-toggle') {
                this.toggleReceiptMode(e.target.value);
            }
            if (e.target.id === 'barcode-type-select') {
                this.barcodeType = e.target.value;
                this.updateBarcodePreview();
            }
        });

        // Manual receipt input
        document.addEventListener('input', (e) => {
            if (e.target.id === 'manual-receipt-input') {
                this.validateManualReceipt(e.target.value);
                this.updateBarcodePreview();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.generate-receipt-btn') || e.target.closest('.generate-receipt-btn')) {
                e.preventDefault();
                this.generateNewReceipt();
            }
            if (e.target.matches('.regenerate-barcode-btn') || e.target.closest('.regenerate-barcode-btn')) {
                e.preventDefault();
                this.regenerateBarcode();
            }
            if (e.target.matches('.print-receipt-btn') || e.target.closest('.print-receipt-btn')) {
                e.preventDefault();
                this.printReceipt();
            }
            if (e.target.matches('.edit-receipt-btn') || e.target.closest('.edit-receipt-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.edit-receipt-btn').getAttribute('data-order-id');
                this.showEditReceiptModal(orderId);
            }
            if (e.target.matches('.show-receipt-stats-btn') || e.target.closest('.show-receipt-stats-btn')) {
                e.preventDefault();
                this.showReceiptStatistics();
            }
        });
    }

    loadExistingReceipts() {
        // Load existing receipt numbers from orders
        if (window.OrdersManager && window.OrdersManager.orders) {
            window.OrdersManager.orders.forEach(order => {
                if (order.receiptNumber) {
                    this.receiptNumbers.add(order.receiptNumber);
                    // Extract sequence number to update lastSequence
                    const sequence = this.extractSequenceFromReceipt(order.receiptNumber);
                    if (sequence > this.lastSequence) {
                        this.lastSequence = sequence;
                    }
                }
            });
        }

        // Load from localStorage as backup
        const savedReceipts = localStorage.getItem('receiptNumbers');
        if (savedReceipts) {
            const receipts = JSON.parse(savedReceipts);
            receipts.forEach(receipt => this.receiptNumbers.add(receipt));
        }
    }

    generateReceiptNumber(manual = false, customNumber = '') {
        if (manual && customNumber) {
            return this.createManualReceipt(customNumber);
        } else {
            return this.createAutoReceipt();
        }
    }

    createAutoReceipt() {
        let receiptNumber;
        let attempts = 0;
        const maxAttempts = 1000;

        do {
            this.lastSequence++;
            const sequence = this.lastSequence.toString().padStart(this.sequenceLength, '0');
            receiptNumber = this.receiptFormat
                .replace('{YEAR}', this.currentYear.toString())
                .replace('{SEQUENCE}', sequence);
            
            attempts++;
            if (attempts > maxAttempts) {
                throw new Error('Unable to generate unique receipt number after maximum attempts');
            }
        } while (this.receiptNumbers.has(receiptNumber));

        return receiptNumber;
    }

    createManualReceipt(customNumber) {
        // Validate format
        if (!this.validateReceiptFormat(customNumber)) {
            throw new Error('Invalid receipt number format. Expected format: XX-YYYY-XXXXXX');
        }

        // Check for duplicates
        if (this.receiptNumbers.has(customNumber)) {
            throw new Error('Receipt number already exists');
        }

        return customNumber;
    }

    validateReceiptFormat(receiptNumber) {
        // Validate format: XX-YYYY-XXXXXX (flexible prefix)
        const pattern = /^[A-Z]{2,3}-\d{4}-\d{6}$/;
        return pattern.test(receiptNumber);
    }

    validateManualReceipt(receiptNumber) {
        const validationResult = document.getElementById('receipt-validation-result');
        if (!validationResult) return;

        if (!receiptNumber) {
            validationResult.innerHTML = '';
            return;
        }

        if (!this.validateReceiptFormat(receiptNumber)) {
            validationResult.innerHTML = '<span class="validation-error"><i class="fas fa-times"></i> تنسيق غير صحيح. المطلوب: XX-YYYY-XXXXXX</span>';
            return false;
        }

        if (this.receiptNumbers.has(receiptNumber)) {
            validationResult.innerHTML = '<span class="validation-error"><i class="fas fa-times"></i> رقم الوصل موجود مسبقاً</span>';
            return false;
        }

        validationResult.innerHTML = '<span class="validation-success"><i class="fas fa-check"></i> رقم الوصل صالح</span>';
        return true;
    }

    extractSequenceFromReceipt(receiptNumber) {
        const parts = receiptNumber.split('-');
        if (parts.length === 3) {
            return parseInt(parts[2]) || 0;
        }
        return 0;
    }

    addReceiptNumber(receiptNumber) {
        this.receiptNumbers.add(receiptNumber);
        this.saveReceiptNumbers();
        
        // Update sequence if it's higher
        const sequence = this.extractSequenceFromReceipt(receiptNumber);
        if (sequence > this.lastSequence) {
            this.lastSequence = sequence;
        }
    }

    removeReceiptNumber(receiptNumber) {
        this.receiptNumbers.delete(receiptNumber);
        this.saveReceiptNumbers();
    }

    saveReceiptNumbers() {
        localStorage.setItem('receiptNumbers', JSON.stringify([...this.receiptNumbers]));
    }

    toggleReceiptMode(mode) {
        const manualSection = document.getElementById('manual-receipt-section');
        const autoSection = document.getElementById('auto-receipt-section');
        
        if (mode === 'manual') {
            manualSection.style.display = 'block';
            autoSection.style.display = 'none';
        } else {
            manualSection.style.display = 'none';
            autoSection.style.display = 'block';
        }
    }

    generateNewReceipt() {
        try {
            const mode = document.querySelector('input[name="receipt-mode"]:checked')?.value || 'auto';
            let receiptNumber;

            if (mode === 'manual') {
                const customNumber = document.getElementById('manual-receipt-input')?.value;
                if (!customNumber) {
                    this.showNotification('يرجى إدخال رقم الوصل', 'error');
                    return;
                }
                receiptNumber = this.generateReceiptNumber(true, customNumber);
            } else {
                receiptNumber = this.generateReceiptNumber(false);
            }

            this.addReceiptNumber(receiptNumber);
            this.displayGeneratedReceipt(receiptNumber);
            this.generateBarcode(receiptNumber);
            this.showNotification('تم إنشاء رقم الوصل بنجاح: ' + receiptNumber, 'success');

        } catch (error) {
            this.showNotification(error.message, 'error');
        }
    }

    displayGeneratedReceipt(receiptNumber) {
        const displayElement = document.getElementById('generated-receipt-display');
        if (displayElement) {
            displayElement.innerHTML = `
                <div class="receipt-display">
                    <h3>رقم الوصل المُنشأ</h3>
                    <div class="receipt-number">${receiptNumber}</div>
                    <div class="receipt-actions">
                        <button class="neu-btn small regenerate-barcode-btn">
                            <i class="fas fa-qrcode"></i>
                            إعادة إنشاء الباركود
                        </button>
                        <button class="neu-btn small print-receipt-btn">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            `;
        }
    }

    initializeBarcodeLibrary() {
        // Initialize barcode generation library
        // This would typically load JsBarcode or similar library
        if (typeof JsBarcode === 'undefined') {
            // Fallback: Load library dynamically
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js';
            script.onload = () => {
                console.log('JsBarcode library loaded successfully');
            };
            document.head.appendChild(script);
        }
    }

    generateBarcode(receiptNumber, type = null) {
        const barcodeType = type || this.barcodeType;
        const canvas = document.getElementById('barcode-canvas');
        
        if (!canvas) {
            console.error('Barcode canvas not found');
            return;
        }

        try {
            if (typeof JsBarcode !== 'undefined') {
                // Clear previous barcode
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Generate new barcode
                JsBarcode(canvas, receiptNumber, {
                    format: this.getBarcodeFormat(barcodeType),
                    width: 2,
                    height: 100,
                    displayValue: true,
                    fontSize: 14,
                    textMargin: 5,
                    margin: 10
                });
            } else {
                // Fallback: Simple text-based barcode
                this.generateFallbackBarcode(canvas, receiptNumber);
            }
        } catch (error) {
            console.error('Error generating barcode:', error);
            this.generateFallbackBarcode(canvas, receiptNumber);
        }
    }

    getBarcodeFormat(type) {
        const formats = {
            'code128': 'CODE128',
            'code39': 'CODE39',
            'qr': 'QR'
        };
        return formats[type] || 'CODE128';
    }

    generateFallbackBarcode(canvas, receiptNumber) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Simple barcode representation
        ctx.fillStyle = '#000';
        ctx.font = '12px monospace';
        ctx.textAlign = 'center';
        
        // Draw bars
        const barWidth = 3;
        const barHeight = 60;
        const startX = 20;
        
        for (let i = 0; i < receiptNumber.length; i++) {
            const charCode = receiptNumber.charCodeAt(i);
            const x = startX + (i * barWidth * 2);
            
            if (charCode % 2 === 0) {
                ctx.fillRect(x, 20, barWidth, barHeight);
            }
        }
        
        // Draw text
        ctx.fillText(receiptNumber, canvas.width / 2, barHeight + 40);
    }

    updateBarcodePreview() {
        const receiptInput = document.getElementById('manual-receipt-input');
        if (receiptInput && receiptInput.value) {
            this.generateBarcode(receiptInput.value);
        }
    }

    regenerateBarcode() {
        const receiptDisplay = document.querySelector('.receipt-number');
        if (receiptDisplay) {
            const receiptNumber = receiptDisplay.textContent;
            this.generateBarcode(receiptNumber);
            this.showNotification('تم إعادة إنشاء الباركود', 'success');
        }
    }

    printReceipt() {
        const printWindow = window.open('', '_blank');
        const receiptNumber = document.querySelector('.receipt-number')?.textContent;
        const canvas = document.getElementById('barcode-canvas');
        
        if (!receiptNumber || !canvas) {
            this.showNotification('لا يوجد وصل للطباعة', 'error');
            return;
        }

        const barcodeDataURL = canvas.toDataURL();
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>طباعة الوصل - ${receiptNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                    .receipt-container { max-width: 400px; margin: 0 auto; border: 2px solid #000; padding: 20px; }
                    .company-name { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                    .receipt-number { font-size: 16px; margin: 15px 0; }
                    .barcode { margin: 20px 0; }
                    .date { font-size: 12px; color: #666; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="receipt-container">
                    <div class="company-name">شركة التوصيل العراقية</div>
                    <div class="receipt-number">رقم الوصل: ${receiptNumber}</div>
                    <div class="barcode">
                        <img src="${barcodeDataURL}" alt="Barcode" />
                    </div>
                    <div class="date">تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-IQ')}</div>
                </div>
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `);
        
        printWindow.document.close();
    }

    showEditReceiptModal(orderId) {
        // This would integrate with the modal system
        if (window.ModalManager) {
            window.ModalManager.showEditReceiptModal(orderId);
        } else {
            alert('سيتم إضافة نافذة تعديل رقم الوصل قريباً');
        }
    }

    showReceiptStatistics() {
        const stats = this.getReceiptStatistics();
        
        if (window.ModalManager) {
            window.ModalManager.showReceiptStatsModal(stats);
        } else {
            // Fallback: Simple alert
            alert(`إحصائيات أرقام الوصل:\nإجمالي الأرقام: ${stats.total}\nالسنة الحالية: ${stats.currentYear}\nآخر رقم: ${stats.lastSequence}`);
        }
    }

    getReceiptStatistics() {
        const currentYearReceipts = [...this.receiptNumbers].filter(receipt => 
            receipt.includes(`-${this.currentYear}-`)
        );

        return {
            total: this.receiptNumbers.size,
            currentYear: currentYearReceipts.length,
            lastSequence: this.lastSequence,
            format: this.receiptFormat,
            barcodeType: this.barcodeType
        };
    }

    exportReceiptNumbers() {
        const receipts = [...this.receiptNumbers].sort();
        const csvContent = 'رقم الوصل,تاريخ الإنشاء,الحالة\n' + 
            receipts.map(receipt => `${receipt},${new Date().toLocaleDateString('ar-IQ')},نشط`).join('\n');
        
        this.downloadCSV(csvContent, `receipt_numbers_${new Date().toISOString().split('T')[0]}.csv`);
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }
        
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        
        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }
        
        return text;
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Receipt System
window.ReceiptSystem = new ReceiptSystem();
