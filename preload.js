const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات التطبيق
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    getAppPath: () => ipcRenderer.invoke('get-app-path'),
    getUserDataPath: () => ipcRenderer.invoke('get-user-data-path'),

    // حوارات النظام
    showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
    showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

    // عمليات الملفات
    writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),

    // الروابط الخارجية
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // استقبال رسائل من القائمة
    onMenuAction: (callback) => {
        ipcRenderer.on('menu-new', callback);
        ipcRenderer.on('menu-open', callback);
        ipcRenderer.on('menu-save', callback);
    },

    // إزالة مستمعي الأحداث
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});

// تعريض وظائف خاصة بنظام التوصيل
contextBridge.exposeInMainWorld('deliverySystemAPI', {
    // حفظ البيانات محلياً
    saveData: async (key, data) => {
        try {
            const userDataPath = await ipcRenderer.invoke('get-user-data-path');
            const filePath = `${userDataPath}/${key}.json`;
            const result = await ipcRenderer.invoke('write-file', filePath, JSON.stringify(data, null, 2));
            return result;
        } catch (error) {
            console.error('Error saving data:', error);
            return { success: false, error: error.message };
        }
    },

    // تحميل البيانات محلياً
    loadData: async (key) => {
        try {
            const userDataPath = await ipcRenderer.invoke('get-user-data-path');
            const filePath = `${userDataPath}/${key}.json`;
            const result = await ipcRenderer.invoke('read-file', filePath);
            
            if (result.success) {
                return { success: true, data: JSON.parse(result.data) };
            } else {
                return result;
            }
        } catch (error) {
            console.error('Error loading data:', error);
            return { success: false, error: error.message };
        }
    },

    // تصدير البيانات
    exportData: async (data, filename) => {
        try {
            const saveResult = await ipcRenderer.invoke('show-save-dialog');
            
            if (!saveResult.canceled) {
                const filePath = saveResult.filePath || `${filename}.json`;
                const result = await ipcRenderer.invoke('write-file', filePath, JSON.stringify(data, null, 2));
                return result;
            }
            
            return { success: false, error: 'Export cancelled' };
        } catch (error) {
            console.error('Error exporting data:', error);
            return { success: false, error: error.message };
        }
    },

    // استيراد البيانات
    importData: async () => {
        try {
            const openResult = await ipcRenderer.invoke('show-open-dialog');
            
            if (!openResult.canceled && openResult.filePaths.length > 0) {
                const filePath = openResult.filePaths[0];
                const result = await ipcRenderer.invoke('read-file', filePath);
                
                if (result.success) {
                    return { success: true, data: JSON.parse(result.data) };
                } else {
                    return result;
                }
            }
            
            return { success: false, error: 'Import cancelled' };
        } catch (error) {
            console.error('Error importing data:', error);
            return { success: false, error: error.message };
        }
    },

    // طباعة التقارير
    printReport: (htmlContent) => {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>طباعة التقرير</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                    @media print { 
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                ${htmlContent}
                <div class="no-print" style="margin-top: 20px; text-align: center;">
                    <button onclick="window.print()">طباعة</button>
                    <button onclick="window.close()">إغلاق</button>
                </div>
            </body>
            </html>
        `);
        printWindow.document.close();
    },

    // إظهار إشعارات النظام
    showNotification: (title, body, icon) => {
        if (Notification.permission === 'granted') {
            new Notification(title, {
                body: body,
                icon: icon || 'assets/icons/icon.png'
            });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(title, {
                        body: body,
                        icon: icon || 'assets/icons/icon.png'
                    });
                }
            });
        }
    }
});

// إضافة دعم للطباعة المحسنة
contextBridge.exposeInMainWorld('printAPI', {
    // طباعة الصفحة الحالية
    printPage: () => {
        window.print();
    },

    // طباعة عنصر محدد
    printElement: (elementId) => {
        const element = document.getElementById(elementId);
        if (element) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>طباعة</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        @media print { 
                            body { margin: 0; }
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    ${element.outerHTML}
                    <div class="no-print" style="margin-top: 20px; text-align: center;">
                        <button onclick="window.print()">طباعة</button>
                        <button onclick="window.close()">إغلاق</button>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
    }
});

// دعم التخزين المحلي المحسن
contextBridge.exposeInMainWorld('storageAPI', {
    // حفظ في localStorage
    setItem: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    },

    // استرجاع من localStorage
    getItem: (key) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        } catch (error) {
            console.error('Error loading from localStorage:', error);
            return null;
        }
    },

    // حذف من localStorage
    removeItem: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    },

    // مسح جميع البيانات
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('Error clearing localStorage:', error);
            return false;
        }
    }
});

// إضافة دعم للنسخ الاحتياطية
contextBridge.exposeInMainWorld('backupAPI', {
    // إنشاء نسخة احتياطية
    createBackup: async () => {
        try {
            const allData = {
                orders: JSON.parse(localStorage.getItem('orders') || '[]'),
                drivers: JSON.parse(localStorage.getItem('drivers') || '[]'),
                customers: JSON.parse(localStorage.getItem('customers') || '[]'),
                regions: JSON.parse(localStorage.getItem('regions') || '[]'),
                settings: JSON.parse(localStorage.getItem('settings') || '{}'),
                timestamp: new Date().toISOString()
            };

            const filename = `backup_${new Date().toISOString().split('T')[0]}`;
            const result = await ipcRenderer.invoke('show-save-dialog');
            
            if (!result.canceled) {
                const filePath = result.filePath || `${filename}.json`;
                const writeResult = await ipcRenderer.invoke('write-file', filePath, JSON.stringify(allData, null, 2));
                return writeResult;
            }
            
            return { success: false, error: 'Backup cancelled' };
        } catch (error) {
            console.error('Error creating backup:', error);
            return { success: false, error: error.message };
        }
    },

    // استعادة من نسخة احتياطية
    restoreBackup: async () => {
        try {
            const openResult = await ipcRenderer.invoke('show-open-dialog');
            
            if (!openResult.canceled && openResult.filePaths.length > 0) {
                const filePath = openResult.filePaths[0];
                const readResult = await ipcRenderer.invoke('read-file', filePath);
                
                if (readResult.success) {
                    const backupData = JSON.parse(readResult.data);
                    
                    // استعادة البيانات
                    if (backupData.orders) localStorage.setItem('orders', JSON.stringify(backupData.orders));
                    if (backupData.drivers) localStorage.setItem('drivers', JSON.stringify(backupData.drivers));
                    if (backupData.customers) localStorage.setItem('customers', JSON.stringify(backupData.customers));
                    if (backupData.regions) localStorage.setItem('regions', JSON.stringify(backupData.regions));
                    if (backupData.settings) localStorage.setItem('settings', JSON.stringify(backupData.settings));
                    
                    return { success: true, timestamp: backupData.timestamp };
                } else {
                    return readResult;
                }
            }
            
            return { success: false, error: 'Restore cancelled' };
        } catch (error) {
            console.error('Error restoring backup:', error);
            return { success: false, error: error.message };
        }
    }
});

// تسجيل أن preload تم تحميله بنجاح
console.log('Preload script loaded successfully');
