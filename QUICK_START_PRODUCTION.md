# دليل البدء السريع للاستخدام التجاري
## Quick Start Guide for Production Use

دليل سريع لتشغيل نظام إدارة شركة التوصيل العراقية في بيئة الإنتاج.

---

## 🚀 **التشغيل السريع (5 دقائق)**

### **الخطوة 1: تحميل النظام**
```bash
# تحميل الملفات
git clone [repository-url]
cd Iraqi-Delivery-System

# أو تحميل ZIP وفك الضغط
unzip Iraqi-Delivery-System.zip
cd Iraqi-Delivery-System
```

### **الخطوة 2: تشغيل الخادم**
```bash
# باستخدام Python (الأسهل)
python -m http.server 8080

# أو باستخدام PHP
php -S localhost:8080

# أو باستخدام Node.js
npx http-server -p 8080
```

### **الخطوة 3: فتح النظام**
```
افتح المتصفح واذهب إلى:
http://localhost:8080
```

### **الخطوة 4: تسجيل الدخول**
```
المدير:
اسم المستخدم: admin
كلمة المرور: admin123
```

---

## ✅ **اختبار سريع للنظام**

### **1. اختبار لوحة التحكم:**
- ✅ تحقق من ظهور الإحصائيات بأرقام إنجليزية
- ✅ تأكد من عمل الإجراءات السريعة
- ✅ اختبر التحديث التلقائي

### **2. اختبار إضافة طلب:**
- ✅ اضغط على "طلب جديد"
- ✅ املأ البيانات واحفظ
- ✅ تحقق من إنشاء رقم وصل تلقائياً
- ✅ اختبر عرض الباركود

### **3. اختبار البحث:**
- ✅ ابحث برقم الطلب
- ✅ ابحث برقم الوصل
- ✅ ابحث باسم العميل

### **4. اختبار النوافذ المنبثقة:**
- ✅ إضافة مندوب
- ✅ إضافة عميل
- ✅ إنشاء فاتورة

---

## 🔧 **الإعدادات الأساسية**

### **تخصيص بيانات الشركة:**

#### **في ملف `assets/js/app.js`:**
```javascript
// تحديث اسم الشركة
const companyName = "اسم شركتك هنا";

// تحديث معلومات الاتصال
const contactInfo = {
    phone: "+964-XXX-XXXX",
    email: "<EMAIL>",
    address: "عنوان شركتك"
};
```

#### **في ملف `index.html`:**
```html
<!-- تحديث عنوان الصفحة -->
<title>نظام إدارة شركة التوصيل - اسم شركتك</title>

<!-- تحديث الشعار -->
<div class="logo">
    <h2>اسم شركتك</h2>
</div>
```

### **إضافة مناطق جديدة:**

#### **في ملف `assets/js/regions.js`:**
```javascript
// إضافة منطقة جديدة
{
    id: 7,
    name: "اسم المنطقة الجديدة",
    deliveryFee: 5000,
    isActive: true,
    driversCount: 0,
    ordersCount: 0
}
```

### **تحديث بيانات المستخدمين:**

#### **في ملف `assets/js/auth.js`:**
```javascript
// تحديث كلمات المرور
const users = [
    {
        username: 'admin',
        password: 'كلمة_مرور_قوية_جديدة',
        role: 'admin',
        name: 'مدير النظام'
    }
    // ... باقي المستخدمين
];
```

---

## 📊 **إدارة البيانات**

### **إضافة مندوبين جدد:**
1. انتقل إلى صفحة "المندوبين"
2. اضغط "إضافة مندوب"
3. املأ البيانات:
   - الاسم الكامل
   - رقم الهاتف (+964XXXXXXXXX)
   - البريد الإلكتروني
   - العنوان
   - المنطقة
   - مبلغ العمولة (بالدينار العراقي)

### **إضافة عملاء جدد:**
1. انتقل إلى صفحة "العملاء"
2. اضغط "إضافة عميل"
3. اختر نوع العميل:
   - **فردي**: للأشخاص العاديين
   - **شركة**: للشركات (مع عمولة خاصة)

### **إدارة المناطق:**
1. انتقل إلى صفحة "إدارة المناطق"
2. أضف المناطق التي تخدمها شركتك
3. حدد رسوم التوصيل لكل منطقة
4. اربط المندوبين بالمناطق

---

## 💰 **النظام المالي**

### **إنشاء فواتير للشركات:**
1. انتقل إلى "نظام الفواتير"
2. اضغط "إنشاء فاتورة"
3. اختر الشركة
4. حدد فترة الفاتورة
5. راجع المبالغ واحفظ

### **تقارير المندوبين:**
1. انتقل إلى "الإدارة المالية"
2. اضغط "تقارير المندوبين"
3. راجع المبالغ المستحقة
4. ادفع العمولات للمندوبين

### **تتبع الإيرادات:**
- لوحة التحكم تعرض الإيرادات الإجمالية
- تقارير مفصلة لكل فترة زمنية
- حساب صافي الأرباح بعد العمولات

---

## 🔒 **الأمان والحماية**

### **تغيير كلمات المرور:**
```javascript
// في assets/js/auth.js
// استخدم كلمات مرور قوية
password: 'كلمة_مرور_معقدة_123!'
```

### **نسخ احتياطية:**
```bash
# نسخ احتياطي يومي
cp -r Iraqi-Delivery-System backup_$(date +%Y%m%d)

# أو ضغط الملفات
tar -czf backup_$(date +%Y%m%d).tar.gz Iraqi-Delivery-System/
```

### **تحديث دوري:**
- راجع التحديثات شهرياً
- اعمل نسخة احتياطية قبل أي تحديث
- اختبر النظام بعد التحديث

---

## 📱 **التشغيل على الأجهزة المختلفة**

### **أجهزة سطح المكتب:**
- دقة الشاشة: 1920x1080 أو أعلى
- المتصفح: Chrome أو Firefox
- الذاكرة: 4GB RAM أو أكثر

### **الأجهزة اللوحية:**
- دقة الشاشة: 1024x768 أو أعلى
- المتصفح: Safari أو Chrome
- اتصال إنترنت مستقر

### **الهواتف الذكية:**
- دقة الشاشة: 375x667 أو أعلى
- المتصفح: Chrome أو Safari
- نظام التشغيل: Android 8+ أو iOS 13+

---

## 🆘 **حل المشاكل الشائعة**

### **المشكلة: النظام لا يحمل**
```bash
# تحقق من الخادم
python -m http.server 8080

# تحقق من المنفذ
netstat -an | grep 8080

# جرب منفذ آخر
python -m http.server 8081
```

### **المشكلة: الأرقام تظهر بالعربية**
- تحقق من تحديث ملفات JavaScript
- امسح ذاكرة التخزين المؤقت للمتصفح
- أعد تحميل الصفحة بـ Ctrl+F5

### **المشكلة: رقم الوصل لا ينشأ**
- تحقق من وجود دالة generateReceiptNumber()
- تحقق من console للأخطاء
- أعد تحميل الصفحة

### **المشكلة: الباركود لا يظهر**
- تحقق من تحميل ملف barcode-generator.js
- تحقق من وجود رقم وصل للطلب
- جرب متصفح آخر

---

## 📞 **الدعم الفني**

### **للمساعدة السريعة:**
1. راجع هذا الدليل أولاً
2. اختبر النظام باستخدام system_test.html
3. تحقق من console المتصفح للأخطاء
4. اعمل نسخة احتياطية قبل أي تغيير

### **للمشاكل المعقدة:**
- اجمع معلومات المشكلة
- اذكر نوع المتصفح والجهاز
- أرفق لقطات شاشة
- اتصل بالدعم الفني

---

## 🎯 **نصائح للاستخدام الأمثل**

### **الأداء:**
- استخدم متصفح حديث
- أغلق التبويبات غير المستخدمة
- نظف ذاكرة التخزين المؤقت دورياً

### **الإنتاجية:**
- استخدم اختصارات لوحة المفاتيح
- احفظ البيانات المهمة بانتظام
- راجع التقارير أسبوعياً

### **الأمان:**
- غير كلمات المرور دورياً
- لا تشارك بيانات الدخول
- اعمل نسخ احتياطية منتظمة

---

**© 2024 نظام إدارة شركة التوصيل العراقية - دليل البدء السريع**

**🚀 النظام جاهز للاستخدام التجاري!**
