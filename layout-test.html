<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاحات التخطيط - نظام إدارة شركة التوصيل العراقية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <link rel="stylesheet" href="assets/css/design-fixes.css">
    <link rel="stylesheet" href="assets/css/dashboard-fixes.css">
    <link rel="stylesheet" href="assets/css/layout-fixes.css">
    <link rel="stylesheet" href="assets/css/components-fixes.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .test-section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e1e5e9;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            background: #ffffff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .demo-sidebar {
            width: 280px;
            height: 200px;
            background: #ffffff;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            position: relative;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .demo-sidebar.collapsed {
            width: 80px;
        }
        .demo-sidebar.mobile {
            transform: translateX(100%);
        }
        .demo-content {
            margin-right: 300px;
            height: 200px;
            background: #f8f9fa;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }
        .demo-content.collapsed {
            margin-right: 100px;
        }
        .demo-content.mobile {
            margin-right: 0;
        }
        .responsive-demo {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 10px 0;
            background: #ffffff;
            transition: all 0.3s ease;
        }
        .responsive-demo.mobile {
            padding: 10px;
            font-size: 14px;
        }
        .responsive-demo.tablet {
            padding: 15px;
            font-size: 15px;
        }
        .responsive-demo.desktop {
            padding: 20px;
            font-size: 16px;
        }
        .control-buttons {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .layout-preview {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
            margin: 15px 0;
            background: #ffffff;
        }
        .preview-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #e1e5e9;
            font-weight: 600;
            color: #2c3e50;
        }
        .preview-body {
            padding: 15px;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-tools"></i> اختبار إصلاحات التخطيط والواجهة</h1>
            <p>فحص شامل لجميع إصلاحات التخطيط والمكونات المطبقة على النظام</p>
            <div class="control-buttons">
                <button class="btn primary" onclick="runAllTests()">
                    <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                </button>
                <button class="btn secondary" onclick="openMainSystem()">
                    <i class="fas fa-external-link-alt"></i> فتح النظام الرئيسي
                </button>
                <button class="btn success" onclick="resetTests()">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </button>
            </div>
        </div>

        <!-- Layout Structure Test -->
        <div class="test-section">
            <h3><i class="fas fa-th-large"></i> اختبار هيكل التخطيط</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>الشريط الجانبي</h4>
                    <div class="demo-sidebar" id="demo-sidebar">
                        <div style="padding: 10px; font-size: 12px; color: #7f8c8d;">
                            <i class="fas fa-bars"></i> القائمة الجانبية
                        </div>
                    </div>
                    <div class="control-buttons">
                        <button class="btn sm secondary" onclick="toggleDemoSidebar()">تبديل</button>
                        <button class="btn sm secondary" onclick="collapseDemoSidebar()">طي</button>
                    </div>
                </div>
                <div class="test-item">
                    <h4>المحتوى الرئيسي</h4>
                    <div class="demo-content" id="demo-content">
                        المحتوى الرئيسي
                    </div>
                </div>
            </div>
        </div>

        <!-- Responsive Design Test -->
        <div class="test-section">
            <h3><i class="fas fa-mobile-alt"></i> اختبار التصميم المتجاوب</h3>
            <div class="control-buttons">
                <button class="btn sm secondary" onclick="testResponsive('mobile')">هاتف (320px)</button>
                <button class="btn sm secondary" onclick="testResponsive('tablet')">تابلت (768px)</button>
                <button class="btn sm secondary" onclick="testResponsive('desktop')">سطح المكتب (1024px+)</button>
            </div>
            <div class="responsive-demo desktop" id="responsive-demo">
                <h4>عرض متجاوب</h4>
                <p>هذا المحتوى يتكيف مع أحجام الشاشات المختلفة</p>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon orders">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3>150</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon delivered">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3>120</h3>
                            <p>تم التوصيل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Components Test -->
        <div class="test-section">
            <h3><i class="fas fa-edit"></i> اختبار مكونات النماذج</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>حقول الإدخال</h4>
                    <div class="form-group">
                        <label>اسم المستخدم</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" placeholder="أدخل اسم المستخدم">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" placeholder="أدخل كلمة المرور">
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="test-item">
                    <h4>قوائم الاختيار والأزرار</h4>
                    <div class="form-group">
                        <label>نوع المستخدم</label>
                        <div class="select-group">
                            <select>
                                <option>اختر نوع المستخدم</option>
                                <option>مدير</option>
                                <option>موظف</option>
                                <option>مندوب</option>
                            </select>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="control-buttons">
                        <button class="btn primary">أساسي</button>
                        <button class="btn success">نجاح</button>
                        <button class="btn warning">تحذير</button>
                        <button class="btn danger">خطر</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Test -->
        <div class="test-section">
            <h3><i class="fas fa-compass"></i> اختبار التنقل</h3>
            <div class="layout-preview">
                <div class="preview-header">
                    <i class="fas fa-bars"></i> شريط التنقل
                </div>
                <div class="preview-body">
                    <div style="display: flex; gap: 20px; align-items: center;">
                        <div style="padding: 10px 15px; background: #667eea; color: white; border-radius: 8px;">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </div>
                        <div style="padding: 10px 15px; background: #f8f9fa; border-radius: 8px;">
                            <i class="fas fa-box"></i> الطلبات
                        </div>
                        <div style="padding: 10px 15px; background: #f8f9fa; border-radius: 8px;">
                            <i class="fas fa-users"></i> المندوبين
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check"></i> نتائج الاختبارات</h3>
            <div id="test-results">
                <p style="text-align: center; color: #7f8c8d; padding: 20px;">
                    اضغط "تشغيل جميع الاختبارات" لبدء الفحص
                </p>
            </div>
        </div>
    </div>

    <script src="assets/js/layout-fixes.js"></script>
    <script>
        let testResults = [];

        function runAllTests() {
            testResults = [];

            // Test layout structure
            testLayoutStructure();

            // Test responsive design
            testResponsiveDesign();

            // Test form components
            testFormComponents();

            // Test navigation
            testNavigation();

            // Test CSS files
            testCSSFiles();

            // Show results
            showTestResults();
        }

        function testLayoutStructure() {
            const sidebar = document.querySelector('.demo-sidebar');
            const content = document.querySelector('.demo-content');

            const sidebarExists = sidebar !== null;
            const contentExists = content !== null;
            const hasTransitions = sidebar && getComputedStyle(sidebar).transition !== 'all 0s ease 0s';

            addTestResult('هيكل التخطيط', sidebarExists && contentExists,
                `الشريط الجانبي: ${sidebarExists ? '✓' : '✗'}, المحتوى: ${contentExists ? '✓' : '✗'}`);

            addTestResult('انتقالات CSS', hasTransitions,
                hasTransitions ? 'الانتقالات تعمل بشكل صحيح' : 'الانتقالات غير مفعلة');
        }

        function testResponsiveDesign() {
            const demo = document.getElementById('responsive-demo');
            const statsGrid = demo.querySelector('.stats-grid');

            const hasResponsiveGrid = statsGrid !== null;
            const hasFlexbox = statsGrid && getComputedStyle(statsGrid).display === 'grid';

            addTestResult('التصميم المتجاوب', hasResponsiveGrid,
                hasResponsiveGrid ? 'الشبكة المتجاوبة تعمل' : 'مشكلة في الشبكة المتجاوبة');

            addTestResult('CSS Grid', hasFlexbox,
                hasFlexbox ? 'CSS Grid مفعل' : 'CSS Grid غير مفعل');
        }

        function testFormComponents() {
            const inputs = document.querySelectorAll('input');
            const selects = document.querySelectorAll('select');
            const buttons = document.querySelectorAll('.btn');

            const hasInputs = inputs.length > 0;
            const hasSelects = selects.length > 0;
            const hasButtons = buttons.length > 0;

            addTestResult('حقول الإدخال', hasInputs,
                `عدد الحقول: ${inputs.length}`);

            addTestResult('قوائم الاختيار', hasSelects,
                `عدد القوائم: ${selects.length}`);

            addTestResult('الأزرار', hasButtons,
                `عدد الأزرار: ${buttons.length}`);
        }

        function testNavigation() {
            const navElements = document.querySelectorAll('[data-page]');
            const hasNavigation = navElements.length > 0;

            addTestResult('عناصر التنقل', hasNavigation,
                hasNavigation ? `${navElements.length} عنصر تنقل` : 'لا توجد عناصر تنقل');
        }

        function testCSSFiles() {
            const cssFiles = [
                'assets/css/layout-fixes.css',
                'assets/css/components-fixes.css',
                'assets/css/design-fixes.css'
            ];

            let loadedFiles = 0;
            const links = document.querySelectorAll('link[rel="stylesheet"]');

            cssFiles.forEach(file => {
                const isLoaded = Array.from(links).some(link => link.href.includes(file));
                if (isLoaded) loadedFiles++;
            });

            addTestResult('ملفات CSS', loadedFiles === cssFiles.length,
                `تم تحميل ${loadedFiles}/${cssFiles.length} ملف`);
        }

        function addTestResult(testName, passed, details) {
            testResults.push({
                name: testName,
                passed: passed,
                details: details,
                timestamp: new Date().toLocaleString('ar-IQ')
            });
        }

        function showTestResults() {
            const resultsContainer = document.getElementById('test-results');
            let html = '';

            const passedTests = testResults.filter(t => t.passed).length;
            const totalTests = testResults.length;
            const percentage = Math.round((passedTests / totalTests) * 100);

            html += `<div class="test-result ${percentage >= 80 ? 'test-pass' : percentage >= 60 ? 'test-warning' : 'test-fail'}">`;
            html += `<i class="fas ${percentage >= 80 ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>`;
            html += `<strong>النتيجة الإجمالية: ${passedTests}/${totalTests} اختبار ناجح (${percentage}%)</strong>`;
            html += `</div>`;

            testResults.forEach(result => {
                const resultClass = result.passed ? 'test-pass' : 'test-fail';
                const icon = result.passed ? 'fa-check' : 'fa-times';
                html += `
                    <div class="test-result ${resultClass}">
                        <i class="fas ${icon}"></i>
                        <div>
                            <strong>${result.name}:</strong> ${result.details}
                            <small style="display: block; margin-top: 5px; opacity: 0.8;">${result.timestamp}</small>
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }

        function toggleDemoSidebar() {
            const sidebar = document.getElementById('demo-sidebar');
            const content = document.getElementById('demo-content');

            sidebar.classList.toggle('mobile');
            content.classList.toggle('mobile');
        }

        function collapseDemoSidebar() {
            const sidebar = document.getElementById('demo-sidebar');
            const content = document.getElementById('demo-content');

            sidebar.classList.toggle('collapsed');
            content.classList.toggle('collapsed');
        }

        function testResponsive(size) {
            const demo = document.getElementById('responsive-demo');

            demo.className = `responsive-demo ${size}`;

            // Update stats grid for mobile
            const statsGrid = demo.querySelector('.stats-grid');
            if (statsGrid) {
                if (size === 'mobile') {
                    statsGrid.style.gridTemplateColumns = '1fr';
                } else if (size === 'tablet') {
                    statsGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                } else {
                    statsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(280px, 1fr))';
                }
            }
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        function resetTests() {
            testResults = [];
            document.getElementById('test-results').innerHTML = `
                <p style="text-align: center; color: #7f8c8d; padding: 20px;">
                    اضغط "تشغيل جميع الاختبارات" لبدء الفحص
                </p>
            `;

            // Reset demo elements
            const sidebar = document.getElementById('demo-sidebar');
            const content = document.getElementById('demo-content');
            const demo = document.getElementById('responsive-demo');

            sidebar.className = 'demo-sidebar';
            content.className = 'demo-content';
            demo.className = 'responsive-demo desktop';
        }

        // Initialize password toggle functionality
        document.addEventListener('DOMContentLoaded', () => {
            const toggleButtons = document.querySelectorAll('.toggle-password');

            toggleButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();

                    const input = button.parentElement.querySelector('input');
                    const icon = button.querySelector('i');

                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });
        });
    </script>
</body>
</html>