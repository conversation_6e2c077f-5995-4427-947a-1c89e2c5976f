// Returns Management System
class ReturnsManager {
    constructor() {
        this.returns = [];
        this.customers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            customer: '',
            status: '',
            reason: '',
            dateFrom: '',
            dateTo: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Filter controls
        document.addEventListener('change', (e) => {
            if (e.target.id === 'customer-filter') {
                this.filters.customer = e.target.value;
                this.filterReturns();
            }
            if (e.target.id === 'status-filter') {
                this.filters.status = e.target.value;
                this.filterReturns();
            }
            if (e.target.id === 'reason-filter') {
                this.filters.reason = e.target.value;
                this.filterReturns();
            }
            if (e.target.id === 'date-from-filter') {
                this.filters.dateFrom = e.target.value;
                this.filterReturns();
            }
            if (e.target.id === 'date-to-filter') {
                this.filters.dateTo = e.target.value;
                this.filterReturns();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-return-btn') || e.target.closest('.add-return-btn')) {
                e.preventDefault();
                this.showAddReturnModal();
            }
            if (e.target.matches('.edit-return-btn') || e.target.closest('.edit-return-btn')) {
                e.preventDefault();
                const returnId = e.target.closest('.edit-return-btn').getAttribute('data-return-id');
                this.showEditReturnModal(returnId);
            }
            if (e.target.matches('.delete-return-btn') || e.target.closest('.delete-return-btn')) {
                e.preventDefault();
                const returnId = e.target.closest('.delete-return-btn').getAttribute('data-return-id');
                this.deleteReturn(returnId);
            }
            if (e.target.matches('.view-return-btn') || e.target.closest('.view-return-btn')) {
                e.preventDefault();
                const returnId = e.target.closest('.view-return-btn').getAttribute('data-return-id');
                this.viewReturnDetails(returnId);
            }
            if (e.target.matches('.refresh-returns-btn') || e.target.closest('.refresh-returns-btn')) {
                e.preventDefault();
                this.refreshReturns();
            }
            if (e.target.matches('.export-returns-btn') || e.target.closest('.export-returns-btn')) {
                e.preventDefault();
                this.exportReturns();
            }
        });

        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'returns-search') {
                this.searchReturns(e.target.value);
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const returnsHTML = `
            <div class="returns-header">
                <div class="returns-title">
                    <h1>إدارة المرتجعات</h1>
                    <p>إدارة شاملة لجميع المرتجعات مع فرز حسب العميل</p>
                </div>
                <div class="returns-actions">
                    <button class="neu-btn primary add-return-btn">
                        <i class="fas fa-plus"></i>
                        إضافة مرتجع
                    </button>
                    <button class="neu-btn secondary export-returns-btn">
                        <i class="fas fa-file-export"></i>
                        تصدير
                    </button>
                    <button class="neu-btn refresh-returns-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="returns-stats">
                <div class="stat-card total-returns">
                    <div class="stat-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-returns-count">0</h3>
                        <p>إجمالي المرتجعات</p>
                    </div>
                </div>
                <div class="stat-card pending-returns">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="pending-returns-count">0</h3>
                        <p>مرتجعات معلقة</p>
                    </div>
                </div>
                <div class="stat-card processed-returns">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="processed-returns-count">0</h3>
                        <p>مرتجعات مُعالجة</p>
                    </div>
                </div>
                <div class="stat-card returns-value">
                    <div class="stat-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="returns-total-value">0 د.ع</h3>
                        <p>قيمة المرتجعات</p>
                    </div>
                </div>
            </div>

            <div class="returns-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="returns-search" class="neu-input" placeholder="البحث في المرتجعات...">
                    </div>
                    <div class="filter-group">
                        <label>العميل:</label>
                        <select id="customer-filter" class="neu-select">
                            <option value="">جميع العملاء</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلق</option>
                            <option value="processing">قيد المعالجة</option>
                            <option value="completed">مكتمل</option>
                            <option value="rejected">مرفوض</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>سبب الإرجاع:</label>
                        <select id="reason-filter" class="neu-select">
                            <option value="">جميع الأسباب</option>
                            <option value="damaged">تالف</option>
                            <option value="wrong-item">صنف خاطئ</option>
                            <option value="customer-request">طلب العميل</option>
                            <option value="quality-issue">مشكلة في الجودة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                </div>
                <div class="filters-row">
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" id="date-from-filter" class="neu-input">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" id="date-to-filter" class="neu-input">
                    </div>
                    <div class="filter-group">
                        <button class="neu-btn secondary clear-filters-btn">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>

            <div class="returns-by-customer">
                <h3>المرتجعات حسب العميل</h3>
                <div class="customer-returns-grid" id="customer-returns-grid">
                    <!-- Customer returns will be loaded here -->
                </div>
            </div>

            <div class="returns-list-container">
                <div class="list-header">
                    <h3>قائمة المرتجعات</h3>
                    <div class="list-actions">
                        <button class="neu-btn small view-toggle-btn" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="neu-btn small view-toggle-btn active" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                <div class="returns-table-wrapper">
                    <table class="returns-table">
                        <thead>
                            <tr>
                                <th>رقم المرتجع</th>
                                <th>رقم الطلب الأصلي</th>
                                <th>العميل</th>
                                <th>تاريخ الإرجاع</th>
                                <th>سبب الإرجاع</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="returns-tbody">
                            <!-- Returns will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="list-pagination" id="returns-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = returnsHTML;
        this.renderReturns();
        this.renderCustomerReturns();
        this.updateStats();
        this.populateCustomerFilter();
    }

    loadSampleData() {
        this.customers = [
            { id: 'CUS-001', name: 'أحمد محمد البغدادي', type: 'individual' },
            { id: 'CUS-002', name: 'شركة التوصيل السريع', type: 'company' },
            { id: 'CUS-003', name: 'مطعم بغداد الأصيل', type: 'company' },
            { id: 'CUS-004', name: 'فاطمة علي الكربلائي', type: 'individual' },
            { id: 'CUS-005', name: 'محمد حسن النجفي', type: 'individual' }
        ];

        this.returns = [
            {
                id: 'RET-001',
                originalOrderId: 'ORD-2024-001',
                customerId: 'CUS-001',
                customerName: 'أحمد محمد البغدادي',
                returnDate: new Date('2024-12-25'),
                reason: 'damaged',
                reasonText: 'تالف',
                description: 'وصل الطلب في حالة تالفة',
                value: 45000,
                status: 'pending',
                statusText: 'معلق',
                createdBy: 'admin',
                notes: 'يحتاج إلى فحص من قبل المورد'
            },
            {
                id: 'RET-002',
                originalOrderId: 'ORD-2024-003',
                customerId: 'CUS-002',
                customerName: 'شركة التوصيل السريع',
                returnDate: new Date('2024-12-24'),
                reason: 'wrong-item',
                reasonText: 'صنف خاطئ',
                description: 'تم إرسال صنف مختلف عن المطلوب',
                value: 75000,
                status: 'processing',
                statusText: 'قيد المعالجة',
                createdBy: 'employee1',
                notes: 'تم التواصل مع المورد'
            },
            {
                id: 'RET-003',
                originalOrderId: 'ORD-2024-005',
                customerId: 'CUS-003',
                customerName: 'مطعم بغداد الأصيل',
                returnDate: new Date('2024-12-23'),
                reason: 'quality-issue',
                reasonText: 'مشكلة في الجودة',
                description: 'جودة المنتج لا تتطابق مع المواصفات',
                value: 120000,
                status: 'completed',
                statusText: 'مكتمل',
                createdBy: 'admin',
                notes: 'تم استرداد المبلغ كاملاً'
            },
            {
                id: 'RET-004',
                originalOrderId: 'ORD-2024-007',
                customerId: 'CUS-004',
                customerName: 'فاطمة علي الكربلائي',
                returnDate: new Date('2024-12-22'),
                reason: 'customer-request',
                reasonText: 'طلب العميل',
                description: 'العميل غير راضي عن المنتج',
                value: 35000,
                status: 'rejected',
                statusText: 'مرفوض',
                createdBy: 'employee1',
                notes: 'تم رفض الطلب لعدم توفر شروط الإرجاع'
            },
            {
                id: 'RET-005',
                originalOrderId: 'ORD-2024-009',
                customerId: 'CUS-001',
                customerName: 'أحمد محمد البغدادي',
                returnDate: new Date('2024-12-21'),
                reason: 'damaged',
                reasonText: 'تالف',
                description: 'تلف أثناء النقل',
                value: 55000,
                status: 'completed',
                statusText: 'مكتمل',
                createdBy: 'admin',
                notes: 'تم تعويض العميل'
            }
        ];
    }

    renderReturns() {
        const tbody = document.getElementById('returns-tbody');
        if (!tbody) return;

        const filteredReturns = this.getFilteredReturns();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const returnsToShow = filteredReturns.slice(startIndex, endIndex);

        tbody.innerHTML = returnsToShow.map(returnItem => `
            <tr class="return-row ${returnItem.status}">
                <td>
                    <strong>${returnItem.id}</strong>
                </td>
                <td>
                    <a href="#" class="order-link" data-order-id="${returnItem.originalOrderId}">
                        ${returnItem.originalOrderId}
                    </a>
                </td>
                <td>
                    <div class="customer-info">
                        <strong>${returnItem.customerName}</strong>
                        <small>${returnItem.customerId}</small>
                    </div>
                </td>
                <td>${this.formatDate(returnItem.returnDate)}</td>
                <td>
                    <span class="reason-badge reason-${returnItem.reason}">
                        ${returnItem.reasonText}
                    </span>
                </td>
                <td>
                    <strong>${this.formatCurrency(returnItem.value)}</strong>
                </td>
                <td>
                    <span class="status-badge status-${returnItem.status}">
                        ${returnItem.statusText}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-return-btn" data-return-id="${returnItem.id}" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-return-btn" data-return-id="${returnItem.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-return-btn" data-return-id="${returnItem.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination(filteredReturns.length);
    }

    renderCustomerReturns() {
        const container = document.getElementById('customer-returns-grid');
        if (!container) return;

        // Group returns by customer
        const customerReturns = {};
        this.returns.forEach(returnItem => {
            if (!customerReturns[returnItem.customerId]) {
                customerReturns[returnItem.customerId] = {
                    customer: returnItem.customerName,
                    returns: [],
                    totalValue: 0,
                    pendingCount: 0,
                    completedCount: 0
                };
            }
            customerReturns[returnItem.customerId].returns.push(returnItem);
            customerReturns[returnItem.customerId].totalValue += returnItem.value;
            if (returnItem.status === 'pending') {
                customerReturns[returnItem.customerId].pendingCount++;
            } else if (returnItem.status === 'completed') {
                customerReturns[returnItem.customerId].completedCount++;
            }
        });

        container.innerHTML = Object.keys(customerReturns).map(customerId => {
            const data = customerReturns[customerId];
            return `
                <div class="customer-returns-card">
                    <div class="customer-header">
                        <h4>${data.customer}</h4>
                        <span class="customer-id">${customerId}</span>
                    </div>
                    <div class="customer-stats">
                        <div class="stat-item">
                            <span class="stat-label">إجمالي المرتجعات:</span>
                            <span class="stat-value">${this.formatNumber(data.returns.length)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">القيمة الإجمالية:</span>
                            <span class="stat-value">${this.formatCurrency(data.totalValue)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">معلقة:</span>
                            <span class="stat-value pending">${this.formatNumber(data.pendingCount)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">مكتملة:</span>
                            <span class="stat-value completed">${this.formatNumber(data.completedCount)}</span>
                        </div>
                    </div>
                    <div class="customer-actions">
                        <button class="neu-btn small view-customer-returns-btn" data-customer-id="${customerId}">
                            <i class="fas fa-eye"></i>
                            عرض المرتجعات
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    updateStats() {
        const totalReturns = this.returns.length;
        const pendingReturns = this.returns.filter(r => r.status === 'pending').length;
        const processedReturns = this.returns.filter(r => r.status === 'completed').length;
        const totalValue = this.returns.reduce((sum, r) => sum + r.value, 0);

        document.getElementById('total-returns-count').textContent = this.formatNumber(totalReturns);
        document.getElementById('pending-returns-count').textContent = this.formatNumber(pendingReturns);
        document.getElementById('processed-returns-count').textContent = this.formatNumber(processedReturns);
        document.getElementById('returns-total-value').textContent = this.formatCurrency(totalValue);
    }

    populateCustomerFilter() {
        const customerFilter = document.getElementById('customer-filter');
        if (!customerFilter) return;

        const customerOptions = this.customers.map(customer =>
            `<option value="${customer.id}">${customer.name}</option>`
        ).join('');

        customerFilter.innerHTML = '<option value="">جميع العملاء</option>' + customerOptions;
    }

    getFilteredReturns() {
        return this.returns.filter(returnItem => {
            const matchesCustomer = !this.filters.customer || returnItem.customerId === this.filters.customer;
            const matchesStatus = !this.filters.status || returnItem.status === this.filters.status;
            const matchesReason = !this.filters.reason || returnItem.reason === this.filters.reason;

            let matchesDateRange = true;
            if (this.filters.dateFrom) {
                matchesDateRange = matchesDateRange && returnItem.returnDate >= new Date(this.filters.dateFrom);
            }
            if (this.filters.dateTo) {
                matchesDateRange = matchesDateRange && returnItem.returnDate <= new Date(this.filters.dateTo);
            }

            return matchesCustomer && matchesStatus && matchesReason && matchesDateRange;
        });
    }

    renderPagination(totalItems) {
        const pagination = document.getElementById('returns-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    filterReturns() {
        this.currentPage = 1;
        this.renderReturns();
        this.renderCustomerReturns();
    }

    searchReturns(query) {
        if (!query) {
            this.renderReturns();
            return;
        }

        const filteredReturns = this.returns.filter(returnItem =>
            returnItem.id.toLowerCase().includes(query.toLowerCase()) ||
            returnItem.originalOrderId.toLowerCase().includes(query.toLowerCase()) ||
            returnItem.customerName.toLowerCase().includes(query.toLowerCase()) ||
            returnItem.description.toLowerCase().includes(query.toLowerCase())
        );

        this.renderFilteredReturns(filteredReturns);
    }

    renderFilteredReturns(filteredReturns) {
        const tbody = document.getElementById('returns-tbody');
        if (!tbody) return;

        tbody.innerHTML = filteredReturns.map(returnItem => `
            <tr class="return-row ${returnItem.status}">
                <td><strong>${returnItem.id}</strong></td>
                <td><a href="#" class="order-link">${returnItem.originalOrderId}</a></td>
                <td>
                    <div class="customer-info">
                        <strong>${returnItem.customerName}</strong>
                        <small>${returnItem.customerId}</small>
                    </div>
                </td>
                <td>${this.formatDate(returnItem.returnDate)}</td>
                <td><span class="reason-badge reason-${returnItem.reason}">${returnItem.reasonText}</span></td>
                <td><strong>${this.formatCurrency(returnItem.value)}</strong></td>
                <td><span class="status-badge status-${returnItem.status}">${returnItem.statusText}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-return-btn" data-return-id="${returnItem.id}" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-return-btn" data-return-id="${returnItem.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-return-btn" data-return-id="${returnItem.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    showAddReturnModal() {
        if (window.ModalManager) {
            window.ModalManager.showAddReturnModal();
        } else {
            alert('سيتم إضافة نافذة إضافة مرتجع قريباً');
        }
    }

    showEditReturnModal(returnId) {
        const returnItem = this.returns.find(r => r.id === returnId);
        if (returnItem && window.ModalManager) {
            window.ModalManager.showEditReturnModal(returnItem);
        } else {
            alert('سيتم إضافة نافذة تعديل المرتجع قريباً');
        }
    }

    viewReturnDetails(returnId) {
        const returnItem = this.returns.find(r => r.id === returnId);
        if (returnItem && window.ModalManager) {
            window.ModalManager.showReturnDetailsModal(returnItem);
        } else {
            alert('سيتم إضافة نافذة تفاصيل المرتجع قريباً');
        }
    }

    deleteReturn(returnId) {
        if (confirm('هل أنت متأكد من حذف هذا المرتجع؟')) {
            this.returns = this.returns.filter(r => r.id !== returnId);
            this.renderReturns();
            this.renderCustomerReturns();
            this.updateStats();
            this.showNotification('تم حذف المرتجع بنجاح', 'success');
        }
    }

    refreshReturns() {
        this.renderReturns();
        this.renderCustomerReturns();
        this.updateStats();
        this.showNotification('تم تحديث المرتجعات', 'success');
    }

    exportReturns() {
        const csvData = this.generateCSVData();
        this.downloadCSV(csvData, `returns_${new Date().toISOString().split('T')[0]}.csv`);
    }

    generateCSVData() {
        let csvContent = 'رقم المرتجع,رقم الطلب الأصلي,العميل,تاريخ الإرجاع,سبب الإرجاع,القيمة,الحالة,الوصف\n';

        this.returns.forEach(returnItem => {
            csvContent += `${returnItem.id},${returnItem.originalOrderId},${returnItem.customerName},${this.formatDate(returnItem.returnDate)},${returnItem.reasonText},${returnItem.value},${returnItem.statusText},"${returnItem.description}"\n`;
        });

        return csvContent;
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Helper functions
    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-IQ');
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Returns Manager
window.ReturnsManager = new ReturnsManager();
