// UI Fixes and Enhancements for Iraqi Delivery Management System

class UIFixes {
    constructor() {
        this.init();
    }

    init() {
        this.setupGlobalEventListeners();
        this.fixCommonIssues();
        this.enhanceUserExperience();
        this.setupAccessibility();
        this.setupPerformanceOptimizations();
    }

    setupGlobalEventListeners() {
        // Fix form submission issues
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('needs-validation')) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showValidationErrors(form);
                }
                form.classList.add('was-validated');
            }
        });

        // Fix button click issues
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (button && !button.disabled) {
                this.addClickFeedback(button);
            }

            // Fix dropdown toggles
            if (e.target.matches('.dropdown-toggle') || e.target.closest('.dropdown-toggle')) {
                e.preventDefault();
                this.toggleDropdown(e.target.closest('.dropdown-toggle'));
            }

            // Close dropdowns when clicking outside
            if (!e.target.closest('.dropdown')) {
                this.closeAllDropdowns();
            }
        });

        // Fix input focus issues
        document.addEventListener('focusin', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.enhanceInputFocus(e.target);
            }
        });

        document.addEventListener('focusout', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.validateInput(e.target);
            }
        });

        // Fix keyboard navigation
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // Fix resize issues
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Fix scroll issues
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });
    }

    fixCommonIssues() {
        // Fix missing alt attributes
        document.querySelectorAll('img:not([alt])').forEach(img => {
            img.setAttribute('alt', 'صورة');
        });

        // Fix missing labels
        document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])').forEach(input => {
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (!label && input.placeholder) {
                input.setAttribute('aria-label', input.placeholder);
            }
        });

        // Fix missing roles
        document.querySelectorAll('button:not([role])').forEach(button => {
            button.setAttribute('role', 'button');
        });

        // Fix tab index issues
        document.querySelectorAll('a[href="#"], button[disabled]').forEach(element => {
            element.setAttribute('tabindex', '-1');
        });

        // Fix contrast issues
        this.fixContrastIssues();

        // Fix layout issues
        this.fixLayoutIssues();
    }

    enhanceUserExperience() {
        // Add loading states
        this.setupLoadingStates();

        // Add smooth scrolling
        this.setupSmoothScrolling();

        // Add tooltips
        this.setupTooltips();

        // Add animations
        this.setupAnimations();

        // Add keyboard shortcuts
        this.setupKeyboardShortcuts();
    }

    setupAccessibility() {
        // Add ARIA labels
        document.querySelectorAll('.btn:not([aria-label])').forEach(btn => {
            const text = btn.textContent.trim() || btn.getAttribute('title') || 'زر';
            btn.setAttribute('aria-label', text);
        });

        // Add focus indicators
        const style = document.createElement('style');
        style.textContent = `
            *:focus {
                outline: 2px solid var(--primary-color, #667eea) !important;
                outline-offset: 2px !important;
            }
            
            .sr-only {
                position: absolute !important;
                width: 1px !important;
                height: 1px !important;
                padding: 0 !important;
                margin: -1px !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                white-space: nowrap !important;
                border: 0 !important;
            }
        `;
        document.head.appendChild(style);

        // Add skip links
        this.addSkipLinks();

        // Add landmark roles
        this.addLandmarkRoles();
    }

    setupPerformanceOptimizations() {
        // Lazy load images
        this.setupLazyLoading();

        // Debounce resize events
        this.debounceResize();

        // Optimize animations
        this.optimizeAnimations();

        // Preload critical resources
        this.preloadCriticalResources();
    }

    addClickFeedback(button) {
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }

    toggleDropdown(toggle) {
        const dropdown = toggle.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (menu) {
            const isOpen = menu.classList.contains('show');
            this.closeAllDropdowns();
            
            if (!isOpen) {
                menu.classList.add('show');
                toggle.setAttribute('aria-expanded', 'true');
            }
        }
    }

    closeAllDropdowns() {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
            const toggle = menu.closest('.dropdown').querySelector('.dropdown-toggle');
            if (toggle) {
                toggle.setAttribute('aria-expanded', 'false');
            }
        });
    }

    enhanceInputFocus(input) {
        const group = input.closest('.input-group, .form-group');
        if (group) {
            group.classList.add('focused');
        }
    }

    validateInput(input) {
        const group = input.closest('.input-group, .form-group');
        if (group) {
            group.classList.remove('focused');
        }

        // Add validation feedback
        if (input.checkValidity()) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
    }

    showValidationErrors(form) {
        const invalidInputs = form.querySelectorAll(':invalid');
        invalidInputs.forEach(input => {
            input.classList.add('is-invalid');
            
            // Show error message
            let errorMsg = input.nextElementSibling;
            if (!errorMsg || !errorMsg.classList.contains('invalid-feedback')) {
                errorMsg = document.createElement('div');
                errorMsg.className = 'invalid-feedback';
                input.parentNode.appendChild(errorMsg);
            }
            
            errorMsg.textContent = input.validationMessage || 'هذا الحقل مطلوب';
        });
    }

    handleKeyboardNavigation(e) {
        // Escape key handling
        if (e.key === 'Escape') {
            this.closeAllDropdowns();
            
            // Close modals
            const openModal = document.querySelector('.modal.show');
            if (openModal && window.app && window.app.managers.modal) {
                window.app.managers.modal.closeModal();
            }
        }

        // Tab navigation enhancement
        if (e.key === 'Tab') {
            this.enhanceTabNavigation(e);
        }

        // Arrow key navigation for menus
        if (['ArrowUp', 'ArrowDown'].includes(e.key)) {
            this.handleArrowNavigation(e);
        }
    }

    handleResize() {
        // Update mobile menu state
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            if (window.innerWidth <= 768) {
                sidebar.classList.add('mobile');
            } else {
                sidebar.classList.remove('mobile', 'mobile-open');
            }
        }

        // Update responsive tables
        this.updateResponsiveTables();
    }

    handleScroll() {
        // Add scroll shadow to header
        const header = document.querySelector('.header');
        if (header) {
            if (window.scrollY > 10) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        }
    }

    fixContrastIssues() {
        // Add high contrast mode support
        const style = document.createElement('style');
        style.textContent = `
            @media (prefers-contrast: high) {
                :root {
                    --text-primary: #000000;
                    --text-secondary: #333333;
                    --bg-primary: #ffffff;
                    --bg-secondary: #f8f9fa;
                }
            }
        `;
        document.head.appendChild(style);
    }

    fixLayoutIssues() {
        // Fix overflow issues
        document.querySelectorAll('.table-wrapper').forEach(wrapper => {
            wrapper.style.overflowX = 'auto';
        });

        // Fix flex issues
        document.querySelectorAll('.d-flex').forEach(flex => {
            if (!flex.style.flexWrap) {
                flex.style.flexWrap = 'wrap';
            }
        });
    }

    setupLoadingStates() {
        // Add loading state to buttons
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button[type="submit"]');
            if (button && !button.disabled) {
                this.showButtonLoading(button);
            }
        });
    }

    showButtonLoading(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        button.disabled = true;

        // Reset after 3 seconds (adjust as needed)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 3000);
    }

    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    setupTooltips() {
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
        });
    }

    showTooltip(element) {
        const title = element.getAttribute('title');
        if (!title) return;

        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = title;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
        `;

        document.body.appendChild(tooltip);

        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

        // Store reference
        element._tooltip = tooltip;
        
        // Remove title to prevent default tooltip
        element.setAttribute('data-original-title', title);
        element.removeAttribute('title');
    }

    hideTooltip(element) {
        if (element._tooltip) {
            element._tooltip.remove();
            element._tooltip = null;
        }
        
        // Restore title
        const originalTitle = element.getAttribute('data-original-title');
        if (originalTitle) {
            element.setAttribute('title', originalTitle);
            element.removeAttribute('data-original-title');
        }
    }

    setupAnimations() {
        // Add entrance animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        });

        document.querySelectorAll('.stat-card, .neu-card').forEach(card => {
            observer.observe(card);
        });

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            .animate-in {
                animation: slideInUp 0.6s ease-out;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchBtn = document.querySelector('.search-btn');
                if (searchBtn) {
                    searchBtn.click();
                }
            }

            // Alt + M for menu toggle
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                const menuToggle = document.querySelector('.mobile-menu-toggle, .sidebar-toggle');
                if (menuToggle) {
                    menuToggle.click();
                }
            }
        });
    }

    addSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'تخطي إلى المحتوى الرئيسي';
        skipLink.className = 'sr-only';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color, #667eea);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 10000;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });

        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    addLandmarkRoles() {
        // Add main role
        const mainContent = document.querySelector('.main-content, .page-content');
        if (mainContent && !mainContent.getAttribute('role')) {
            mainContent.setAttribute('role', 'main');
            mainContent.id = 'main-content';
        }

        // Add navigation role
        const nav = document.querySelector('.sidebar, .nav-menu');
        if (nav && !nav.getAttribute('role')) {
            nav.setAttribute('role', 'navigation');
            nav.setAttribute('aria-label', 'القائمة الرئيسية');
        }

        // Add banner role
        const header = document.querySelector('.header');
        if (header && !header.getAttribute('role')) {
            header.setAttribute('role', 'banner');
        }
    }

    setupLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    debounceResize() {
        let resizeTimer;
        const originalResize = window.onresize;
        
        window.onresize = () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                if (originalResize) originalResize();
                this.handleResize();
            }, 250);
        };
    }

    optimizeAnimations() {
        // Reduce animations for users who prefer reduced motion
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    preloadCriticalResources() {
        // Preload critical CSS
        const criticalCSS = ['assets/css/style.css', 'assets/css/design-fixes.css'];
        criticalCSS.forEach(href => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = href;
            document.head.appendChild(link);
        });
    }

    updateResponsiveTables() {
        document.querySelectorAll('.data-table').forEach(table => {
            const wrapper = table.closest('.table-wrapper');
            if (wrapper) {
                if (table.offsetWidth > wrapper.offsetWidth) {
                    wrapper.classList.add('scrollable');
                } else {
                    wrapper.classList.remove('scrollable');
                }
            }
        });
    }
}

// Initialize UI fixes when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new UIFixes();
});

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.UIFixes = UIFixes;
}
