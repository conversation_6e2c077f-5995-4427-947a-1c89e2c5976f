# التثبيت السريع لتطبيق سطح المكتب
## Quick Desktop Setup Guide

**⏱️ الوقت المطلوب**: 10-15 دقيقة  
**💾 المساحة المطلوبة**: 2 GB  
**🖥️ النظام**: Windows 10/11  

---

## 🚀 **الطريقة السريعة (مُوصى بها)**

### **الخطوة 1: تحميل Node.js**
1. اذهب إلى: https://nodejs.org/
2. اضغط **"Download for Windows"** (الزر الأخضر)
3. شغل الملف المُحمل واتبع التثبيت العادي
4. ✅ **مهم**: تأكد من تحديد "Add to PATH"

### **الخطوة 2: تشغيل ملف البناء التلقائي**
1. انقر نقراً مزدوجاً على ملف: **`build-desktop-app.bat`**
2. اختر **"Run as Administrator"** إذا طُلب منك
3. انتظر انتهاء العملية (10-15 دقيقة)
4. ✅ ستحصل على ملف `.exe` جاهز!

---

## 📋 **الطريقة اليدوية**

### **إذا فضلت التحكم الكامل:**

```cmd
# 1. افتح Command Prompt كمدير
# 2. انتقل لمجلد المشروع
cd "C:\Users\<USER>\Downloads\حاسبة"

# 3. تثبيت التبعيات
npm install

# 4. اختبار التطبيق (اختياري)
npm start

# 5. بناء ملف exe
npm run build-win
```

---

## 📁 **النتيجة المتوقعة**

بعد انتهاء البناء، ستجد في مجلد `dist/`:

```
📦 نظام إدارة شركة التوصيل العراقية Setup 1.0.0.exe
   ↳ مثبت كامل (~150 MB)
   ↳ ينشئ اختصارات على سطح المكتب
   ↳ يضيف التطبيق لقائمة البرامج

🚀 نظام إدارة شركة التوصيل العراقية 1.0.0.exe  
   ↳ نسخة محمولة (~180 MB)
   ↳ تعمل بدون تثبيت
   ↳ يمكن نسخها على فلاش ميموري
```

---

## ⚡ **اختبار سريع**

### **للتأكد من نجاح البناء:**
1. شغل ملف **Setup.exe**
2. اتبع معالج التثبيت
3. افتح التطبيق من سطح المكتب
4. سجل دخول:
   - **المستخدم**: admin
   - **كلمة المرور**: admin123
5. ✅ إذا فتح التطبيق ودخلت بنجاح = كل شيء يعمل!

---

## 🔧 **حل المشاكل السريع**

### **❌ "node is not recognized"**
- أعد تثبيت Node.js وتأكد من "Add to PATH"
- أعد تشغيل Command Prompt

### **❌ "npm install failed"**
```cmd
npm cache clean --force
npm install
```

### **❌ "Permission denied"**
- شغل Command Prompt كمدير (Run as Administrator)

### **❌ "Build failed"**
- تأكد من وجود 2 GB مساحة فارغة
- أغلق البرامج الأخرى وأعد المحاولة

---

## 📞 **الدعم السريع**

### **🆘 تحتاج مساعدة؟**
- **WhatsApp**: +964-XXX-XXXX
- **Email**: <EMAIL>
- **Telegram**: @IraqiDeliverySupport

### **📚 مراجع مفيدة:**
- **الدليل المفصل**: `DESKTOP_INSTALLATION_GUIDE.md`
- **دليل المستخدم**: `USER_GUIDE.md`
- **الأسئلة الشائعة**: `FAQ.md`

---

## 🎯 **نصائح مهمة**

### **✅ للحصول على أفضل النتائج:**
1. **استخدم Windows 10/11** (64-bit)
2. **أغلق برامج الحماية** مؤقتاً أثناء البناء
3. **تأكد من الاتصال بالإنترنت** أثناء تثبيت التبعيات
4. **لا تغلق النافذة** أثناء عملية البناء
5. **احتفظ بنسخة احتياطية** من الملفات المُنتجة

### **🚀 بعد التثبيت:**
- **اختبر جميع الوظائف** قبل التوزيع
- **أنشئ نسخة احتياطية** من البيانات
- **درب المستخدمين** على الميزات الجديدة

---

**© 2024 نظام إدارة شركة التوصيل العراقية**

**🎉 استمتع بتطبيق سطح المكتب الاحترافي! 🎉**
