<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الحقيقي - تشخيص الأخطاء</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error-details {
            background: #f1f3f4;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            border-left: 3px solid #dc3545;
        }
        .fix-suggestion {
            background: #e7f3ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #0066cc;
        }
        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .run-test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
        }
        .run-test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-bug"></i> اختبار النظام الحقيقي - تشخيص الأخطاء</h1>
            <p>فحص شامل للوظائف الفعلية وتحديد المشاكل الحقيقية</p>
            <button class="run-test-btn" onclick="runRealTests()">
                <i class="fas fa-play"></i> تشغيل الاختبار الحقيقي
            </button>
        </div>

        <div class="test-stats" id="test-stats">
            <div class="stat-card">
                <div class="stat-number" id="total-tests">0</div>
                <div>إجمالي الاختبارات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passed-tests">0</div>
                <div>اختبارات ناجحة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failed-tests">0</div>
                <div>اختبارات فاشلة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="success-rate">0%</div>
                <div>معدل النجاح</div>
            </div>
        </div>

        <div id="test-results"></div>
    </div>

    <!-- تحميل جميع ملفات النظام للاختبار -->
    <script src="assets/js/app.js"></script>
    <script src="assets/js/orders.js"></script>
    <script src="assets/js/modals.js"></script>
    <script src="assets/js/customers.js"></script>
    <script src="assets/js/finance.js"></script>
    <script src="assets/js/tracking.js"></script>
    <script src="assets/js/reports.js"></script>
    <script src="assets/js/driver-reports.js"></script>
    <script src="assets/js/invoices.js"></script>

    <script>
        let testResults = [];
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        // تهيئة النظام للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة النظام الرئيسي
            if (typeof DeliveryManagementSystem !== 'undefined') {
                window.app = new DeliveryManagementSystem();
            }

            // تهيئة مدير النوافذ المنبثقة
            if (typeof ModalManager !== 'undefined') {
                window.ModalManager = new ModalManager();
            }

            // تهيئة مدير الطلبات
            if (typeof OrderManager !== 'undefined') {
                window.OrderManager = new OrderManager();
            }

            // تهيئة مدير العملاء
            if (typeof CustomerManager !== 'undefined') {
                window.CustomerManager = new CustomerManager();
            }
        });

        function runRealTests() {
            testResults = [];
            totalTests = 0;
            passedTests = 0;
            failedTests = 0;

            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '<div class="test-section"><h3>جاري تشغيل الاختبارات...</h3></div>';

            // تشغيل جميع الاختبارات
            setTimeout(() => {
                testLoginSystem();
                testOrderManagement();
                testDriverManagement();
                testCustomerManagement();
                testFinancialSystem();
                testTrackingSystem();
                testReportsSystem();
                testDesktopFeatures();

                displayResults();
                updateStats();
                generateTestReport();
            }, 1000);
        }

        function addTestResult(category, testName, passed, error = null, suggestion = null) {
            testResults.push({
                category,
                testName,
                passed,
                error,
                suggestion,
                timestamp: new Date().toISOString()
            });
            
            totalTests++;
            if (passed) {
                passedTests++;
            } else {
                failedTests++;
            }
        }

        function testLoginSystem() {
            const category = "نظام تسجيل الدخول";

            try {
                // فحص وجود نموذج تسجيل الدخول
                const loginForm = document.querySelector('.login-form') || document.querySelector('#login-form');
                if (loginForm) {
                    addTestResult(category, "وجود نموذج تسجيل الدخول", true);
                } else {
                    addTestResult(category, "وجود نموذج تسجيل الدخول", false,
                        "لم يتم العثور على نموذج تسجيل الدخول",
                        "تأكد من وجود عنصر بالكلاس .login-form أو #login-form");
                }

                // فحص وجود نظام إدارة التوصيل
                if (typeof window.DeliveryManagementSystem !== 'undefined') {
                    addTestResult(category, "نظام إدارة التوصيل", true);
                } else {
                    addTestResult(category, "نظام إدارة التوصيل", false,
                        "DeliveryManagementSystem غير موجود",
                        "تأكد من تحميل ملف app.js بشكل صحيح");
                }

                // فحص وجود وظيفة تسجيل الدخول في النظام
                if (window.app && typeof window.app.handleLogin === 'function') {
                    addTestResult(category, "وظيفة تسجيل الدخول", true);
                } else {
                    addTestResult(category, "وظيفة تسجيل الدخول", false,
                        "وظيفة handleLogin غير موجودة في النظام",
                        "تأكد من تهيئة النظام بشكل صحيح");
                }

                // فحص وجود حقول تسجيل الدخول المطلوبة
                const usernameField = document.querySelector('#username');
                const passwordField = document.querySelector('#password');
                const userTypeField = document.querySelector('#user_type');

                if (usernameField && passwordField && userTypeField) {
                    addTestResult(category, "حقول تسجيل الدخول", true);
                } else {
                    addTestResult(category, "حقول تسجيل الدخول", false,
                        "بعض حقول تسجيل الدخول مفقودة",
                        "تأكد من وجود حقول username, password, user_type");
                }

            } catch (error) {
                addTestResult(category, "اختبار نظام تسجيل الدخول", false, error.message);
            }
        }

        function testOrderManagement() {
            const category = "إدارة الطلبات";
            
            try {
                // فحص وجود وظيفة إضافة طلب
                if (typeof showAddOrderModal === 'function') {
                    addTestResult(category, "وظيفة إضافة طلب جديد", true);
                } else {
                    addTestResult(category, "وظيفة إضافة طلب جديد", false,
                        "وظيفة showAddOrderModal غير موجودة",
                        "أضف وظيفة showAddOrderModal في ملف orders.js");
                }

                // فحص وجود وظيفة تحديث حالة الطلب
                if (typeof updateOrderStatus === 'function') {
                    addTestResult(category, "وظيفة تحديث حالة الطلب", true);
                } else {
                    addTestResult(category, "وظيفة تحديث حالة الطلب", false,
                        "وظيفة updateOrderStatus غير موجودة",
                        "أضف وظيفة updateOrderStatus في ملف orders.js");
                }

                // فحص وجود وظيفة حذف الطلب
                if (typeof deleteOrder === 'function') {
                    addTestResult(category, "وظيفة حذف الطلب", true);
                } else {
                    addTestResult(category, "وظيفة حذف الطلب", false,
                        "وظيفة deleteOrder غير موجودة",
                        "أضف وظيفة deleteOrder في ملف orders.js");
                }

                // فحص وجود وظيفة البحث والفلترة
                if (typeof filterOrders === 'function') {
                    addTestResult(category, "وظيفة البحث والفلترة", true);
                } else {
                    addTestResult(category, "وظيفة البحث والفلترة", false,
                        "وظيفة filterOrders غير موجودة",
                        "أضف وظيفة filterOrders في ملف orders.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار إدارة الطلبات", false, error.message);
            }
        }

        function testDriverManagement() {
            const category = "إدارة المندوبين";
            
            try {
                // فحص عدم وجود حقل البريد الإلكتروني
                const emailField = document.querySelector('#driver-email');
                if (!emailField) {
                    addTestResult(category, "إزالة حقل البريد الإلكتروني", true);
                } else {
                    addTestResult(category, "إزالة حقل البريد الإلكتروني", false,
                        "حقل البريد الإلكتروني ما زال موجود",
                        "احذف حقل #driver-email من نموذج إضافة المندوب");
                }

                // فحص أن رقم الهاتف اختياري
                const phoneField = document.querySelector('#driver-phone');
                if (phoneField && !phoneField.hasAttribute('required')) {
                    addTestResult(category, "رقم الهاتف اختياري", true);
                } else if (phoneField && phoneField.hasAttribute('required')) {
                    addTestResult(category, "رقم الهاتف اختياري", false,
                        "رقم الهاتف ما زال مطلوب",
                        "احذف خاصية required من حقل #driver-phone");
                } else {
                    addTestResult(category, "رقم الهاتف اختياري", false,
                        "حقل رقم الهاتف غير موجود",
                        "أضف حقل #driver-phone في نموذج إضافة المندوب");
                }

                // فحص وجود وظيفة إضافة مندوب
                if (typeof showAddDriverModal === 'function') {
                    addTestResult(category, "وظيفة إضافة مندوب", true);
                } else {
                    addTestResult(category, "وظيفة إضافة مندوب", false,
                        "وظيفة showAddDriverModal غير موجودة",
                        "أضف وظيفة showAddDriverModal في ملف modals.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار إدارة المندوبين", false, error.message);
            }
        }

        function testCustomerManagement() {
            const category = "إدارة العملاء";
            
            try {
                // فحص وجود نظام عمولة العميل
                if (typeof showBulkCommissionModal === 'function') {
                    addTestResult(category, "نظام عمولة العميل", true);
                } else {
                    addTestResult(category, "نظام عمولة العميل", false,
                        "وظيفة showBulkCommissionModal غير موجودة",
                        "أضف وظيفة showBulkCommissionModal في ملف customers.js");
                }

                // فحص وجود تحليل العملاء
                if (typeof showCustomerAnalytics === 'function') {
                    addTestResult(category, "تحليل العملاء", true);
                } else {
                    addTestResult(category, "تحليل العملاء", false,
                        "وظيفة showCustomerAnalytics غير موجودة",
                        "أضف وظيفة showCustomerAnalytics في ملف customers.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار إدارة العملاء", false, error.message);
            }
        }

        function testFinancialSystem() {
            const category = "النظام المالي";
            
            try {
                // فحص وجود نظام الفواتير
                if (typeof window.InvoiceManager !== 'undefined') {
                    addTestResult(category, "نظام الفواتير", true);
                } else {
                    addTestResult(category, "نظام الفواتير", false,
                        "InvoiceManager غير موجود",
                        "تأكد من تحميل ملف invoices.js");
                }

                // فحص وجود النظام المالي
                if (typeof window.FinanceManager !== 'undefined') {
                    addTestResult(category, "مدير النظام المالي", true);
                } else {
                    addTestResult(category, "مدير النظام المالي", false,
                        "FinanceManager غير موجود",
                        "تأكد من تحميل ملف finance.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار النظام المالي", false, error.message);
            }
        }

        function testTrackingSystem() {
            const category = "نظام التتبع";
            
            try {
                // فحص وجود نظام التتبع
                if (typeof window.TrackingManager !== 'undefined') {
                    addTestResult(category, "مدير نظام التتبع", true);
                } else {
                    addTestResult(category, "مدير نظام التتبع", false,
                        "TrackingManager غير موجود",
                        "تأكد من تحميل ملف tracking.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار نظام التتبع", false, error.message);
            }
        }

        function testReportsSystem() {
            const category = "نظام التقارير";
            
            try {
                // فحص وجود نظام التقارير
                if (typeof window.ReportsManager !== 'undefined') {
                    addTestResult(category, "مدير نظام التقارير", true);
                } else {
                    addTestResult(category, "مدير نظام التقارير", false,
                        "ReportsManager غير موجود",
                        "تأكد من تحميل ملف reports.js");
                }

                // فحص وجود تقارير المندوبين
                if (typeof window.DriverReportsManager !== 'undefined') {
                    addTestResult(category, "تقارير المندوبين", true);
                } else {
                    addTestResult(category, "تقارير المندوبين", false,
                        "DriverReportsManager غير موجود",
                        "تأكد من تحميل ملف driver-reports.js");
                }

            } catch (error) {
                addTestResult(category, "اختبار نظام التقارير", false, error.message);
            }
        }

        function testDesktopFeatures() {
            const category = "ميزات سطح المكتب";
            
            try {
                // فحص وجود APIs سطح المكتب
                if (typeof window.electronAPI !== 'undefined') {
                    addTestResult(category, "Electron APIs", true);
                } else {
                    addTestResult(category, "Electron APIs", false,
                        "electronAPI غير متاح",
                        "هذا طبيعي في المتصفح - يعمل فقط في تطبيق سطح المكتب");
                }

                // فحص وجود نظام النسخ الاحتياطية
                if (typeof window.backupAPI !== 'undefined') {
                    addTestResult(category, "نظام النسخ الاحتياطية", true);
                } else {
                    addTestResult(category, "نظام النسخ الاحتياطية", false,
                        "backupAPI غير متاح",
                        "هذا طبيعي في المتصفح - يعمل فقط في تطبيق سطح المكتب");
                }

            } catch (error) {
                addTestResult(category, "اختبار ميزات سطح المكتب", false, error.message);
            }
        }

        function displayResults() {
            const resultsContainer = document.getElementById('test-results');
            let html = '';

            // تجميع النتائج حسب الفئة
            const categories = {};
            testResults.forEach(result => {
                if (!categories[result.category]) {
                    categories[result.category] = [];
                }
                categories[result.category].push(result);
            });

            // عرض النتائج
            Object.keys(categories).forEach(category => {
                html += `<div class="test-section">`;
                html += `<h3><i class="fas fa-cog"></i> ${category}</h3>`;
                
                categories[category].forEach(result => {
                    const resultClass = result.passed ? 'test-pass' : 'test-fail';
                    const icon = result.passed ? 'fa-check' : 'fa-times';
                    
                    html += `<div class="test-result ${resultClass}">`;
                    html += `<i class="fas ${icon}"></i> ${result.testName}`;
                    html += `</div>`;
                    
                    if (!result.passed && result.error) {
                        html += `<div class="error-details">`;
                        html += `<strong>خطأ:</strong> ${result.error}`;
                        html += `</div>`;
                    }
                    
                    if (!result.passed && result.suggestion) {
                        html += `<div class="fix-suggestion">`;
                        html += `<strong>الحل المقترح:</strong> ${result.suggestion}`;
                        html += `</div>`;
                    }
                });
                
                html += `</div>`;
            });

            resultsContainer.innerHTML = html;
        }

        function updateStats() {
            document.getElementById('total-tests').textContent = totalTests;
            document.getElementById('passed-tests').textContent = passedTests;
            document.getElementById('failed-tests').textContent = failedTests;
            
            const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
        }

        function generateTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                totalTests: totalTests,
                passedTests: passedTests,
                failedTests: failedTests,
                successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0,
                testResults: testResults
            };

            // حفظ التقرير
            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            console.log('تقرير الاختبار:', report);
        }
    </script>
</body>
</html>
