// Orders Management System
class OrdersManager {
    constructor() {
        this.orders = [];
        this.customers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            status: '',
            dateFrom: '',
            dateTo: '',
            search: '',
            region: '',
            customer: '',
            showAll: true
        };
        this.storageKey = 'iraqi_delivery_orders';
        this.customersKey = 'iraqi_delivery_customers';
        this.init();
    }

    init() {
        this.loadFromStorage();
        this.setupEventListeners();
        this.loadSampleData();
    }

    // Storage Management
    saveToStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.orders));
            localStorage.setItem(this.customersKey, JSON.stringify(this.customers));
        } catch (error) {
            console.error('Error saving to storage:', error);
            this.showNotification('خطأ في حفظ البيانات', 'error');
        }
    }

    loadFromStorage() {
        try {
            const savedOrders = localStorage.getItem(this.storageKey);
            const savedCustomers = localStorage.getItem(this.customersKey);

            if (savedOrders) {
                this.orders = JSON.parse(savedOrders);
            }

            if (savedCustomers) {
                this.customers = JSON.parse(savedCustomers);
            }
        } catch (error) {
            console.error('Error loading from storage:', error);
            this.orders = [];
            this.customers = [];
        }
    }

    addOrder(order) {
        // Add order to list
        this.orders.unshift(order);

        // Add customer if not exists
        this.addCustomerIfNotExists(order.customerName);

        // Save to storage
        this.saveToStorage();

        // Update UI
        this.renderOrders();
        this.updateStats();
        this.updateCustomerFilter();
    }

    addCustomerIfNotExists(customerName) {
        if (!this.customers.find(c => c.name === customerName)) {
            this.customers.push({
                id: Date.now(),
                name: customerName,
                createdAt: new Date().toISOString()
            });
        }
    }

    setupEventListeners() {
        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'orders-search') {
                this.filters.search = e.target.value;
                this.filterOrders();
            }
        });

        // Filter by status, region, customer
        document.addEventListener('change', (e) => {
            if (e.target.id === 'status-filter') {
                this.filters.status = e.target.value;
                this.filterOrders();
            }
            if (e.target.id === 'region-filter') {
                this.filters.region = e.target.value;
                this.filterOrders();
            }
            if (e.target.id === 'customer-filter') {
                this.filters.customer = e.target.value;
                this.filterOrders();
            }
            if (e.target.id === 'show-all-orders') {
                this.filters.showAll = e.target.checked;
                this.filterOrders();
            }
        });

        // Date filters
        document.addEventListener('change', (e) => {
            if (e.target.id === 'date-from') {
                this.filters.dateFrom = e.target.value;
                this.filterOrders();
            }
            if (e.target.id === 'date-to') {
                this.filters.dateTo = e.target.value;
                this.filterOrders();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-order-btn') || e.target.closest('.add-order-btn')) {
                e.preventDefault();
                this.showAddOrderModal();
            }
            if (e.target.matches('.edit-order-btn') || e.target.closest('.edit-order-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.edit-order-btn').getAttribute('data-order-id');
                this.showEditOrderModal(orderId);
            }
            if (e.target.matches('.view-order-btn') || e.target.closest('.view-order-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.view-order-btn').getAttribute('data-order-id');
                this.showOrderDetails(orderId);
            }
            if (e.target.matches('.delete-order-btn') || e.target.closest('.delete-order-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.delete-order-btn').getAttribute('data-order-id');
                this.deleteOrder(orderId);
            }
            if (e.target.matches('.print-order-btn') || e.target.closest('.print-order-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.print-order-btn').getAttribute('data-order-id');
                this.printOrder(orderId);
            }
            if (e.target.matches('.status-update-btn') || e.target.closest('.status-update-btn')) {
                e.preventDefault();
                const orderId = e.target.closest('.status-update-btn').getAttribute('data-order-id');
                this.showUpdateStatusModal(orderId);
            }
            if (e.target.matches('.clear-filters-btn') || e.target.closest('.clear-filters-btn')) {
                e.preventDefault();
                this.clearFilters();
            }
        });

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.getAttribute('data-page'));
                this.currentPage = page;
                this.renderOrders();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const ordersHTML = `
            <div class="orders-header">
                <div class="orders-title">
                    <h1>إدارة الطلبات</h1>
                    <p>عرض وإدارة جميع طلبات التوصيل</p>
                </div>
                <div class="orders-actions">
                    <button class="neu-btn primary add-order-btn">
                        <i class="fas fa-plus"></i>
                        طلب جديد
                    </button>
                    <button class="neu-btn info activity-log-btn" onclick="window.ActivityLogger.showActivityLogModal()">
                        <i class="fas fa-history"></i>
                        سجل الحركات
                    </button>
                    <button class="neu-btn secondary export-btn">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="orders-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="orders-search" class="neu-input" placeholder="رقم الطلب، رقم الوصل، اسم العميل، رقم الهاتف...">
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلق</option>
                            <option value="assigned">معين</option>
                            <option value="picked_up">تم الاستلام</option>
                            <option value="in_transit">قيد التوصيل</option>
                            <option value="delivered">تم التسليم</option>
                            <option value="returned">مرتجع</option>
                            <option value="cancelled">ملغي</option>
                            <option value="postponed">مؤجل</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>المنطقة:</label>
                        <select id="region-filter" class="neu-select">
                            <option value="">جميع المناطق</option>
                            <option value="1">بغداد - الكرخ</option>
                            <option value="2">بغداد - الرصافة</option>
                            <option value="3">بغداد - الصدر</option>
                            <option value="4">بغداد - الكاظمية</option>
                            <option value="5">بغداد - الأعظمية</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>العميل:</label>
                        <select id="customer-filter" class="neu-select">
                            <option value="">جميع العملاء</option>
                        </select>
                    </div>
                </div>
                <div class="filters-row">
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" id="date-from" class="neu-input">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" id="date-to" class="neu-input">
                    </div>
                    <div class="filter-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="show-all-orders" checked>
                            <label for="show-all-orders">عرض جميع الطلبات</label>
                        </div>
                    </div>
                    <div class="filter-group">
                        <button class="neu-btn secondary clear-filters-btn">
                            <i class="fas fa-times"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>

            <div class="orders-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-orders">0</h3>
                        <p>إجمالي الطلبات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="pending-orders">0</h3>
                        <p>طلبات معلقة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="delivered-orders">0</h3>
                        <p>تم التسليم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="postponed-orders">0</h3>
                        <p>مؤجلة</p>
                    </div>
                </div>
            </div>

            <div class="orders-table-container">
                <div class="table-header">
                    <h3>قائمة الطلبات</h3>
                    <div class="table-actions">
                        <button class="neu-btn small refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table class="orders-table" id="orders-table">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>رقم الوصل</th>
                                <th>العميل</th>
                                <th>المستلم</th>
                                <th>العنوان والمنطقة</th>
                                <th>المبالغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="orders-tbody">
                            <!-- Orders will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination" id="orders-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = ordersHTML;
        this.updateCustomerFilter();
        this.renderOrders();
        this.updateStats();
    }

    loadSampleData() {
        this.orders = [
            {
                id: 'ORD-2024-001',
                receiptNumber: 'IQ-2024-000001',
                customer: 'فاطمة أحمد',
                customerPhone: '+9647501111111',
                senderName: 'فاطمة أحمد',
                recipient: 'منى سالم',
                recipientPhone: '+9647506666666',
                region: 'بغداد - الكرخ',
                regionId: 1,
                driver: 'أحمد محمد المندوب',
                driverId: 1,
                amount: 5000,
                codAmount: 0,
                totalAmount: 5000,
                status: 'delivered',
                priority: 'normal',
                deliveryType: 'standard',
                packageDescription: 'هدية عيد ميلاد',
                specialInstructions: 'يرجى التعامل بحذر',
                senderAddress: 'حي الكرادة، شارع أبو نواس',
                recipientAddress: 'حي الجادرية، شارع الجامعة',
                createdAt: '2024-12-25 10:30:00',
                deliveredAt: '2024-12-25 15:45:00'
            },
            {
                id: 'ORD-2024-002',
                receiptNumber: 'IQ-2024-000002',
                customer: 'سارة محمد',
                customerPhone: '+9647502222222',
                senderName: 'سارة محمد',
                recipient: 'نورا عبدالله',
                recipientPhone: '+9647507777777',
                region: 'بغداد - الرصافة',
                regionId: 2,
                driver: 'محمد علي المندوب',
                driverId: 2,
                amount: 5000,
                codAmount: 50000,
                totalAmount: 55000,
                status: 'in_transit',
                priority: 'urgent',
                deliveryType: 'same_day',
                packageDescription: 'مستندات مهمة',
                specialInstructions: 'عاجل جداً',
                senderAddress: 'حي الجادرية، شارع الجامعة',
                recipientAddress: 'حي الكاظمية، شارع الإمام موسى',
                createdAt: '2024-12-26 09:15:00',
                pickedUpAt: '2024-12-26 10:30:00'
            },
            {
                id: 'ORD-2024-003',
                receiptNumber: 'IQ-2024-000003',
                customer: 'مطعم بغداد الأصيل',
                customerPhone: '+9647503333333',
                recipient: 'خالد أحمد',
                recipientPhone: '+9647508888888',
                region: 'بغداد - الأعظمية',
                regionId: 5,
                driver: 'أحمد محمد المندوب',
                driverId: 1,
                amount: 4000,
                codAmount: 25000,
                totalAmount: 29000,
                status: 'delivered',
                priority: 'express',
                deliveryType: 'same_day',
                packageDescription: 'طلب طعام',
                senderAddress: 'شارع المتنبي، الرصافة',
                recipientAddress: 'حي الأعظمية، شارع الإمام الأعظم',
                createdAt: '2024-12-26 12:00:00',
                deliveredAt: '2024-12-26 13:30:00'
            },
            {
                id: 'ORD-2024-004',
                receiptNumber: 'IQ-2024-000004',
                customer: 'شركة التوصيل السريع',
                customerPhone: '+9647504444444',
                recipient: 'رانيا محمد',
                recipientPhone: '+9647509999999',
                region: 'بغداد - الصدر',
                regionId: 3,
                driver: null,
                driverId: null,
                amount: 3500,
                codAmount: 0,
                totalAmount: 3500,
                status: 'pending',
                priority: 'normal',
                deliveryType: 'standard',
                packageDescription: 'منتجات إلكترونية',
                senderAddress: 'حي الجادرية، مجمع الأعمال',
                recipientAddress: 'حي الصدر، شارع الثورة',
                createdAt: '2024-12-27 08:45:00'
            },
            {
                id: 'ORD-2024-005',
                receiptNumber: 'IQ-2024-000005',
                customer: 'صيدلية دجلة',
                customerPhone: '+9647505555555',
                recipient: 'عبدالرحمن سعد',
                recipientPhone: '+9647500000000',
                region: 'بغداد - الكاظمية',
                regionId: 4,
                driver: 'عبدالله حسن المندوب',
                driverId: 3,
                amount: 4500,
                codAmount: 0,
                totalAmount: 4500,
                status: 'picked_up',
                priority: 'urgent',
                deliveryType: 'same_day',
                packageDescription: 'أدوية',
                senderAddress: 'شارع الرشيد، الكرخ',
                recipientAddress: 'حي الدورة، شارع الوحدة',
                createdAt: '2024-12-27 11:20:00',
                pickedUpAt: '2024-12-27 12:00:00'
            }
        ];
    }

    renderOrders() {
        const tbody = document.getElementById('orders-tbody');
        if (!tbody) return;

        const filteredOrders = this.getFilteredOrders();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const ordersToShow = filteredOrders.slice(startIndex, endIndex);

        tbody.innerHTML = ordersToShow.map(order => `
            <tr>
                <td>
                    <div class="order-id">
                        <strong>${order.id}</strong>
                        <span class="priority ${order.priority}">${this.getPriorityText(order.priority)}</span>
                    </div>
                </td>
                <td>
                    <div class="receipt-number">
                        <strong>${order.receiptNumber || 'غير محدد'}</strong>
                        ${order.receiptNumber ? `<button class="barcode-btn" onclick="window.BarcodeGenerator.generateBarcodeForOrder('${order.id}')" title="عرض الباركود">
                            <i class="fas fa-barcode"></i>
                        </button>` : ''}
                    </div>
                </td>
                <td>
                    <div class="customer-info">
                        <strong>${order.customerName || order.customer || 'غير محدد'}</strong>
                        <small>${order.senderPhone || order.customerPhone || ''}</small>
                    </div>
                </td>
                <td>
                    <div class="recipient-info">
                        <strong>${order.recipientName || order.recipient || 'غير محدد'}</strong>
                        <small>${order.recipientPhone || ''}</small>
                    </div>
                </td>
                <td>
                    <div class="address-info">
                        <span title="${order.address || order.recipientAddress || ''}">${(order.address || order.recipientAddress || '').substring(0, 30)}${(order.address || order.recipientAddress || '').length > 30 ? '...' : ''}</span>
                        <small>${order.region}</small>
                    </div>
                </td>
                <td>
                    <div class="amount-info">
                        <strong>السعر: ${this.formatCurrency(order.orderPrice || 0)}</strong>
                        <small>التوصيل: ${this.formatCurrency(order.deliveryFee || 0)}</small>
                        <strong>المجموع: ${this.formatCurrency(order.totalAmount || 0)}</strong>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${order.status}">${this.getStatusText(order.status)}</span>
                </td>
                <td>
                    <div class="date-info">
                        <strong>${this.formatDate(order.createdAt)}</strong>
                        <small>${this.formatTime(order.createdAt)}</small>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-order-btn" data-order-id="${order.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-order-btn" data-order-id="${order.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn status-update-btn" data-order-id="${order.id}" title="تحديث الحالة">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="action-btn print-order-btn" data-order-id="${order.id}" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="action-btn delete-order-btn" data-order-id="${order.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination(filteredOrders.length);
    }



    renderPagination(totalItems) {
        const pagination = document.getElementById('orders-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    updateStats() {
        const totalOrders = document.getElementById('total-orders');
        const pendingOrders = document.getElementById('pending-orders');
        const deliveredOrders = document.getElementById('delivered-orders');
        const postponedOrders = document.getElementById('postponed-orders');

        if (totalOrders) totalOrders.textContent = this.orders.length;
        if (pendingOrders) pendingOrders.textContent = this.orders.filter(o => o.status === 'pending').length;
        if (deliveredOrders) deliveredOrders.textContent = this.orders.filter(o => o.status === 'delivered').length;
        if (postponedOrders) postponedOrders.textContent = this.orders.filter(o => o.status === 'postponed').length;
    }

    updateCustomerFilter() {
        const customerFilter = document.getElementById('customer-filter');
        if (!customerFilter) return;

        // Clear existing options except the first one
        customerFilter.innerHTML = '<option value="">جميع العملاء</option>';

        // Add customer options
        this.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.name;
            option.textContent = customer.name;
            customerFilter.appendChild(option);
        });
    }

    getFilteredOrders() {
        let filtered = [...this.orders];

        // Filter by customer if specific customer selected
        if (this.filters.customer && !this.filters.showAll) {
            filtered = filtered.filter(order =>
                order.customerName && order.customerName.toLowerCase().includes(this.filters.customer.toLowerCase())
            );
        }

        // Filter by search term
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(order =>
                order.id.toString().includes(searchTerm) ||
                order.receiptNumber?.toLowerCase().includes(searchTerm) ||
                order.customerName?.toLowerCase().includes(searchTerm) ||
                order.senderName?.toLowerCase().includes(searchTerm) ||
                order.recipientName?.toLowerCase().includes(searchTerm) ||
                order.recipientPhone?.includes(searchTerm) ||
                order.address?.toLowerCase().includes(searchTerm)
            );
        }

        // Filter by status
        if (this.filters.status) {
            filtered = filtered.filter(order => order.status === this.filters.status);
        }

        // Filter by region
        if (this.filters.region) {
            filtered = filtered.filter(order => order.regionId == this.filters.region);
        }

        // Filter by date range
        if (this.filters.dateFrom) {
            filtered = filtered.filter(order => order.createdAt >= this.filters.dateFrom);
        }

        if (this.filters.dateTo) {
            filtered = filtered.filter(order => order.createdAt <= this.filters.dateTo + ' 23:59:59');
        }

        return filtered;
    }

    clearFilters() {
        // Reset all filters
        this.filters = {
            status: '',
            dateFrom: '',
            dateTo: '',
            search: '',
            region: '',
            customer: '',
            showAll: true
        };

        // Clear form inputs
        const searchInput = document.getElementById('orders-search');
        const statusFilter = document.getElementById('status-filter');
        const regionFilter = document.getElementById('region-filter');
        const customerFilter = document.getElementById('customer-filter');
        const dateFromInput = document.getElementById('date-from');
        const dateToInput = document.getElementById('date-to');
        const showAllCheckbox = document.getElementById('show-all-orders');

        if (searchInput) searchInput.value = '';
        if (statusFilter) statusFilter.value = '';
        if (regionFilter) regionFilter.value = '';
        if (customerFilter) customerFilter.value = '';
        if (dateFromInput) dateFromInput.value = '';
        if (dateToInput) dateToInput.value = '';
        if (showAllCheckbox) showAllCheckbox.checked = true;

        // Re-render orders
        this.filterOrders();
        this.showNotification('تم مسح جميع الفلاتر', 'info');
    }

    filterOrders() {
        this.currentPage = 1;
        this.renderOrders();
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'assigned': 'معين',
            'picked_up': 'تم الاستلام',
            'in_transit': 'قيد التوصيل',
            'delivered': 'تم التسليم',
            'returned': 'مرتجع',
            'cancelled': 'ملغي',
            'postponed': 'مؤجل'
        };
        return statuses[status] || status;
    }

    getPriorityText(priority) {
        const priorities = {
            'normal': 'عادي',
            'urgent': 'عاجل',
            'express': 'سريع'
        };
        return priorities[priority] || priority;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    formatTime(dateString) {
        return new Date(dateString).toLocaleTimeString('ar-IQ', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showAddOrderModal() {
        if (window.ModalManager) {
            window.ModalManager.showAddOrderModal();
        } else {
            alert('سيتم إضافة نافذة إنشاء طلب جديد قريباً');
        }
    }

    // Create new order with receipt number
    createOrder(orderData) {
        try {
            // Generate receipt number
            let receiptNumber = '';
            if (window.ReceiptSystem) {
                receiptNumber = window.ReceiptSystem.generateReceiptNumber();
                window.ReceiptSystem.addReceiptNumber(receiptNumber);
            } else {
                // Fallback receipt number generation
                receiptNumber = this.generateFallbackReceiptNumber();
            }

            // Create order object
            const newOrder = {
                id: this.generateOrderId(),
                receiptNumber: receiptNumber,
                customer: orderData.customer || '',
                customerPhone: orderData.customerPhone || '',
                senderName: orderData.senderName || orderData.customer || '',
                recipient: orderData.recipient || '',
                recipientPhone: orderData.recipientPhone || '',
                region: orderData.region || '',
                regionId: orderData.regionId || 1,
                driver: orderData.driver || '',
                driverId: orderData.driverId || '',
                amount: parseFloat(orderData.amount) || 0,
                codAmount: parseFloat(orderData.codAmount) || 0,
                totalAmount: parseFloat(orderData.totalAmount) || parseFloat(orderData.amount) || 0,
                status: orderData.status || 'pending',
                priority: orderData.priority || 'normal',
                deliveryType: orderData.deliveryType || 'standard',
                packageDescription: orderData.packageDescription || '',
                specialInstructions: orderData.specialInstructions || '',
                senderAddress: orderData.senderAddress || '',
                recipientAddress: orderData.recipientAddress || '',
                createdAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
                deliveredAt: null
            };

            // Add to orders array
            this.orders.unshift(newOrder);

            // Save to localStorage
            this.saveOrders();

            // Update UI
            this.renderOrders();
            this.updateStats();

            // Show success notification
            this.showNotification(`تم إنشاء الطلب بنجاح برقم الوصل: ${receiptNumber}`, 'success');

            // Generate barcode if system is available
            if (window.AdvancedBarcodeSystem) {
                setTimeout(() => {
                    window.AdvancedBarcodeSystem.generateBarcode(receiptNumber);
                }, 500);
            }

            return newOrder;

        } catch (error) {
            console.error('Error creating order:', error);
            this.showNotification('خطأ في إنشاء الطلب: ' + error.message, 'error');
            return null;
        }
    }

    // Generate fallback receipt number
    generateFallbackReceiptNumber() {
        const year = new Date().getFullYear();
        const timestamp = Date.now().toString().slice(-6);
        return `IQ-${year}-${timestamp}`;
    }

    // Generate unique order ID
    generateOrderId() {
        const year = new Date().getFullYear();
        const orderCount = this.orders.length + 1;
        return `ORD-${year}-${String(orderCount).padStart(3, '0')}`;
    }

    // Save orders to localStorage
    saveOrders() {
        try {
            localStorage.setItem('orders', JSON.stringify(this.orders));
        } catch (error) {
            console.error('Error saving orders:', error);
        }
    }

    // Load orders from localStorage
    loadOrders() {
        try {
            const savedOrders = localStorage.getItem('orders');
            if (savedOrders) {
                this.orders = JSON.parse(savedOrders);
            }
        } catch (error) {
            console.error('Error loading orders:', error);
        }
    }

    showEditOrderModal(orderId) {
        if (window.ModalManager) {
            window.ModalManager.showEditOrderModal(orderId);
        } else {
            alert(`سيتم إضافة نافذة تعديل الطلب ${orderId} قريباً`);
        }
    }

    showOrderDetails(orderId) {
        if (window.ModalManager) {
            window.ModalManager.showOrderDetailsModal(orderId);
        } else {
            alert(`سيتم إضافة نافذة تفاصيل الطلب ${orderId} قريباً`);
        }
    }

    deleteOrder(orderId) {
        if (confirm(`هل أنت متأكد من حذف الطلب ${orderId}؟`)) {
            this.orders = this.orders.filter(order => order.id !== orderId);
            this.saveToStorage();
            this.renderOrders();
            this.updateStats();
            this.showNotification('تم حذف الطلب بنجاح', 'success');
        }
    }

    showUpdateStatusModal(orderId) {
        if (window.ModalManager) {
            window.ModalManager.showUpdateOrderStatusModal(orderId);
        } else {
            // Fallback: Simple status update
            const order = this.orders.find(o => o.id === orderId);
            if (order) {
                const statuses = ['pending', 'confirmed', 'picked_up', 'in_transit', 'delivered', 'cancelled'];
                const statusNames = {
                    'pending': 'في الانتظار',
                    'confirmed': 'مؤكد',
                    'picked_up': 'تم الاستلام',
                    'in_transit': 'في الطريق',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغي'
                };

                let options = statuses.map(status =>
                    `${status === order.status ? '✓ ' : ''}${statusNames[status]} (${status})`
                ).join('\n');

                const newStatus = prompt(`اختر الحالة الجديدة للطلب ${orderId}:\n\n${options}\n\nأدخل الحالة:`);
                if (newStatus && statuses.includes(newStatus)) {
                    this.updateOrderStatus(orderId, newStatus);
                }
            }
        }
    }

    updateOrderStatus(orderId, newStatus) {
        const order = this.orders.find(o => o.id === orderId);
        if (order) {
            const oldStatus = order.status;
            order.status = newStatus;
            order.updatedAt = new Date().toISOString().slice(0, 19).replace('T', ' ');

            // Update specific timestamps based on status
            if (newStatus === 'confirmed' && !order.confirmedAt) {
                order.confirmedAt = order.updatedAt;
            } else if (newStatus === 'picked_up' && !order.pickedUpAt) {
                order.pickedUpAt = order.updatedAt;
            } else if (newStatus === 'delivered' && !order.deliveredAt) {
                order.deliveredAt = order.updatedAt;
            }

            this.saveToStorage();
            this.renderOrders();
            this.updateStats();

            // Log activity
            if (window.ActivityLogger) {
                window.ActivityLogger.logActivity(
                    'ORDER_STATUS_UPDATE',
                    `تم تحديث حالة الطلب ${orderId} من "${this.getStatusText(oldStatus)}" إلى "${this.getStatusText(newStatus)}"`,
                    { orderId, oldStatus, newStatus }
                );
            }

            this.showNotification(`تم تحديث حالة الطلب ${orderId} إلى "${this.getStatusText(newStatus)}"`, 'success');
        }
    }

    generateReceiptNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hour = String(now.getHours()).padStart(2, '0');
        const minute = String(now.getMinutes()).padStart(2, '0');
        const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');

        return `${year}${month}${day}${hour}${minute}${random}`;
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    printOrder(orderId) {
        // Use the new barcode printing functionality
        if (window.BarcodeGenerator) {
            window.BarcodeGenerator.printReceiptForOrder(orderId);
        } else {
            alert(`سيتم إضافة وظيفة طباعة الطلب ${orderId} قريباً`);
        }
    }

    // Generate barcode for order receipt number
    generateBarcodeForOrder(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order || !order.receiptNumber) {
            this.showNotification('رقم الوصل غير متوفر لهذا الطلب', 'error');
            return;
        }

        if (window.AdvancedBarcodeSystem) {
            // Open receipt manager with this receipt number
            const receiptManagerWindow = window.open('receipt-barcode-manager.html', '_blank');
            receiptManagerWindow.onload = function() {
                // Set the receipt number and generate barcode
                setTimeout(() => {
                    const barcodeInput = receiptManagerWindow.document.getElementById('barcode-data-input');
                    if (barcodeInput) {
                        barcodeInput.value = order.receiptNumber;
                        receiptManagerWindow.AdvancedBarcodeSystem.generateBarcode(order.receiptNumber);
                        receiptManagerWindow.receiptBarcodeManagerUI.switchTab('barcode');
                    }
                }, 1000);
            };
        } else {
            alert('نظام الباركود غير متوفر');
        }
    }

    // Update order with receipt number
    updateOrderReceiptNumber(orderId, receiptNumber) {
        const order = this.orders.find(o => o.id === orderId);
        if (order) {
            order.receiptNumber = receiptNumber;
            this.saveOrders();
            this.renderOrders();
            this.showNotification(`تم تحديث رقم الوصل للطلب ${orderId}`, 'success');
        }
    }

    // Search orders by receipt number
    searchByReceiptNumber(receiptNumber) {
        const order = this.orders.find(o => o.receiptNumber === receiptNumber);
        if (order) {
            // Filter to show only this order
            this.filters.search = receiptNumber;
            this.filterOrders();
            this.showNotification(`تم العثور على الطلب: ${order.id}`, 'success');
        } else {
            this.showNotification('لم يتم العثور على طلب بهذا الرقم', 'error');
        }
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Barcode Generator for Orders
class BarcodeGenerator {
    static generateBarcodeForOrder(orderId) {
        if (window.OrdersManager) {
            window.OrdersManager.generateBarcodeForOrder(orderId);
        }
    }

    static printReceiptForOrder(orderId) {
        if (!window.OrdersManager) return;

        const order = window.OrdersManager.orders.find(o => o.id === orderId);
        if (!order || !order.receiptNumber) {
            alert('رقم الوصل غير متوفر لهذا الطلب');
            return;
        }

        // Generate barcode and print
        if (window.AdvancedBarcodeSystem) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 400;
            tempCanvas.height = 200;

            window.AdvancedBarcodeSystem.generateBarcode(order.receiptNumber, 'code128', tempCanvas);

            const printWindow = window.open('', '_blank');
            const barcodeDataURL = tempCanvas.toDataURL();

            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>وصل الطلب - ${order.receiptNumber}</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                        .receipt-container { max-width: 400px; margin: 0 auto; border: 2px solid #000; padding: 20px; }
                        .company-name { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                        .order-info { margin: 15px 0; text-align: right; }
                        .barcode { margin: 20px 0; }
                        .date { font-size: 12px; color: #666; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="receipt-container">
                        <div class="company-name">شركة التوصيل العراقية</div>
                        <div class="order-info">
                            <div><strong>رقم الطلب:</strong> ${order.id}</div>
                            <div><strong>رقم الوصل:</strong> ${order.receiptNumber}</div>
                            <div><strong>العميل:</strong> ${order.customer}</div>
                            <div><strong>المستلم:</strong> ${order.recipient}</div>
                            <div><strong>المبلغ:</strong> ${order.totalAmount.toLocaleString()} د.ع</div>
                        </div>
                        <div class="barcode">
                            <img src="${barcodeDataURL}" alt="Barcode" />
                        </div>
                        <div class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-IQ')}</div>
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            };
                        };
                    </script>
                </body>
                </html>
            `);

            printWindow.document.close();
        }
    }
}

// Make BarcodeGenerator globally available
window.BarcodeGenerator = BarcodeGenerator;

// Initialize Orders Manager
window.OrdersManager = new OrdersManager();
