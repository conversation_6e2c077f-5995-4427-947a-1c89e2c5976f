// Advanced Receipt Features: Export, Import, Statistics, Archive
class AdvancedReceiptFeatures {
    constructor() {
        this.exportFormats = ['csv', 'excel', 'pdf', 'json'];
        this.importFormats = ['csv', 'excel', 'json'];
        this.statisticsCache = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeExportSystem();
        this.initializeImportSystem();
        this.initializeStatisticsSystem();
    }

    setupEventListeners() {
        // Export functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-receipts-btn') || e.target.closest('.export-receipts-btn')) {
                e.preventDefault();
                this.showExportModal();
            }
            if (e.target.matches('.import-receipts-btn') || e.target.closest('.import-receipts-btn')) {
                e.preventDefault();
                this.showImportModal();
            }
            if (e.target.matches('.generate-report-btn') || e.target.closest('.generate-report-btn')) {
                e.preventDefault();
                this.generateAdvancedReport();
            }
            if (e.target.matches('.archive-receipts-btn') || e.target.closest('.archive-receipts-btn')) {
                e.preventDefault();
                this.showArchiveModal();
            }
        });

        // File input handling
        document.addEventListener('change', (e) => {
            if (e.target.id === 'import-file-input') {
                this.handleFileImport(e.target.files[0]);
            }
        });
    }

    // Export System
    initializeExportSystem() {
        this.exportTemplates = {
            csv: {
                name: 'CSV (Comma Separated Values)',
                extension: 'csv',
                mimeType: 'text/csv',
                description: 'ملف نصي مفصول بفواصل'
            },
            excel: {
                name: 'Excel Spreadsheet',
                extension: 'xlsx',
                mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                description: 'ملف Excel قابل للتحرير'
            },
            pdf: {
                name: 'PDF Document',
                extension: 'pdf',
                mimeType: 'application/pdf',
                description: 'مستند PDF للطباعة'
            },
            json: {
                name: 'JSON Data',
                extension: 'json',
                mimeType: 'application/json',
                description: 'بيانات JSON للنسخ الاحتياطي'
            }
        };
    }

    async exportReceiptNumbers(format = 'csv', options = {}) {
        try {
            const receipts = await this.getReceiptDataForExport(options);
            
            switch (format) {
                case 'csv':
                    return this.exportToCSV(receipts, options);
                case 'excel':
                    return this.exportToExcel(receipts, options);
                case 'pdf':
                    return this.exportToPDF(receipts, options);
                case 'json':
                    return this.exportToJSON(receipts, options);
                default:
                    throw new Error('Unsupported export format');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('خطأ في التصدير: ' + error.message, 'error');
        }
    }

    async getReceiptDataForExport(options = {}) {
        // Get receipt data from API or local storage
        if (window.ReceiptAPIClient) {
            try {
                const response = await window.ReceiptAPIClient.getReceiptNumbers({
                    limit: options.limit || 1000,
                    status: options.status || '',
                    year: options.year || '',
                    search: options.search || ''
                });
                return response.receipts;
            } catch (error) {
                console.warn('API not available, using local data');
            }
        }

        // Fallback to local data
        return this.getLocalReceiptData(options);
    }

    getLocalReceiptData(options = {}) {
        const receipts = window.ReceiptSystem ? [...window.ReceiptSystem.receiptNumbers] : [];
        
        return receipts.map((receiptNumber, index) => ({
            id: index + 1,
            receipt_number: receiptNumber,
            order_id: `ORD-2024-${String(index + 1).padStart(3, '0')}`,
            format_type: 'IQ-{YEAR}-{SEQUENCE}',
            sequence_number: this.extractSequenceFromReceipt(receiptNumber),
            year_created: this.extractYearFromReceipt(receiptNumber),
            status: 'active',
            barcode_type: 'code128',
            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
            created_by: 'system'
        }));
    }

    exportToCSV(receipts, options = {}) {
        const headers = [
            'رقم الوصل',
            'رقم الطلب',
            'التنسيق',
            'الرقم التسلسلي',
            'السنة',
            'الحالة',
            'نوع الباركود',
            'تاريخ الإنشاء',
            'المنشئ'
        ];

        const csvContent = [
            headers.join(','),
            ...receipts.map(receipt => [
                receipt.receipt_number,
                receipt.order_id || '',
                receipt.format_type,
                receipt.sequence_number,
                receipt.year_created,
                receipt.status,
                receipt.barcode_type,
                receipt.created_at,
                receipt.created_by || ''
            ].join(','))
        ].join('\n');

        this.downloadFile(csvContent, `receipt_numbers_${this.getTimestamp()}.csv`, 'text/csv');
        this.showNotification('تم تصدير أرقام الوصل بصيغة CSV', 'success');
    }

    exportToExcel(receipts, options = {}) {
        // Create Excel-like structure (simplified)
        const excelData = {
            worksheets: [{
                name: 'أرقام الوصل',
                data: [
                    ['رقم الوصل', 'رقم الطلب', 'التنسيق', 'الرقم التسلسلي', 'السنة', 'الحالة', 'نوع الباركود', 'تاريخ الإنشاء', 'المنشئ'],
                    ...receipts.map(receipt => [
                        receipt.receipt_number,
                        receipt.order_id || '',
                        receipt.format_type,
                        receipt.sequence_number,
                        receipt.year_created,
                        receipt.status,
                        receipt.barcode_type,
                        receipt.created_at,
                        receipt.created_by || ''
                    ])
                ]
            }]
        };

        // Convert to CSV for now (Excel export would require additional library)
        this.exportToCSV(receipts, options);
        this.showNotification('تم تصدير أرقام الوصل (CSV format)', 'success');
    }

    exportToPDF(receipts, options = {}) {
        // Create PDF content
        const pdfContent = this.generatePDFContent(receipts, options);
        
        // Open in new window for printing
        const printWindow = window.open('', '_blank');
        printWindow.document.write(pdfContent);
        printWindow.document.close();
        
        this.showNotification('تم فتح تقرير PDF للطباعة', 'success');
    }

    generatePDFContent(receipts, options = {}) {
        const title = options.title || 'تقرير أرقام الوصل';
        const date = new Date().toLocaleDateString('ar-IQ');
        
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>${title}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .report-title { font-size: 18px; margin-bottom: 5px; }
                    .report-date { font-size: 14px; color: #666; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">شركة التوصيل العراقية</div>
                    <div class="report-title">${title}</div>
                    <div class="report-date">تاريخ التقرير: ${date}</div>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>رقم الوصل</th>
                            <th>رقم الطلب</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${receipts.map(receipt => `
                            <tr>
                                <td>${receipt.receipt_number}</td>
                                <td>${receipt.order_id || '-'}</td>
                                <td>${this.getStatusText(receipt.status)}</td>
                                <td>${receipt.created_at}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                <div class="footer">
                    <p>إجمالي أرقام الوصل: ${receipts.length}</p>
                    <p>تم إنشاء التقرير في: ${new Date().toLocaleString('ar-IQ')}</p>
                </div>
                
                <script>
                    window.onload = function() {
                        window.print();
                    };
                </script>
            </body>
            </html>
        `;
    }

    exportToJSON(receipts, options = {}) {
        const jsonData = {
            export_info: {
                timestamp: new Date().toISOString(),
                total_records: receipts.length,
                format: 'receipt_numbers_backup',
                version: '1.0'
            },
            receipts: receipts
        };

        const jsonContent = JSON.stringify(jsonData, null, 2);
        this.downloadFile(jsonContent, `receipt_backup_${this.getTimestamp()}.json`, 'application/json');
        this.showNotification('تم تصدير النسخة الاحتياطية بصيغة JSON', 'success');
    }

    // Import System
    initializeImportSystem() {
        this.importValidators = {
            csv: this.validateCSVImport.bind(this),
            excel: this.validateExcelImport.bind(this),
            json: this.validateJSONImport.bind(this)
        };
    }

    async handleFileImport(file) {
        if (!file) return;

        const fileExtension = file.name.split('.').pop().toLowerCase();
        const supportedFormats = ['csv', 'xlsx', 'xls', 'json'];

        if (!supportedFormats.includes(fileExtension)) {
            this.showNotification('صيغة الملف غير مدعومة', 'error');
            return;
        }

        try {
            const fileContent = await this.readFile(file);
            const importData = await this.parseImportFile(fileContent, fileExtension);
            
            if (importData && importData.length > 0) {
                await this.processImportData(importData);
            } else {
                this.showNotification('لا توجد بيانات صالحة في الملف', 'error');
            }
        } catch (error) {
            console.error('Import error:', error);
            this.showNotification('خطأ في استيراد الملف: ' + error.message, 'error');
        }
    }

    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }

    async parseImportFile(content, format) {
        switch (format) {
            case 'csv':
                return this.parseCSV(content);
            case 'xlsx':
            case 'xls':
                return this.parseExcel(content);
            case 'json':
                return this.parseJSON(content);
            default:
                throw new Error('صيغة غير مدعومة');
        }
    }

    parseCSV(content) {
        const lines = content.split('\n').filter(line => line.trim());
        if (lines.length < 2) throw new Error('الملف فارغ أو لا يحتوي على بيانات');

        const headers = lines[0].split(',').map(h => h.trim());
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',').map(v => v.trim());
            if (values.length >= headers.length) {
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index];
                });
                data.push(row);
            }
        }

        return data;
    }

    parseExcel(content) {
        // For now, treat as CSV (would need library like SheetJS for proper Excel parsing)
        return this.parseCSV(content);
    }

    parseJSON(content) {
        try {
            const data = JSON.parse(content);
            if (data.receipts && Array.isArray(data.receipts)) {
                return data.receipts;
            } else if (Array.isArray(data)) {
                return data;
            } else {
                throw new Error('تنسيق JSON غير صحيح');
            }
        } catch (error) {
            throw new Error('خطأ في تحليل ملف JSON');
        }
    }

    async processImportData(data) {
        const validRecords = [];
        const errors = [];

        for (let i = 0; i < data.length; i++) {
            try {
                const record = this.validateImportRecord(data[i]);
                if (record) {
                    validRecords.push(record);
                }
            } catch (error) {
                errors.push(`السطر ${i + 1}: ${error.message}`);
            }
        }

        if (validRecords.length > 0) {
            await this.importValidRecords(validRecords);
            this.showNotification(`تم استيراد ${validRecords.length} رقم وصل بنجاح`, 'success');
        }

        if (errors.length > 0) {
            console.warn('Import errors:', errors);
            this.showNotification(`تم تجاهل ${errors.length} سجل بسبب أخطاء`, 'warning');
        }
    }

    validateImportRecord(record) {
        const receiptNumber = record.receipt_number || record['رقم الوصل'];
        
        if (!receiptNumber) {
            throw new Error('رقم الوصل مطلوب');
        }

        if (!this.validateReceiptFormat(receiptNumber)) {
            throw new Error('تنسيق رقم الوصل غير صحيح');
        }

        return {
            receipt_number: receiptNumber,
            order_id: record.order_id || record['رقم الطلب'] || null,
            status: record.status || record['الحالة'] || 'active',
            created_by: 'import'
        };
    }

    async importValidRecords(records) {
        if (window.ReceiptAPIClient) {
            try {
                await window.ReceiptAPIClient.importReceiptNumbers(records);
                return;
            } catch (error) {
                console.warn('API import failed, using local storage');
            }
        }

        // Fallback to local storage
        records.forEach(record => {
            if (window.ReceiptSystem) {
                window.ReceiptSystem.addReceiptNumber(record.receipt_number);
            }
        });
    }

    // Statistics System
    initializeStatisticsSystem() {
        this.statisticsTypes = {
            daily: 'إحصائيات يومية',
            weekly: 'إحصائيات أسبوعية',
            monthly: 'إحصائيات شهرية',
            yearly: 'إحصائيات سنوية'
        };
    }

    async generateAdvancedStatistics(type = 'monthly', options = {}) {
        try {
            const cacheKey = `stats_${type}_${JSON.stringify(options)}`;
            
            if (this.statisticsCache.has(cacheKey)) {
                return this.statisticsCache.get(cacheKey);
            }

            const stats = await this.calculateStatistics(type, options);
            this.statisticsCache.set(cacheKey, stats);
            
            // Cache for 5 minutes
            setTimeout(() => {
                this.statisticsCache.delete(cacheKey);
            }, 5 * 60 * 1000);

            return stats;
        } catch (error) {
            console.error('Statistics error:', error);
            throw error;
        }
    }

    async calculateStatistics(type, options = {}) {
        const receipts = await this.getReceiptDataForExport(options);
        const now = new Date();
        
        const stats = {
            type: type,
            period: this.getPeriodInfo(type, now),
            total_receipts: receipts.length,
            status_breakdown: this.calculateStatusBreakdown(receipts),
            format_breakdown: this.calculateFormatBreakdown(receipts),
            yearly_breakdown: this.calculateYearlyBreakdown(receipts),
            growth_rate: this.calculateGrowthRate(receipts, type),
            usage_patterns: this.calculateUsagePatterns(receipts),
            generated_at: now.toISOString()
        };

        return stats;
    }

    // Utility methods
    validateReceiptFormat(receiptNumber) {
        return /^[A-Z]{2,3}-\d{4}-\d{6}$/.test(receiptNumber);
    }

    extractSequenceFromReceipt(receiptNumber) {
        const parts = receiptNumber.split('-');
        return parts.length === 3 ? parseInt(parts[2]) : 0;
    }

    extractYearFromReceipt(receiptNumber) {
        const parts = receiptNumber.split('-');
        return parts.length === 3 ? parseInt(parts[1]) : new Date().getFullYear();
    }

    getStatusText(status) {
        const statuses = {
            'active': 'نشط',
            'used': 'مستخدم',
            'cancelled': 'ملغي',
            'archived': 'مؤرشف'
        };
        return statuses[status] || status;
    }

    getTimestamp() {
        return new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // Modal methods (to be implemented)
    showExportModal() {
        alert('سيتم إضافة نافذة التصدير قريباً');
    }

    showImportModal() {
        alert('سيتم إضافة نافذة الاستيراد قريباً');
    }

    showArchiveModal() {
        alert('سيتم إضافة نافذة الأرشفة قريباً');
    }

    generateAdvancedReport() {
        this.exportReceiptNumbers('pdf', { title: 'تقرير شامل لأرقام الوصل' });
    }
}

// Initialize Advanced Receipt Features
window.AdvancedReceiptFeatures = new AdvancedReceiptFeatures();
