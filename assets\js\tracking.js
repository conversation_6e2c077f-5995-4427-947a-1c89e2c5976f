// Order Tracking System
class TrackingManager {
    constructor() {
        this.activeOrders = [];
        this.drivers = [];
        this.selectedOrder = null;
        this.map = null;
        this.markers = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Order selection
        document.addEventListener('change', (e) => {
            if (e.target.id === 'order-select') {
                this.selectedOrder = e.target.value;
                this.updateOrderDetails();
                this.updateMapView();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-tracking-btn') || e.target.closest('.refresh-tracking-btn')) {
                e.preventDefault();
                this.refreshTracking();
            }
            if (e.target.matches('.call-driver-btn') || e.target.closest('.call-driver-btn')) {
                e.preventDefault();
                this.callDriver();
            }
            if (e.target.matches('.send-message-btn') || e.target.closest('.send-message-btn')) {
                e.preventDefault();
                this.sendMessage();
            }
            if (e.target.matches('.track-all-drivers-btn') || e.target.closest('.track-all-drivers-btn')) {
                e.preventDefault();
                this.showAllDriversOnMap();
            }
            if (e.target.matches('.route-optimization-btn') || e.target.closest('.route-optimization-btn')) {
                e.preventDefault();
                this.optimizeRoutes();
            }
            if (e.target.matches('.delivery-history-btn') || e.target.closest('.delivery-history-btn')) {
                e.preventDefault();
                this.showDeliveryHistory();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const trackingHTML = `
            <div class="tracking-header">
                <div class="tracking-title">
                    <h1>تتبع الطلبات</h1>
                    <p>تتبع مباشر لحالة ومواقع الطلبات</p>
                </div>
                <div class="tracking-actions">
                    <button class="neu-btn primary refresh-tracking-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <button class="neu-btn info track-all-drivers-btn">
                        <i class="fas fa-users"></i>
                        عرض جميع المندوبين
                    </button>
                    <button class="neu-btn success route-optimization-btn">
                        <i class="fas fa-route"></i>
                        تحسين المسارات
                    </button>
                    <button class="neu-btn warning delivery-history-btn">
                        <i class="fas fa-history"></i>
                        سجل التسليم
                    </button>
                </div>
            </div>

            <div class="tracking-controls">
                <div class="control-group">
                    <label>اختيار الطلب:</label>
                    <select id="order-select" class="neu-select">
                        <option value="">اختر طلباً للتتبع...</option>
                        <!-- Orders will be loaded here -->
                    </select>
                </div>
                <div class="tracking-stats">
                    <div class="stat-item">
                        <span class="stat-label">طلبات نشطة:</span>
                        <span class="stat-value" id="active-orders-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">مندوبين متاحين:</span>
                        <span class="stat-value" id="available-drivers-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">في الطريق:</span>
                        <span class="stat-value" id="in-transit-count">0</span>
                    </div>
                </div>
            </div>

            <div class="tracking-content">
                <div class="tracking-map-container">
                    <div class="map-header">
                        <h3>الخريطة التفاعلية</h3>
                        <div class="map-controls">
                            <button class="map-btn" id="center-map-btn" title="توسيط الخريطة">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                            <button class="map-btn" id="fullscreen-btn" title="ملء الشاشة">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="map-placeholder" id="tracking-map">
                        <div class="map-mock">
                            <div class="map-legend">
                                <div class="legend-item">
                                    <div class="legend-marker pickup"></div>
                                    <span>نقطة الاستلام</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-marker delivery"></div>
                                    <span>نقطة التسليم</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-marker driver"></div>
                                    <span>موقع المندوب</span>
                                </div>
                            </div>
                            <div class="map-content">
                                <div class="mock-route">
                                    <div class="route-point pickup-point">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <span>نقطة الاستلام</span>
                                    </div>
                                    <div class="route-line"></div>
                                    <div class="route-point driver-point">
                                        <i class="fas fa-car"></i>
                                        <span>المندوب</span>
                                    </div>
                                    <div class="route-line"></div>
                                    <div class="route-point delivery-point">
                                        <i class="fas fa-flag-checkered"></i>
                                        <span>نقطة التسليم</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tracking-details">
                    <div class="order-info-card" id="order-info-card">
                        <div class="card-header">
                            <h3>معلومات الطلب</h3>
                            <span class="order-status" id="order-status-badge">اختر طلباً</span>
                        </div>
                        <div class="card-content" id="order-details-content">
                            <p class="no-selection">يرجى اختيار طلب من القائمة أعلاه لعرض التفاصيل</p>
                        </div>
                    </div>

                    <div class="driver-info-card" id="driver-info-card">
                        <div class="card-header">
                            <h3>معلومات المندوب</h3>
                            <div class="driver-actions">
                                <button class="action-btn call-driver-btn" title="اتصال">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="action-btn send-message-btn" title="رسالة">
                                    <i class="fas fa-sms"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content" id="driver-details-content">
                            <p class="no-selection">لا يوجد مندوب معين لهذا الطلب</p>
                        </div>
                    </div>

                    <div class="tracking-timeline-card">
                        <div class="card-header">
                            <h3>مراحل التسليم</h3>
                        </div>
                        <div class="timeline" id="tracking-timeline">
                            <!-- Timeline will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="active-orders-list">
                <div class="list-header">
                    <h3>الطلبات النشطة</h3>
                    <span class="orders-count" id="active-orders-badge">0 طلب</span>
                </div>
                <div class="orders-grid" id="active-orders-grid">
                    <!-- Active orders will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = trackingHTML;
        this.loadOrdersSelect();
        this.renderActiveOrders();
        this.updateStats();
        this.initMap();
    }

    loadSampleData() {
        this.activeOrders = [
            {
                id: 'ORD-2024-001',
                customer: 'فاطمة أحمد',
                recipient: 'منى سالم',
                pickupAddress: 'حي الكرادة، شارع أبو نواس',
                deliveryAddress: 'حي الجادرية، شارع الجامعة',
                status: 'in_transit',
                driverId: 1,
                driverName: 'أحمد محمد المندوب',
                driverPhone: '+9647501234569',
                estimatedTime: '15 دقيقة',
                progress: 75,
                timeline: [
                    { status: 'pending', time: '10:30', completed: true, description: 'تم إنشاء الطلب' },
                    { status: 'assigned', time: '10:45', completed: true, description: 'تم تعيين المندوب' },
                    { status: 'picked_up', time: '11:15', completed: true, description: 'تم استلام الطلب' },
                    { status: 'in_transit', time: '11:30', completed: true, description: 'في الطريق للتسليم' },
                    { status: 'delivered', time: '--:--', completed: false, description: 'تسليم الطلب' }
                ]
            },
            {
                id: 'ORD-2024-002',
                customer: 'سارة محمد',
                recipient: 'نورا عبدالله',
                pickupAddress: 'حي الجادرية، شارع الجامعة',
                deliveryAddress: 'حي الكاظمية، شارع الإمام موسى',
                status: 'picked_up',
                driverId: 2,
                driverName: 'محمد علي المندوب',
                driverPhone: '+9647501234570',
                estimatedTime: '25 دقيقة',
                progress: 50,
                timeline: [
                    { status: 'pending', time: '09:15', completed: true, description: 'تم إنشاء الطلب' },
                    { status: 'assigned', time: '09:30', completed: true, description: 'تم تعيين المندوب' },
                    { status: 'picked_up', time: '10:00', completed: true, description: 'تم استلام الطلب' },
                    { status: 'in_transit', time: '--:--', completed: false, description: 'في الطريق للتسليم' },
                    { status: 'delivered', time: '--:--', completed: false, description: 'تسليم الطلب' }
                ]
            },
            {
                id: 'ORD-2024-003',
                customer: 'مطعم بغداد الأصيل',
                recipient: 'خالد أحمد',
                pickupAddress: 'شارع المتنبي، الرصافة',
                deliveryAddress: 'حي الأعظمية، شارع الإمام الأعظم',
                status: 'assigned',
                driverId: 3,
                driverName: 'عبدالله حسن المندوب',
                driverPhone: '+9647501234571',
                estimatedTime: '10 دقائق',
                progress: 25,
                timeline: [
                    { status: 'pending', time: '12:00', completed: true, description: 'تم إنشاء الطلب' },
                    { status: 'assigned', time: '12:05', completed: true, description: 'تم تعيين المندوب' },
                    { status: 'picked_up', time: '--:--', completed: false, description: 'استلام الطلب' },
                    { status: 'in_transit', time: '--:--', completed: false, description: 'في الطريق للتسليم' },
                    { status: 'delivered', time: '--:--', completed: false, description: 'تسليم الطلب' }
                ]
            }
        ];

        this.drivers = [
            { id: 1, name: 'أحمد محمد المندوب', phone: '+9647501234569', available: false, lat: 33.3152, lng: 44.3661 },
            { id: 2, name: 'محمد علي المندوب', phone: '+9647501234570', available: false, lat: 33.3128, lng: 44.3615 },
            { id: 3, name: 'عبدالله حسن المندوب', phone: '+9647501234571', available: false, lat: 33.3203, lng: 44.3719 }
        ];
    }

    loadOrdersSelect() {
        const select = document.getElementById('order-select');
        if (!select) return;

        select.innerHTML = '<option value="">اختر طلباً للتتبع...</option>' +
            this.activeOrders.map(order => 
                `<option value="${order.id}">${order.id} - ${order.customer}</option>`
            ).join('');
    }

    updateOrderDetails() {
        const order = this.activeOrders.find(o => o.id === this.selectedOrder);
        const statusBadge = document.getElementById('order-status-badge');
        const detailsContent = document.getElementById('order-details-content');
        const driverContent = document.getElementById('driver-details-content');
        const timeline = document.getElementById('tracking-timeline');

        if (!order) {
            if (statusBadge) statusBadge.textContent = 'اختر طلباً';
            if (detailsContent) detailsContent.innerHTML = '<p class="no-selection">يرجى اختيار طلب من القائمة أعلاه لعرض التفاصيل</p>';
            if (driverContent) driverContent.innerHTML = '<p class="no-selection">لا يوجد مندوب معين لهذا الطلب</p>';
            if (timeline) timeline.innerHTML = '';
            return;
        }

        // Update status badge
        if (statusBadge) {
            statusBadge.textContent = this.getStatusText(order.status);
            statusBadge.className = `order-status ${order.status}`;
        }

        // Update order details
        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="detail-row">
                    <span class="label">رقم الطلب:</span>
                    <span class="value">${order.id}</span>
                </div>
                <div class="detail-row">
                    <span class="label">العميل:</span>
                    <span class="value">${order.customer}</span>
                </div>
                <div class="detail-row">
                    <span class="label">المستلم:</span>
                    <span class="value">${order.recipient}</span>
                </div>
                <div class="detail-row">
                    <span class="label">من:</span>
                    <span class="value">${order.pickupAddress}</span>
                </div>
                <div class="detail-row">
                    <span class="label">إلى:</span>
                    <span class="value">${order.deliveryAddress}</span>
                </div>
                <div class="detail-row">
                    <span class="label">الوقت المتوقع:</span>
                    <span class="value">${order.estimatedTime}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${order.progress}%"></div>
                    <span class="progress-text">${order.progress}%</span>
                </div>
            `;
        }

        // Update driver details
        if (driverContent && order.driverId) {
            driverContent.innerHTML = `
                <div class="driver-profile">
                    <div class="driver-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="driver-info">
                        <h4>${order.driverName}</h4>
                        <p>${order.driverPhone}</p>
                    </div>
                </div>
                <div class="driver-status">
                    <span class="status-indicator active"></span>
                    <span>نشط</span>
                </div>
            `;
        }

        // Update timeline
        if (timeline) {
            timeline.innerHTML = order.timeline.map(step => `
                <div class="timeline-item ${step.completed ? 'completed' : 'pending'}">
                    <div class="timeline-marker">
                        <i class="fas fa-${step.completed ? 'check' : 'clock'}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-time">${step.time}</div>
                        <div class="timeline-description">${step.description}</div>
                    </div>
                </div>
            `).join('');
        }
    }

    renderActiveOrders() {
        const grid = document.getElementById('active-orders-grid');
        const badge = document.getElementById('active-orders-badge');

        if (badge) badge.textContent = `${this.activeOrders.length} طلب`;

        if (!grid) return;

        grid.innerHTML = this.activeOrders.map(order => `
            <div class="order-card ${order.id === this.selectedOrder ? 'selected' : ''}" data-order-id="${order.id}">
                <div class="order-header">
                    <span class="order-id">${order.id}</span>
                    <span class="order-status ${order.status}">${this.getStatusText(order.status)}</span>
                </div>
                <div class="order-info">
                    <div class="customer-name">${order.customer}</div>
                    <div class="recipient-name">→ ${order.recipient}</div>
                </div>
                <div class="order-progress">
                    <div class="progress-bar small">
                        <div class="progress-fill" style="width: ${order.progress}%"></div>
                    </div>
                    <span class="eta">${order.estimatedTime}</span>
                </div>
            </div>
        `).join('');

        // Add click handlers for order cards
        grid.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('click', () => {
                const orderId = card.getAttribute('data-order-id');
                document.getElementById('order-select').value = orderId;
                this.selectedOrder = orderId;
                this.updateOrderDetails();
                this.updateMapView();
                this.renderActiveOrders(); // Re-render to update selection
            });
        });
    }

    updateStats() {
        const activeCount = document.getElementById('active-orders-count');
        const availableCount = document.getElementById('available-drivers-count');
        const transitCount = document.getElementById('in-transit-count');

        if (activeCount) activeCount.textContent = this.activeOrders.length;
        if (availableCount) availableCount.textContent = this.drivers.filter(d => d.available).length;
        if (transitCount) transitCount.textContent = this.activeOrders.filter(o => o.status === 'in_transit').length;
    }

    initMap() {
        // Initialize mock map
        const mapElement = document.getElementById('tracking-map');
        if (mapElement) {
            // Add click handlers for map controls
            const centerBtn = document.getElementById('center-map-btn');
            const fullscreenBtn = document.getElementById('fullscreen-btn');

            if (centerBtn) {
                centerBtn.addEventListener('click', () => {
                    alert('سيتم توسيط الخريطة على الطلب المحدد');
                });
            }

            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', () => {
                    mapElement.classList.toggle('fullscreen');
                });
            }
        }
    }

    updateMapView() {
        // Update map view for selected order
        if (this.selectedOrder) {
            console.log(`تحديث الخريطة للطلب: ${this.selectedOrder}`);
        }
    }

    refreshTracking() {
        // Simulate refresh
        alert('تم تحديث بيانات التتبع');
        this.updateOrderDetails();
        this.renderActiveOrders();
        this.updateStats();
    }

    callDriver() {
        const order = this.activeOrders.find(o => o.id === this.selectedOrder);
        if (order && order.driverPhone) {
            alert(`سيتم الاتصال بالمندوب: ${order.driverPhone}`);
        } else {
            alert('لا يوجد مندوب معين لهذا الطلب');
        }
    }

    sendMessage() {
        const order = this.activeOrders.find(o => o.id === this.selectedOrder);
        if (order && order.driverPhone) {
            const message = prompt(`إرسال رسالة للمندوب ${order.driverName}:\n\nأدخل الرسالة:`);
            if (message) {
                // Here you would integrate with SMS service
                alert(`تم إرسال الرسالة: "${message}" للمندوب ${order.driverName}`);

                // Log activity
                if (window.ActivityLogger) {
                    window.ActivityLogger.logActivity(
                        'MESSAGE_SENT',
                        `تم إرسال رسالة للمندوب ${order.driverName} للطلب ${order.id}`,
                        { orderId: order.id, driverId: order.driverId, message }
                    );
                }
            }
        } else {
            alert('لا يوجد مندوب معين لهذا الطلب');
        }
    }

    showAllDriversOnMap() {
        // Clear existing markers
        this.clearMarkers();

        // Add all drivers to map
        this.drivers.forEach(driver => {
            if (driver.lat && driver.lng) {
                const marker = {
                    lat: driver.lat,
                    lng: driver.lng,
                    title: driver.name,
                    type: 'driver',
                    available: driver.available,
                    phone: driver.phone
                };
                this.addMarker(marker);
            }
        });

        // Update map view to show all drivers
        this.fitMapToMarkers();
        this.showNotification(`تم عرض ${this.drivers.length} مندوب على الخريطة`, 'info');
    }

    optimizeRoutes() {
        const activeOrders = this.activeOrders.filter(order =>
            order.status === 'confirmed' || order.status === 'picked_up'
        );

        if (activeOrders.length === 0) {
            alert('لا توجد طلبات نشطة لتحسين المسارات');
            return;
        }

        // Simple route optimization simulation
        const optimizedRoutes = this.calculateOptimalRoutes(activeOrders);

        alert(`تم تحسين المسارات لـ ${activeOrders.length} طلب\nتوفير متوقع في الوقت: ${optimizedRoutes.timeSaved} دقيقة\nتوفير في المسافة: ${optimizedRoutes.distanceSaved} كم`);

        // Log activity
        if (window.ActivityLogger) {
            window.ActivityLogger.logActivity(
                'ROUTE_OPTIMIZED',
                `تم تحسين المسارات لـ ${activeOrders.length} طلب`,
                { ordersCount: activeOrders.length, timeSaved: optimizedRoutes.timeSaved }
            );
        }
    }

    calculateOptimalRoutes(orders) {
        // Simplified route optimization algorithm
        const timeSaved = Math.floor(Math.random() * 30) + 10; // 10-40 minutes
        const distanceSaved = Math.floor(Math.random() * 15) + 5; // 5-20 km

        return {
            timeSaved,
            distanceSaved,
            optimizedOrders: orders
        };
    }

    showDeliveryHistory() {
        const deliveredOrders = this.getDeliveredOrdersToday();

        if (deliveredOrders.length === 0) {
            alert('لا توجد طلبات مسلمة اليوم');
            return;
        }

        let historyText = `سجل التسليم لليوم (${deliveredOrders.length} طلب):\n\n`;
        deliveredOrders.forEach(order => {
            historyText += `${order.id} - ${order.customer} - ${order.deliveredAt}\n`;
        });

        alert(historyText);
    }

    getDeliveredOrdersToday() {
        const today = new Date().toISOString().split('T')[0];
        return this.activeOrders.filter(order =>
            order.status === 'delivered' &&
            order.deliveredAt &&
            order.deliveredAt.startsWith(today)
        );
    }

    clearMarkers() {
        this.markers = [];
        // In a real implementation, you would clear Google Maps markers here
    }

    addMarker(markerData) {
        this.markers.push(markerData);
        // In a real implementation, you would add Google Maps marker here
    }

    fitMapToMarkers() {
        // In a real implementation, you would fit Google Maps bounds to show all markers
        console.log('Fitting map to show all markers');
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'assigned': 'معين',
            'picked_up': 'تم الاستلام',
            'in_transit': 'قيد التوصيل',
            'delivered': 'تم التسليم'
        };
        return statuses[status] || status;
    }
}

// Initialize Tracking Manager
window.TrackingManager = new TrackingManager();
