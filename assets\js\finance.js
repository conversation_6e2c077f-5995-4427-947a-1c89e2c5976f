// Finance Management System
class FinanceManager {
    constructor() {
        this.transactions = [];
        this.currentPeriod = 'month';
        this.selectedDate = new Date();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Period filter
        document.addEventListener('change', (e) => {
            if (e.target.id === 'period-filter') {
                this.currentPeriod = e.target.value;
                this.updateFinancialData();
            }
            if (e.target.id === 'date-filter') {
                this.selectedDate = new Date(e.target.value);
                this.updateFinancialData();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-report-btn') || e.target.closest('.export-report-btn')) {
                e.preventDefault();
                this.exportReport();
            }
            if (e.target.matches('.view-transaction-btn') || e.target.closest('.view-transaction-btn')) {
                e.preventDefault();
                const transactionId = e.target.closest('.view-transaction-btn').getAttribute('data-transaction-id');
                this.showTransactionDetails(transactionId);
            }
            if (e.target.matches('.generate-invoice-btn') || e.target.closest('.generate-invoice-btn')) {
                e.preventDefault();
                this.showGenerateInvoiceModal();
            }
            if (e.target.matches('.add-payment-btn') || e.target.closest('.add-payment-btn')) {
                e.preventDefault();
                this.showAddPaymentModal();
            }
            if (e.target.matches('.driver-commission-btn') || e.target.closest('.driver-commission-btn')) {
                e.preventDefault();
                this.showDriverCommissionReport();
            }
            if (e.target.matches('.customer-statement-btn') || e.target.closest('.customer-statement-btn')) {
                e.preventDefault();
                this.showCustomerStatementModal();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const financeHTML = `
            <div class="finance-header">
                <div class="finance-title">
                    <h1>الإدارة المالية</h1>
                    <p>تقارير مالية وحسابات الشركة</p>
                </div>
                <div class="finance-actions">
                    <button class="neu-btn primary generate-invoice-btn">
                        <i class="fas fa-file-invoice"></i>
                        إنشاء فاتورة
                    </button>
                    <button class="neu-btn success add-payment-btn">
                        <i class="fas fa-money-bill-wave"></i>
                        إضافة دفعة
                    </button>
                    <button class="neu-btn info driver-commission-btn">
                        <i class="fas fa-users"></i>
                        عمولات المندوبين
                    </button>
                    <button class="neu-btn warning customer-statement-btn">
                        <i class="fas fa-file-alt"></i>
                        كشف حساب عميل
                    </button>
                    <button class="neu-btn secondary export-report-btn">
                        <i class="fas fa-file-export"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>

            <div class="finance-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>الفترة:</label>
                        <select id="period-filter" class="neu-select">
                            <option value="day">يومي</option>
                            <option value="week">أسبوعي</option>
                            <option value="month" selected>شهري</option>
                            <option value="quarter">ربع سنوي</option>
                            <option value="year">سنوي</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>التاريخ:</label>
                        <input type="date" id="date-filter" class="neu-input" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                </div>
            </div>

            <div class="finance-overview">
                <div class="overview-card revenue">
                    <div class="overview-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-revenue">0 د.ع</h3>
                        <p>إجمالي الإيرادات</p>
                        <span class="change-indicator positive" id="revenue-change">+0%</span>
                    </div>
                </div>
                <div class="overview-card expenses">
                    <div class="overview-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-expenses">0 د.ع</h3>
                        <p>إجمالي المصروفات</p>
                        <span class="change-indicator negative" id="expenses-change">+0%</span>
                    </div>
                </div>
                <div class="overview-card profit">
                    <div class="overview-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="net-profit">0 د.ع</h3>
                        <p>صافي الربح</p>
                        <span class="change-indicator positive" id="profit-change">+0%</span>
                    </div>
                </div>
                <div class="overview-card commissions">
                    <div class="overview-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-commissions">0 د.ع</h3>
                        <p>عمولات المندوبين</p>
                        <span class="change-indicator neutral" id="commissions-change">+0%</span>
                    </div>
                </div>
            </div>

            <div class="finance-charts">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>الإيرادات والمصروفات</h3>
                    </div>
                    <div class="chart-placeholder">
                        <canvas id="revenue-chart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>توزيع الإيرادات</h3>
                    </div>
                    <div class="chart-placeholder">
                        <canvas id="distribution-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="finance-breakdown">
                <div class="breakdown-section">
                    <h3>تفصيل الإيرادات</h3>
                    <div class="breakdown-items">
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">رسوم التوصيل</span>
                                <span class="item-amount" id="delivery-fees">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="delivery-fees-percent">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">رسوم إضافية</span>
                                <span class="item-amount" id="extra-fees">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="extra-fees-percent">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">رسوم التوصيل السريع</span>
                                <span class="item-amount" id="express-fees">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="express-fees-percent">0%</div>
                        </div>
                    </div>
                </div>
                <div class="breakdown-section">
                    <h3>تفصيل المصروفات</h3>
                    <div class="breakdown-items">
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">عمولات المندوبين</span>
                                <span class="item-amount" id="driver-commissions">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="driver-commissions-percent">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">مصروفات تشغيلية</span>
                                <span class="item-amount" id="operational-expenses">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="operational-expenses-percent">0%</div>
                        </div>
                        <div class="breakdown-item">
                            <div class="item-info">
                                <span class="item-label">مصروفات أخرى</span>
                                <span class="item-amount" id="other-expenses">0 د.ع</span>
                            </div>
                            <div class="item-percentage" id="other-expenses-percent">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="transactions-section">
                <div class="section-header">
                    <h3>المعاملات الأخيرة</h3>
                    <button class="neu-btn small view-all-btn">
                        عرض الكل
                    </button>
                </div>
                <div class="transactions-table-container">
                    <table class="transactions-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-tbody">
                            <!-- Transactions will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="drivers-earnings">
                <div class="section-header">
                    <h3>أرباح المندوبين</h3>
                </div>
                <div class="earnings-grid" id="drivers-earnings-grid">
                    <!-- Driver earnings will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = financeHTML;
        this.updateFinancialData();
        this.renderTransactions();
        this.renderDriverEarnings();
        this.initCharts();
    }

    loadSampleData() {
        this.transactions = [
            {
                id: 1,
                date: '2024-12-27',
                type: 'revenue',
                description: 'رسوم توصيل - طلب ORD-2024-001',
                amount: 5000,
                balance: 2450000,
                orderId: 'ORD-2024-001'
            },
            {
                id: 2,
                date: '2024-12-27',
                type: 'commission',
                description: 'عمولة مندوب - أحمد محمد',
                amount: -750,
                balance: 2449250,
                driverId: 1
            },
            {
                id: 3,
                date: '2024-12-26',
                type: 'revenue',
                description: 'رسوم توصيل - طلب ORD-2024-002',
                amount: 5000,
                balance: 2450000,
                orderId: 'ORD-2024-002'
            },
            {
                id: 4,
                date: '2024-12-26',
                type: 'expense',
                description: 'مصروفات تشغيلية - وقود',
                amount: -50000,
                balance: 2445000,
                category: 'operational'
            },
            {
                id: 5,
                date: '2024-12-25',
                type: 'revenue',
                description: 'رسوم توصيل - طلب ORD-2024-003',
                amount: 4000,
                balance: 2495000,
                orderId: 'ORD-2024-003'
            }
        ];

        this.driversEarnings = [
            {
                driverId: 1,
                name: 'أحمد محمد المندوب',
                deliveries: 15,
                totalEarnings: 225000,
                commissionRate: 15,
                rating: 4.8
            },
            {
                driverId: 2,
                name: 'محمد علي المندوب',
                deliveries: 12,
                totalEarnings: 144000,
                commissionRate: 12,
                rating: 4.6
            },
            {
                driverId: 3,
                name: 'عبدالله حسن المندوب',
                deliveries: 18,
                totalEarnings: 270000,
                commissionRate: 15,
                rating: 4.9
            }
        ];
    }

    updateFinancialData() {
        // Calculate financial metrics
        const revenue = this.calculateRevenue();
        const expenses = this.calculateExpenses();
        const profit = revenue - expenses;
        const commissions = this.calculateCommissions();

        // Update overview cards
        document.getElementById('total-revenue').textContent = this.formatCurrency(revenue);
        document.getElementById('total-expenses').textContent = this.formatCurrency(expenses);
        document.getElementById('net-profit').textContent = this.formatCurrency(profit);
        document.getElementById('total-commissions').textContent = this.formatCurrency(commissions);

        // Update breakdown
        this.updateBreakdown(revenue, expenses);

        // Update change indicators (mock data)
        document.getElementById('revenue-change').textContent = '+12.5%';
        document.getElementById('expenses-change').textContent = '****%';
        document.getElementById('profit-change').textContent = '+15.2%';
        document.getElementById('commissions-change').textContent = '+10.1%';
    }

    calculateRevenue() {
        return this.transactions
            .filter(t => t.type === 'revenue')
            .reduce((sum, t) => sum + t.amount, 0);
    }

    calculateExpenses() {
        return Math.abs(this.transactions
            .filter(t => t.type === 'commission' || t.type === 'expense')
            .reduce((sum, t) => sum + t.amount, 0));
    }

    calculateCommissions() {
        return Math.abs(this.transactions
            .filter(t => t.type === 'commission')
            .reduce((sum, t) => sum + t.amount, 0));
    }

    updateBreakdown(revenue, expenses) {
        // Revenue breakdown
        const deliveryFees = revenue * 0.8;
        const extraFees = revenue * 0.15;
        const expressFees = revenue * 0.05;

        document.getElementById('delivery-fees').textContent = this.formatCurrency(deliveryFees);
        document.getElementById('extra-fees').textContent = this.formatCurrency(extraFees);
        document.getElementById('express-fees').textContent = this.formatCurrency(expressFees);

        document.getElementById('delivery-fees-percent').textContent = '80%';
        document.getElementById('extra-fees-percent').textContent = '15%';
        document.getElementById('express-fees-percent').textContent = '5%';

        // Expenses breakdown
        const driverCommissions = expenses * 0.6;
        const operationalExpenses = expenses * 0.3;
        const otherExpenses = expenses * 0.1;

        document.getElementById('driver-commissions').textContent = this.formatCurrency(driverCommissions);
        document.getElementById('operational-expenses').textContent = this.formatCurrency(operationalExpenses);
        document.getElementById('other-expenses').textContent = this.formatCurrency(otherExpenses);

        document.getElementById('driver-commissions-percent').textContent = '60%';
        document.getElementById('operational-expenses-percent').textContent = '30%';
        document.getElementById('other-expenses-percent').textContent = '10%';
    }

    renderTransactions() {
        const tbody = document.getElementById('transactions-tbody');
        if (!tbody) return;

        const recentTransactions = this.transactions.slice(0, 10);

        tbody.innerHTML = recentTransactions.map(transaction => `
            <tr>
                <td>${this.formatDate(transaction.date)}</td>
                <td>
                    <span class="transaction-type ${transaction.type}">
                        ${this.getTransactionTypeText(transaction.type)}
                    </span>
                </td>
                <td>${transaction.description}</td>
                <td class="${transaction.amount >= 0 ? 'positive' : 'negative'}">
                    ${this.formatCurrency(Math.abs(transaction.amount))}
                </td>
                <td>${this.formatCurrency(transaction.balance)}</td>
                <td>
                    <button class="action-btn view-transaction-btn" data-transaction-id="${transaction.id}" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    renderDriverEarnings() {
        const grid = document.getElementById('drivers-earnings-grid');
        if (!grid) return;

        grid.innerHTML = this.driversEarnings.map(driver => `
            <div class="earnings-card">
                <div class="driver-info">
                    <div class="driver-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="driver-details">
                        <h4>${driver.name}</h4>
                        <div class="driver-stats">
                            <span>${driver.deliveries} طلب</span>
                            <span>${driver.rating} ⭐</span>
                        </div>
                    </div>
                </div>
                <div class="earnings-info">
                    <div class="earnings-amount">
                        ${this.formatCurrency(driver.totalEarnings)}
                    </div>
                    <div class="commission-rate">
                        عمولة ${driver.commissionRate}%
                    </div>
                </div>
            </div>
        `).join('');
    }

    initCharts() {
        // Initialize charts (placeholder for now)
        const revenueChart = document.getElementById('revenue-chart');
        const distributionChart = document.getElementById('distribution-chart');

        if (revenueChart) {
            const ctx = revenueChart.getContext('2d');
            ctx.fillStyle = '#667eea';
            ctx.fillRect(50, 50, 300, 100);
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(50, 100, 200, 50);
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px Arial';
            ctx.fillText('الإيرادات والمصروفات', 120, 30);
        }

        if (distributionChart) {
            const ctx = distributionChart.getContext('2d');
            ctx.fillStyle = '#28a745';
            ctx.beginPath();
            ctx.arc(200, 100, 80, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#ffffff';
            ctx.font = '16px Arial';
            ctx.fillText('توزيع الإيرادات', 130, 30);
        }
    }

    getTransactionTypeText(type) {
        const types = {
            'revenue': 'إيراد',
            'commission': 'عمولة',
            'expense': 'مصروف'
        };
        return types[type] || type;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    exportReport() {
        const reportData = this.generateFinancialReport();
        this.downloadCSV(reportData, `financial_report_${new Date().toISOString().split('T')[0]}.csv`);
    }

    generateFinancialReport() {
        const filteredTransactions = this.getFilteredTransactions();
        let csvContent = 'التاريخ,النوع,الوصف,المبلغ,الطلب المرتبط,المندوب\n';

        filteredTransactions.forEach(transaction => {
            csvContent += `${this.formatDate(transaction.date)},${this.getTransactionTypeText(transaction.type)},${transaction.description},${transaction.amount},${transaction.orderId || ''},${transaction.driverName || ''}\n`;
        });

        return csvContent;
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showGenerateInvoiceModal() {
        if (window.ModalManager) {
            window.ModalManager.showGenerateInvoiceModal();
        } else {
            alert('سيتم إضافة نافذة إنشاء الفاتورة قريباً');
        }
    }

    showAddPaymentModal() {
        if (window.ModalManager) {
            window.ModalManager.showAddPaymentModal();
        } else {
            alert('سيتم إضافة نافذة إضافة الدفعة قريباً');
        }
    }

    showDriverCommissionReport() {
        if (window.DriverReportsManager) {
            window.DriverReportsManager.loadContent();
        } else {
            alert('سيتم إضافة تقرير عمولات المندوبين قريباً');
        }
    }

    showCustomerStatementModal() {
        if (window.ModalManager) {
            window.ModalManager.showCustomerStatementModal();
        } else {
            alert('سيتم إضافة نافذة كشف حساب العميل قريباً');
        }
    }

    addPayment(paymentData) {
        const newTransaction = {
            id: Date.now(),
            type: 'revenue',
            amount: parseFloat(paymentData.amount),
            description: `دفعة من ${paymentData.customerName}`,
            date: paymentData.date || new Date().toISOString().split('T')[0],
            orderId: paymentData.orderId || null,
            customerId: paymentData.customerId || null,
            paymentMethod: paymentData.paymentMethod || 'cash'
        };

        this.transactions.push(newTransaction);
        this.updateFinancialData();
        this.renderTransactions();

        if (window.ActivityLogger) {
            window.ActivityLogger.logActivity(
                'PAYMENT_ADDED',
                `تم إضافة دفعة بمبلغ ${this.formatCurrency(newTransaction.amount)}`,
                { transactionId: newTransaction.id, amount: newTransaction.amount }
            );
        }
    }

    generateInvoice(invoiceData) {
        const invoice = {
            id: `INV-${Date.now()}`,
            customerId: invoiceData.customerId,
            customerName: invoiceData.customerName,
            items: invoiceData.items || [],
            subtotal: parseFloat(invoiceData.subtotal || 0),
            tax: parseFloat(invoiceData.tax || 0),
            total: parseFloat(invoiceData.total || 0),
            dueDate: invoiceData.dueDate,
            status: 'pending',
            createdAt: new Date().toISOString(),
            notes: invoiceData.notes || ''
        };

        // Add to transactions
        const transaction = {
            id: Date.now(),
            type: 'revenue',
            amount: invoice.total,
            description: `فاتورة ${invoice.id} - ${invoice.customerName}`,
            date: new Date().toISOString().split('T')[0],
            invoiceId: invoice.id,
            customerId: invoice.customerId
        };

        this.transactions.push(transaction);
        this.updateFinancialData();
        this.renderTransactions();

        return invoice;
    }

    showTransactionDetails(transactionId) {
        if (window.ModalManager) {
            window.ModalManager.showTransactionDetailsModal(transactionId);
        } else {
            const transaction = this.transactions.find(t => t.id == transactionId);
            if (transaction) {
                alert(`تفاصيل المعاملة:\nالنوع: ${this.getTransactionTypeText(transaction.type)}\nالمبلغ: ${this.formatCurrency(transaction.amount)}\nالوصف: ${transaction.description}\nالتاريخ: ${this.formatDate(transaction.date)}`);
            }
        }
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Finance Manager
window.FinanceManager = new FinanceManager();
