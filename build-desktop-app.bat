@echo off
chcp 65001 >nul
title نظام إدارة شركة التوصيل العراقية - بناء التطبيق

echo.
echo ========================================
echo   نظام إدارة شركة التوصيل العراقية
echo   بناء تطبيق سطح المكتب
echo ========================================
echo.

echo [1/5] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js مثبت بنجاح

echo.
echo [2/5] فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متاح!
    pause
    exit /b 1
)
echo ✅ npm متاح

echo.
echo [3/5] تثبيت التبعيات...
echo هذا قد يستغرق 3-5 دقائق...
npm install
if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت التبعيات!
    echo جرب تشغيل الأمر كمدير (Run as Administrator)
    pause
    exit /b 1
)
echo ✅ تم تثبيت التبعيات بنجاح

echo.
echo [4/5] اختبار التطبيق...
echo سيتم فتح التطبيق للاختبار...
echo أغلق التطبيق بعد التأكد من عمله بشكل صحيح
timeout /t 3 >nul
start /wait npm start
echo ✅ تم اختبار التطبيق

echo.
echo [5/5] بناء ملف exe...
echo هذا قد يستغرق 5-10 دقائق...
echo يرجى الانتظار...
npm run build-win
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء التطبيق!
    echo تأكد من وجود مساحة كافية على القرص (2 GB على الأقل)
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم بناء التطبيق بنجاح!
echo ========================================
echo.
echo الملفات المُنتجة:
echo 📁 مجلد: dist\
echo 📦 المثبت: نظام إدارة شركة التوصيل العراقية Setup 1.0.0.exe
echo 🚀 النسخة المحمولة: نظام إدارة شركة التوصيل العراقية 1.0.0.exe
echo.
echo الحجم المتوقع: ~150-200 MB
echo.

echo هل تريد فتح مجلد الملفات المُنتجة؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer dist
)

echo.
echo شكراً لاستخدام نظام إدارة شركة التوصيل العراقية!
echo للدعم التقني: <EMAIL>
echo.
pause
