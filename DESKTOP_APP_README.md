# نظام إدارة شركة التوصيل العراقية - تطبيق سطح المكتب
## Iraqi Delivery Management System - Desktop Application

<div align="center">

**🖥️ نسخة سطح المكتب الاحترافية من نظام إدارة شركة التوصيل العراقية 🖥️**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/iraqi-delivery/desktop-app)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)](https://github.com/iraqi-delivery/desktop-app)
[![Electron](https://img.shields.io/badge/Electron-27.0.0-blue.svg)](https://electronjs.org/)

</div>

---

## 🎉 **مرحباً بك في النسخة الجديدة!**

تم تطوير نسخة سطح مكتب كاملة ومتطورة من النظام باستخدام تقنية **Electron**، مما يوفر تجربة استخدام محسنة وميزات متقدمة.

---

## 🚀 **المميزات الجديدة في نسخة سطح المكتب**

### **⚡ الأداء والسرعة**
- ✅ **تشغيل محلي** - لا يحتاج خادم أو إنترنت
- ✅ **سرعة فائقة** - استجابة فورية لجميع العمليات
- ✅ **استهلاك ذاكرة محسن** - أداء مُحسن للأجهزة المتوسطة

### **🔒 الأمان والحماية**
- ✅ **تشفير البيانات** - جميع البيانات الحساسة مشفرة
- ✅ **تخزين محلي آمن** - بيانات محفوظة على جهازك فقط
- ✅ **نسخ احتياطية تلقائية** - حماية من فقدان البيانات
- ✅ **سجل الأنشطة** - تتبع جميع العمليات

### **🖨️ الطباعة المحسنة**
- ✅ **طباعة عالية الجودة** - تقارير وفواتير احترافية
- ✅ **معاينة قبل الطباعة** - تحكم كامل في التنسيق
- ✅ **طباعة مخصصة** - اختيار أجزاء محددة للطباعة
- ✅ **حفظ كـ PDF** - تصدير مباشر للملفات

### **🔔 الإشعارات الذكية**
- ✅ **إشعارات سطح المكتب** - تنبيهات فورية للأحداث المهمة
- ✅ **إشعارات مخصصة** - تحكم في نوع وتوقيت الإشعارات
- ✅ **تذكيرات تلقائية** - تذكيرات للمهام المعلقة

### **⌨️ اختصارات لوحة المفاتيح**
- ✅ **Ctrl+S** - حفظ سريع للبيانات
- ✅ **Ctrl+P** - طباعة فورية
- ✅ **Ctrl+O** - فتح ملف
- ✅ **F5** - إعادة تحميل
- ✅ **F11** - ملء الشاشة

### **💾 إدارة البيانات المتقدمة**
- ✅ **نسخ احتياطية ذكية** - نسخ تلقائية ويدوية
- ✅ **استيراد وتصدير** - تبادل البيانات بسهولة
- ✅ **ضغط البيانات** - توفير مساحة التخزين
- ✅ **استعادة سريعة** - استعادة البيانات بنقرة واحدة

---

## 📥 **التحميل والتثبيت**

### **🪟 Windows (الموصى به)**
```
📁 نظام إدارة شركة التوصيل العراقية Setup.exe (150 MB)
```
- **المثبت الكامل** - تثبيت تلقائي مع جميع المتطلبات
- **النسخة المحمولة** - تشغيل مباشر بدون تثبيت
- **دعم Windows 7/8/10/11** - جميع إصدارات Windows

### **🍎 macOS**
```
📁 نظام إدارة شركة التوصيل العراقية.dmg (120 MB)
```
- **تثبيت سهل** - سحب وإفلات في مجلد Applications
- **دعم Intel و Apple Silicon** - يعمل على جميع أجهزة Mac
- **macOS 10.12+** - دعم الإصدارات الحديثة

### **🐧 Linux**
```bash
# Ubuntu/Debian
sudo dpkg -i iraqi-delivery-system_1.0.0_amd64.deb

# AppImage (جميع التوزيعات)
chmod +x نظام-إدارة-شركة-التوصيل-العراقية-1.0.0.AppImage
./نظام-إدارة-شركة-التوصيل-العراقية-1.0.0.AppImage
```

---

## 🛠️ **بناء التطبيق من المصدر**

### **المتطلبات:**
- **Node.js 16+** - https://nodejs.org/
- **npm أو yarn** - مدير الحزم
- **Git** - للاستنساخ (اختياري)

### **خطوات البناء:**

#### **1. إعداد المشروع:**
```bash
# استنساخ المشروع (أو تحميل ZIP)
git clone https://github.com/iraqi-delivery/desktop-app.git
cd desktop-app

# تثبيت التبعيات
npm install
```

#### **2. تشغيل في وضع التطوير:**
```bash
npm start
# أو
npm run dev
```

#### **3. بناء للإنتاج:**
```bash
# بناء لجميع الأنظمة
npm run build

# بناء لـ Windows فقط
npm run build-win

# بناء لـ macOS فقط
npm run build-mac

# بناء لـ Linux فقط
npm run build-linux
```

#### **4. الملفات المُنتجة:**
```
dist/
├── نظام إدارة شركة التوصيل العراقية Setup 1.0.0.exe    (Windows)
├── نظام إدارة شركة التوصيل العراقية-1.0.0.dmg          (macOS)
├── نظام إدارة شركة التوصيل العراقية-1.0.0.AppImage      (Linux)
└── iraqi-delivery-system_1.0.0_amd64.deb                (Ubuntu/Debian)
```

---

## 📖 **دليل الاستخدام السريع**

### **🔐 تسجيل الدخول:**
1. شغل التطبيق من سطح المكتب
2. أدخل بيانات المستخدم
3. اختر نوع المستخدم
4. اضغط "تسجيل الدخول"

### **📦 إدارة الطلبات:**
1. انقر "إدارة الطلبات" من القائمة الجانبية
2. اضغط "إضافة طلب جديد"
3. املأ بيانات الطلب
4. احفظ بـ **Ctrl+S**

### **💾 النسخ الاحتياطية:**
1. انتقل إلى "الإعدادات"
2. اضغط "نسخة احتياطية"
3. اختر مكان الحفظ
4. اضغط "إنشاء"

### **🖨️ الطباعة:**
1. افتح التقرير أو الفاتورة المطلوبة
2. اضغط **Ctrl+P** أو زر الطباعة
3. اختر الطابعة والإعدادات
4. اضغط "طباعة"

---

## 🔧 **الإعدادات والتخصيص**

### **⚙️ إعدادات التطبيق:**
- **اللغة:** العربية (افتراضي) / الإنجليزية
- **المظهر:** فاتح / داكن / تلقائي
- **النسخ الاحتياطية:** تلقائية كل 24 ساعة
- **الإشعارات:** تفعيل/إيقاف إشعارات النظام

### **🏢 إعدادات الشركة:**
- **اسم الشركة:** قابل للتخصيص
- **العنوان والهاتف:** معلومات الاتصال
- **الشعار:** رفع شعار الشركة
- **العملة:** دينار عراقي (IQD)

### **👥 إدارة المستخدمين:**
- **إضافة مستخدمين جدد**
- **تعديل الصلاحيات**
- **تغيير كلمات المرور**
- **سجل الأنشطة**

---

## 🆘 **الدعم والمساعدة**

### **📞 الدعم التقني:**
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-XXX-XXXX
- **الدردشة المباشرة:** متاحة في التطبيق

### **📚 الموارد:**
- **دليل المستخدم:** https://docs.iraqi-delivery.com
- **فيديوهات تعليمية:** https://iraqi-delivery.com/tutorials
- **أسئلة شائعة:** https://iraqi-delivery.com/faq

### **🐛 الإبلاغ عن الأخطاء:**
- **GitHub Issues:** https://github.com/iraqi-delivery/desktop-app/issues
- **البريد الإلكتروني:** <EMAIL>

---

## 🔄 **التحديثات التلقائية**

التطبيق يتحقق تلقائياً من التحديثات ويقوم بتنزيلها في الخلفية:

- ✅ **تحديثات أمنية** - تطبق تلقائياً
- ✅ **ميزات جديدة** - إشعار للمستخدم
- ✅ **إصلاح الأخطاء** - تحديث سلس
- ✅ **تحديث يدوي** - متاح في قائمة المساعدة

---

## 🎯 **خارطة الطريق**

### **الإصدار 1.1 (قريباً):**
- 🔄 **مزامنة السحابة** - نسخ احتياطية سحابية
- 📱 **تطبيق محمول** - تطبيق للهواتف الذكية
- 🤖 **ذكاء اصطناعي** - تحليل الأنماط والتنبؤ
- 🌐 **API متقدم** - تكامل مع أنظمة خارجية

### **الإصدار 1.2:**
- 📊 **تحليلات متقدمة** - Business Intelligence
- 🔗 **تكامل المدفوعات** - بوابات دفع إلكترونية
- 📧 **إشعارات البريد الإلكتروني** - تنبيهات تلقائية
- 🗺️ **خرائط محسنة** - تتبع GPS متقدم

---

## 📄 **الترخيص والحقوق**

هذا المشروع مرخص تحت **رخصة MIT** - انظر ملف [LICENSE](LICENSE) للتفاصيل.

**© 2024 نظام إدارة شركة التوصيل العراقية. جميع الحقوق محفوظة.**

---

## 🤝 **المساهمة والتطوير**

نرحب بمساهماتكم في تطوير النظام:

1. **Fork** المشروع
2. إنشاء **branch** جديد للميزة
3. **Commit** التغييرات
4. **Push** إلى البرانش
5. إنشاء **Pull Request**

---

## 📞 **التواصل**

- **🌐 الموقع الرسمي:** https://iraqi-delivery.com
- **📧 البريد الإلكتروني:** <EMAIL>
- **📱 تويتر:** @IraqiDelivery
- **📘 فيسبوك:** /IraqiDeliverySystem
- **💼 لينكد إن:** /company/iraqi-delivery

---

<div align="center">

**🎉 شكراً لاختيارك نظام إدارة شركة التوصيل العراقية! 🎉**

**🇮🇶 صُنع بحب في العراق 🇮🇶**

</div>
