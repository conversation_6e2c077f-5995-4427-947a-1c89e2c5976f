<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة شركة التوصيل العراقية</title>
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--bg-color);
            border-radius: 20px;
            box-shadow: var(--neu-shadow);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: var(--card-bg);
            border-radius: 15px;
            box-shadow: var(--neu-shadow-inset);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin: 0.5rem 0;
            background: var(--bg-color);
            border-radius: 10px;
            box-shadow: var(--neu-shadow-small);
        }
        
        .test-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            color: white;
        }
        
        .test-pass { background: #27ae60; }
        .test-fail { background: #e74c3c; }
        .test-pending { background: #f39c12; }
        
        .test-button {
            padding: 0.5rem 1rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .test-results {
            margin-top: 2rem;
            padding: 1rem;
            background: var(--card-bg);
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار نظام إدارة شركة التوصيل العراقية</h1>
            <p>اختبار شامل لجميع وظائف النظام</p>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-code"></i> اختبار ملفات JavaScript</h2>
            <div class="test-item">
                <span>تحميل ملف app.js</span>
                <span class="test-status test-pending" id="app-js-status">انتظار</span>
            </div>
            <div class="test-item">
                <span>تحميل ملف dashboard.js</span>
                <span class="test-status test-pending" id="dashboard-js-status">انتظار</span>
            </div>
            <div class="test-item">
                <span>تحميل ملف orders.js</span>
                <span class="test-status test-pending" id="orders-js-status">انتظار</span>
            </div>
            <div class="test-item">
                <span>تحميل ملف barcode-generator.js</span>
                <span class="test-status test-pending" id="barcode-js-status">انتظار</span>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-calculator"></i> اختبار تحويل الأرقام</h2>
            <div class="test-item">
                <span>تحويل الأرقام العربية إلى الإنجليزية</span>
                <span class="test-status test-pending" id="number-conversion-status">انتظار</span>
            </div>
            <div class="test-item">
                <span>تنسيق العملة العراقية</span>
                <span class="test-status test-pending" id="currency-format-status">انتظار</span>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-barcode"></i> اختبار نظام رقم الوصل</h2>
            <div class="test-item">
                <span>إنشاء رقم وصل تلقائي</span>
                <span class="test-status test-pending" id="receipt-generation-status">انتظار</span>
            </div>
            <div class="test-item">
                <span>إنشاء باركود</span>
                <span class="test-status test-pending" id="barcode-generation-status">انتظار</span>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-window-maximize"></i> اختبار النوافذ المنبثقة</h2>
            <div class="test-item">
                <span>نافذة إضافة طلب</span>
                <button class="test-button" onclick="testAddOrderModal()">اختبار</button>
            </div>
            <div class="test-item">
                <span>نافذة إضافة مندوب</span>
                <button class="test-button" onclick="testAddDriverModal()">اختبار</button>
            </div>
            <div class="test-item">
                <span>نافذة عرض الباركود</span>
                <button class="test-button" onclick="testBarcodeModal()">اختبار</button>
            </div>
        </div>

        <div class="test-section">
            <h2><i class="fas fa-link"></i> اختبار التنقل</h2>
            <div class="test-item">
                <span>الانتقال للوحة التحكم</span>
                <button class="test-button" onclick="testNavigation('dashboard')">اختبار</button>
            </div>
            <div class="test-item">
                <span>الانتقال لصفحة الطلبات</span>
                <button class="test-button" onclick="testNavigation('orders')">اختبار</button>
            </div>
            <div class="test-item">
                <span>الانتقال لصفحة المندوبين</span>
                <button class="test-button" onclick="testNavigation('drivers')">اختبار</button>
            </div>
        </div>

        <button class="test-button" onclick="runAllTests()" style="width: 100%; padding: 1rem; font-size: 1.2rem; margin-top: 2rem;">
            <i class="fas fa-play"></i> تشغيل جميع الاختبارات
        </button>

        <div class="test-results" id="test-results" style="display: none;">
            <h3><i class="fas fa-chart-bar"></i> نتائج الاختبار</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <!-- Load all system scripts -->
    <script src="assets/js/app.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/activity-logger.js"></script>
    <script src="assets/js/barcode-generator.js"></script>
    <script src="assets/js/modals.js"></script>
    <script src="assets/js/orders.js"></script>
    <script src="assets/js/drivers.js"></script>
    <script src="assets/js/customers.js"></script>
    <script src="assets/js/finance.js"></script>
    <script src="assets/js/tracking.js"></script>
    <script src="assets/js/regions.js"></script>
    <script src="assets/js/billing.js"></script>
    <script src="assets/js/driver-reports.js"></script>
    <script src="assets/js/postponed-orders.js"></script>

    <script>
        // Test functions
        function updateStatus(elementId, status, message = '') {
            const element = document.getElementById(elementId);
            element.className = `test-status test-${status}`;
            element.textContent = status === 'pass' ? 'نجح' : status === 'fail' ? 'فشل' : 'انتظار';
            if (message) {
                element.title = message;
            }
        }

        function testJavaScriptFiles() {
            // Test if main objects are loaded
            updateStatus('app-js-status', window.app ? 'pass' : 'fail');
            updateStatus('dashboard-js-status', window.DashboardManager ? 'pass' : 'fail');
            updateStatus('orders-js-status', window.OrdersManager ? 'pass' : 'fail');
            updateStatus('barcode-js-status', window.BarcodeGenerator ? 'pass' : 'fail');
        }

        function testNumberConversion() {
            try {
                if (window.app && window.app.convertArabicToEnglishNumbers) {
                    const result = window.app.convertArabicToEnglishNumbers('١٢٣٤٥');
                    updateStatus('number-conversion-status', result === '12345' ? 'pass' : 'fail');
                } else {
                    updateStatus('number-conversion-status', 'fail', 'Function not found');
                }

                if (window.app && window.app.formatCurrency) {
                    const result = window.app.formatCurrency(123456);
                    updateStatus('currency-format-status', result.includes('د.ع') ? 'pass' : 'fail');
                } else {
                    updateStatus('currency-format-status', 'fail', 'Function not found');
                }
            } catch (error) {
                updateStatus('number-conversion-status', 'fail', error.message);
                updateStatus('currency-format-status', 'fail', error.message);
            }
        }

        function testReceiptSystem() {
            try {
                if (window.OrdersManager && window.OrdersManager.generateReceiptNumber) {
                    const receiptNumber = window.OrdersManager.generateReceiptNumber();
                    updateStatus('receipt-generation-status', receiptNumber && receiptNumber.length === 12 ? 'pass' : 'fail');
                } else {
                    updateStatus('receipt-generation-status', 'fail', 'Function not found');
                }

                if (window.BarcodeGenerator && window.BarcodeGenerator.generateBarcode) {
                    const barcode = window.BarcodeGenerator.generateBarcode('123456789012');
                    updateStatus('barcode-generation-status', barcode ? 'pass' : 'fail');
                } else {
                    updateStatus('barcode-generation-status', 'fail', 'Function not found');
                }
            } catch (error) {
                updateStatus('receipt-generation-status', 'fail', error.message);
                updateStatus('barcode-generation-status', 'fail', error.message);
            }
        }

        function testAddOrderModal() {
            try {
                if (window.ModalManager && window.ModalManager.showAddOrderModal) {
                    window.ModalManager.showAddOrderModal();
                    alert('نافذة إضافة الطلب تعمل بشكل صحيح');
                } else {
                    alert('خطأ: نافذة إضافة الطلب غير متاحة');
                }
            } catch (error) {
                alert('خطأ: ' + error.message);
            }
        }

        function testAddDriverModal() {
            try {
                if (window.ModalManager && window.ModalManager.showAddDriverModal) {
                    window.ModalManager.showAddDriverModal();
                    alert('نافذة إضافة المندوب تعمل بشكل صحيح');
                } else {
                    alert('خطأ: نافذة إضافة المندوب غير متاحة');
                }
            } catch (error) {
                alert('خطأ: ' + error.message);
            }
        }

        function testBarcodeModal() {
            try {
                if (window.BarcodeGenerator && window.BarcodeGenerator.showBarcodeModal) {
                    window.BarcodeGenerator.showBarcodeModal('123456789012', {
                        id: 'TEST-001',
                        customer: 'عميل تجريبي',
                        recipient: 'مستلم تجريبي'
                    });
                    alert('نافذة عرض الباركود تعمل بشكل صحيح');
                } else {
                    alert('خطأ: نافذة عرض الباركود غير متاحة');
                }
            } catch (error) {
                alert('خطأ: ' + error.message);
            }
        }

        function testNavigation(page) {
            try {
                if (window.app && window.app.loadPage) {
                    window.app.loadPage(page);
                    alert(`تم الانتقال إلى صفحة ${page} بنجاح`);
                } else {
                    alert('خطأ: نظام التنقل غير متاح');
                }
            } catch (error) {
                alert('خطأ: ' + error.message);
            }
        }

        function runAllTests() {
            document.getElementById('test-results').style.display = 'block';
            
            // Run all automated tests
            testJavaScriptFiles();
            testNumberConversion();
            testReceiptSystem();
            
            // Show results summary
            const resultsContent = document.getElementById('results-content');
            resultsContent.innerHTML = `
                <p><strong>تم تشغيل الاختبارات التلقائية بنجاح!</strong></p>
                <p>يرجى اختبار النوافذ المنبثقة والتنقل يدوياً باستخدام الأزرار أعلاه.</p>
                <p><em>تاريخ الاختبار: ${new Date().toLocaleString('ar-IQ')}</em></p>
            `;
        }

        // Run initial tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                testJavaScriptFiles();
                testNumberConversion();
                testReceiptSystem();
            }, 1000);
        });
    </script>
</body>
</html>
