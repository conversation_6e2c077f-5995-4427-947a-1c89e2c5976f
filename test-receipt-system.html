<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام رقم الوصل والباركود</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-vial"></i> اختبار نظام رقم الوصل والباركود المحسن</h1>
        
        <!-- Test 1: Receipt System Loading -->
        <div class="test-section">
            <h3>اختبار 1: تحميل نظام إدارة أرقام الوصل</h3>
            <button onclick="testReceiptSystemLoading()">تشغيل الاختبار</button>
            <div id="test1-result"></div>
        </div>

        <!-- Test 2: Auto Receipt Generation -->
        <div class="test-section">
            <h3>اختبار 2: إنشاء رقم وصل تلقائي</h3>
            <button onclick="testAutoReceiptGeneration()">تشغيل الاختبار</button>
            <div id="test2-result"></div>
        </div>

        <!-- Test 3: Manual Receipt Generation -->
        <div class="test-section">
            <h3>اختبار 3: إنشاء رقم وصل يدوي</h3>
            <input type="text" id="manual-receipt" placeholder="مثال: IQ-2024-123456" value="IQ-2024-123456">
            <button onclick="testManualReceiptGeneration()">تشغيل الاختبار</button>
            <div id="test3-result"></div>
        </div>

        <!-- Test 4: Receipt Format Validation -->
        <div class="test-section">
            <h3>اختبار 4: التحقق من تنسيق رقم الوصل</h3>
            <input type="text" id="format-test" placeholder="أدخل رقم للاختبار" value="IQ-2024-000001">
            <button onclick="testReceiptFormatValidation()">تشغيل الاختبار</button>
            <div id="test4-result"></div>
        </div>

        <!-- Test 5: Barcode Generation -->
        <div class="test-section">
            <h3>اختبار 5: إنشاء باركود Code128</h3>
            <input type="text" id="barcode-data" placeholder="البيانات للباركود" value="IQ-2024-000001">
            <button onclick="testBarcodeGeneration()">تشغيل الاختبار</button>
            <canvas id="test-canvas" width="400" height="150"></canvas>
            <div id="test5-result"></div>
        </div>

        <!-- Test 6: QR Code Generation -->
        <div class="test-section">
            <h3>اختبار 6: إنشاء رمز QR</h3>
            <input type="text" id="qr-data" placeholder="البيانات للرمز" value="IQ-2024-000001">
            <button onclick="testQRGeneration()">تشغيل الاختبار</button>
            <canvas id="qr-canvas" width="200" height="200"></canvas>
            <div id="test6-result"></div>
        </div>

        <!-- Test 7: Integration Test -->
        <div class="test-section">
            <h3>اختبار 7: التكامل مع نظام الطلبات</h3>
            <button onclick="testOrdersIntegration()">تشغيل الاختبار</button>
            <div id="test7-result"></div>
        </div>

        <!-- Test 8: API Test -->
        <div class="test-section">
            <h3>اختبار 8: اختبار API</h3>
            <button onclick="testAPI()">تشغيل الاختبار</button>
            <div id="test8-result"></div>
        </div>

        <!-- Run All Tests -->
        <div class="test-section">
            <h3>تشغيل جميع الاختبارات</h3>
            <button onclick="runAllTests()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
            <div id="all-tests-result"></div>
        </div>
    </div>

    <!-- Load external libraries -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Load our systems -->
    <script src="assets/js/receipt-system.js"></script>
    <script src="assets/js/advanced-barcode.js"></script>
    <script src="assets/js/receipt-api-client.js"></script>
    <script src="assets/js/advanced-receipt-features.js"></script>

    <script>
        // Test Results Storage
        let testResults = [];

        // Test 1: Receipt System Loading
        function testReceiptSystemLoading() {
            const resultDiv = document.getElementById('test1-result');
            try {
                if (typeof ReceiptSystem !== 'undefined' && window.ReceiptSystem) {
                    resultDiv.innerHTML = '<div class="success"><i class="fas fa-check"></i> نجح: تم تحميل نظام إدارة أرقام الوصل بنجاح</div>';
                    testResults[0] = true;
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: لم يتم تحميل نظام إدارة أرقام الوصل</div>';
                    testResults[0] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[0] = false;
            }
        }

        // Test 2: Auto Receipt Generation
        function testAutoReceiptGeneration() {
            const resultDiv = document.getElementById('test2-result');
            try {
                if (window.ReceiptSystem) {
                    const receiptNumber = window.ReceiptSystem.generateReceiptNumber(false);
                    if (receiptNumber && receiptNumber.match(/^[A-Z]{2,3}-\d{4}-\d{6}$/)) {
                        resultDiv.innerHTML = `<div class="success"><i class="fas fa-check"></i> نجح: تم إنشاء رقم الوصل: ${receiptNumber}</div>`;
                        testResults[1] = true;
                    } else {
                        resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: رقم الوصل المُنشأ غير صحيح</div>';
                        testResults[1] = false;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: نظام إدارة أرقام الوصل غير متوفر</div>';
                    testResults[1] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[1] = false;
            }
        }

        // Test 3: Manual Receipt Generation
        function testManualReceiptGeneration() {
            const resultDiv = document.getElementById('test3-result');
            const manualReceipt = document.getElementById('manual-receipt').value;
            try {
                if (window.ReceiptSystem) {
                    const receiptNumber = window.ReceiptSystem.generateReceiptNumber(true, manualReceipt);
                    if (receiptNumber === manualReceipt) {
                        resultDiv.innerHTML = `<div class="success"><i class="fas fa-check"></i> نجح: تم إنشاء رقم الوصل اليدوي: ${receiptNumber}</div>`;
                        testResults[2] = true;
                    } else {
                        resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: لم يتم إنشاء رقم الوصل اليدوي بشكل صحيح</div>';
                        testResults[2] = false;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: نظام إدارة أرقام الوصل غير متوفر</div>';
                    testResults[2] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[2] = false;
            }
        }

        // Test 4: Receipt Format Validation
        function testReceiptFormatValidation() {
            const resultDiv = document.getElementById('test4-result');
            const testReceipt = document.getElementById('format-test').value;
            try {
                if (window.ReceiptSystem) {
                    const isValid = window.ReceiptSystem.validateReceiptFormat(testReceipt);
                    if (isValid) {
                        resultDiv.innerHTML = `<div class="success"><i class="fas fa-check"></i> نجح: تنسيق رقم الوصل صحيح: ${testReceipt}</div>`;
                        testResults[3] = true;
                    } else {
                        resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> فشل: تنسيق رقم الوصل غير صحيح: ${testReceipt}</div>`;
                        testResults[3] = false;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: نظام إدارة أرقام الوصل غير متوفر</div>';
                    testResults[3] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[3] = false;
            }
        }

        // Test 5: Barcode Generation
        function testBarcodeGeneration() {
            const resultDiv = document.getElementById('test5-result');
            const barcodeData = document.getElementById('barcode-data').value;
            const canvas = document.getElementById('test-canvas');
            try {
                if (typeof JsBarcode !== 'undefined') {
                    JsBarcode(canvas, barcodeData, {
                        format: "CODE128",
                        width: 2,
                        height: 100,
                        displayValue: true
                    });
                    resultDiv.innerHTML = `<div class="success"><i class="fas fa-check"></i> نجح: تم إنشاء الباركود للبيانات: ${barcodeData}</div>`;
                    testResults[4] = true;
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: مكتبة JsBarcode غير متوفرة</div>';
                    testResults[4] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[4] = false;
            }
        }

        // Test 6: QR Code Generation
        function testQRGeneration() {
            const resultDiv = document.getElementById('test6-result');
            const qrData = document.getElementById('qr-data').value;
            const canvas = document.getElementById('qr-canvas');
            try {
                if (typeof QRCode !== 'undefined') {
                    QRCode.toCanvas(canvas, qrData, function (error) {
                        if (error) {
                            resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ في إنشاء QR: ${error}</div>`;
                            testResults[5] = false;
                        } else {
                            resultDiv.innerHTML = `<div class="success"><i class="fas fa-check"></i> نجح: تم إنشاء رمز QR للبيانات: ${qrData}</div>`;
                            testResults[5] = true;
                        }
                    });
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: مكتبة QRCode غير متوفرة</div>';
                    testResults[5] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[5] = false;
            }
        }

        // Test 7: Orders Integration
        function testOrdersIntegration() {
            const resultDiv = document.getElementById('test7-result');
            try {
                if (typeof OrdersManager !== 'undefined' && window.OrdersManager) {
                    resultDiv.innerHTML = '<div class="success"><i class="fas fa-check"></i> نجح: التكامل مع نظام الطلبات متوفر</div>';
                    testResults[6] = true;
                } else {
                    resultDiv.innerHTML = '<div class="info"><i class="fas fa-info"></i> معلومات: نظام الطلبات غير محمل (طبيعي في صفحة الاختبار)</div>';
                    testResults[6] = true; // Consider this as pass since it's expected in test page
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[6] = false;
            }
        }

        // Test 8: API Test
        function testAPI() {
            const resultDiv = document.getElementById('test8-result');
            try {
                if (typeof ReceiptAPIClient !== 'undefined' && window.ReceiptAPIClient) {
                    resultDiv.innerHTML = '<div class="success"><i class="fas fa-check"></i> نجح: عميل API متوفر</div>';
                    testResults[7] = true;
                } else {
                    resultDiv.innerHTML = '<div class="error"><i class="fas fa-times"></i> فشل: عميل API غير متوفر</div>';
                    testResults[7] = false;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><i class="fas fa-times"></i> خطأ: ${error.message}</div>`;
                testResults[7] = false;
            }
        }

        // Run All Tests
        function runAllTests() {
            const resultDiv = document.getElementById('all-tests-result');
            resultDiv.innerHTML = '<div class="info"><i class="fas fa-spinner fa-spin"></i> جاري تشغيل جميع الاختبارات...</div>';
            
            setTimeout(() => {
                testReceiptSystemLoading();
                setTimeout(() => testAutoReceiptGeneration(), 100);
                setTimeout(() => testManualReceiptGeneration(), 200);
                setTimeout(() => testReceiptFormatValidation(), 300);
                setTimeout(() => testBarcodeGeneration(), 400);
                setTimeout(() => testQRGeneration(), 500);
                setTimeout(() => testOrdersIntegration(), 600);
                setTimeout(() => testAPI(), 700);
                
                setTimeout(() => {
                    const passedTests = testResults.filter(result => result === true).length;
                    const totalTests = testResults.length;
                    const successRate = Math.round((passedTests / totalTests) * 100);
                    
                    let resultClass = 'success';
                    if (successRate < 70) resultClass = 'error';
                    else if (successRate < 90) resultClass = 'info';
                    
                    resultDiv.innerHTML = `
                        <div class="${resultClass}">
                            <h4><i class="fas fa-chart-bar"></i> نتائج الاختبار الشامل</h4>
                            <p><strong>الاختبارات الناجحة:</strong> ${passedTests} من ${totalTests}</p>
                            <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                            <p><strong>الحالة:</strong> ${successRate >= 90 ? 'ممتاز' : successRate >= 70 ? 'جيد' : 'يحتاج تحسين'}</p>
                        </div>
                    `;
                }, 1000);
            }, 100);
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            setTimeout(() => {
                testReceiptSystemLoading();
                testBarcodeGeneration();
            }, 1000);
        };
    </script>
</body>
</html>
