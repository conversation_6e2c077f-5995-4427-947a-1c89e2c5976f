; إعدادات مخصصة للمثبت
; Custom installer settings for Iraqi Delivery System

; إضافة دعم اللغة العربية
!define MUI_LANGDLL_ALLLANGUAGES

; رسائل مخصصة
LangString welcome ${LANG_ARABIC} "مرحباً بك في معالج تثبيت نظام إدارة شركة التوصيل العراقية"
LangString welcome ${LANG_ENGLISH} "Welcome to Iraqi Delivery Management System Setup Wizard"

; إعدادات الترخيص
LangString license ${LANG_ARABIC} "اتفاقية الترخيص"
LangString license ${LANG_ENGLISH} "License Agreement"

; إعدادات المجلد
LangString directory ${LANG_ARABIC} "اختر مجلد التثبيت"
LangString directory ${LANG_ENGLISH} "Choose Install Location"

; إعدادات المكونات
LangString components ${LANG_ARABIC} "اختر المكونات"
LangString components ${LANG_ENGLISH} "Choose Components"

; رسائل التثبيت
LangString installing ${LANG_ARABIC} "جاري التثبيت..."
LangString installing ${LANG_ENGLISH} "Installing..."

; رسائل الانتهاء
LangString finish ${LANG_ARABIC} "تم التثبيت بنجاح"
LangString finish ${LANG_ENGLISH} "Installation Complete"

; إضافة اختصارات سطح المكتب
Section "Desktop Shortcut" SecDesktop
    CreateShortCut "$DESKTOP\نظام إدارة التوصيل العراقية.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
SectionEnd

; إضافة اختصارات قائمة ابدأ
Section "Start Menu Shortcuts" SecStartMenu
    CreateDirectory "$SMPROGRAMS\نظام إدارة التوصيل العراقية"
    CreateShortCut "$SMPROGRAMS\نظام إدارة التوصيل العراقية\نظام إدارة التوصيل العراقية.lnk" "$INSTDIR\${PRODUCT_NAME}.exe"
    CreateShortCut "$SMPROGRAMS\نظام إدارة التوصيل العراقية\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
SectionEnd

; تسجيل نوع الملف
Section "File Association" SecFileAssoc
    WriteRegStr HKCR ".idm" "" "IraqiDeliveryFile"
    WriteRegStr HKCR "IraqiDeliveryFile" "" "Iraqi Delivery Management File"
    WriteRegStr HKCR "IraqiDeliveryFile\DefaultIcon" "" "$INSTDIR\${PRODUCT_NAME}.exe,0"
    WriteRegStr HKCR "IraqiDeliveryFile\shell\open\command" "" '"$INSTDIR\${PRODUCT_NAME}.exe" "%1"'
SectionEnd

; إنشاء مجلد البيانات
Section "Data Directory" SecDataDir
    CreateDirectory "$APPDATA\IraqiDeliverySystem"
    CreateDirectory "$APPDATA\IraqiDeliverySystem\backups"
    CreateDirectory "$APPDATA\IraqiDeliverySystem\exports"
    CreateDirectory "$APPDATA\IraqiDeliverySystem\logs"
SectionEnd

; تثبيت Visual C++ Redistributable إذا لزم الأمر
Section "Visual C++ Runtime" SecVCRedist
    ; فحص وجود Visual C++ Runtime
    ReadRegStr $0 HKLM "SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" "Installed"
    StrCmp $0 "1" vcredist_done
    
    ; تحميل وتثبيت Visual C++ Runtime
    MessageBox MB_YESNO "يتطلب هذا البرنامج Visual C++ Runtime. هل تريد تحميله وتثبيته؟" IDNO vcredist_done
    
    ; يمكن إضافة رابط التحميل هنا
    ExecShell "open" "https://aka.ms/vs/17/release/vc_redist.x64.exe"
    
    vcredist_done:
SectionEnd

; إعدادات الأمان
Section "Security Settings" SecSecurity
    ; إضافة استثناء Windows Defender إذا لزم الأمر
    WriteRegStr HKLM "SOFTWARE\Microsoft\Windows Defender\Exclusions\Paths" "$INSTDIR" "0"
SectionEnd

; تحديث متغيرات البيئة
Section "Environment Variables" SecEnvVars
    ; إضافة مسار التطبيق إلى PATH إذا لزم الأمر
    ; EnVar::SetHKLM
    ; EnVar::AddValue "PATH" "$INSTDIR"
    ; Pop $0
SectionEnd

; إنشاء ملف إعدادات افتراضي
Section "Default Settings" SecDefaultSettings
    FileOpen $0 "$APPDATA\IraqiDeliverySystem\settings.json" w
    FileWrite $0 '{'
    FileWrite $0 '"language": "ar",'
    FileWrite $0 '"theme": "light",'
    FileWrite $0 '"autoBackup": true,'
    FileWrite $0 '"backupInterval": 24,'
    FileWrite $0 '"currency": "IQD",'
    FileWrite $0 '"dateFormat": "dd/mm/yyyy",'
    FileWrite $0 '"firstRun": true'
    FileWrite $0 '}'
    FileClose $0
SectionEnd

; معالج إلغاء التثبيت
Section "Uninstall"
    ; حذف الملفات
    Delete "$INSTDIR\${PRODUCT_NAME}.exe"
    Delete "$INSTDIR\Uninstall.exe"
    RMDir /r "$INSTDIR"
    
    ; حذف الاختصارات
    Delete "$DESKTOP\نظام إدارة التوصيل العراقية.lnk"
    RMDir /r "$SMPROGRAMS\نظام إدارة التوصيل العراقية"
    
    ; حذف تسجيل الملفات
    DeleteRegKey HKCR ".idm"
    DeleteRegKey HKCR "IraqiDeliveryFile"
    
    ; حذف مفاتيح التسجيل
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
    
    ; سؤال المستخدم عن حذف البيانات
    MessageBox MB_YESNO "هل تريد حذف جميع بيانات التطبيق؟ (النسخ الاحتياطية، الإعدادات، إلخ)" IDNO keep_data
    RMDir /r "$APPDATA\IraqiDeliverySystem"
    keep_data:
SectionEnd

; وصف الأقسام
LangString DESC_SecDesktop ${LANG_ARABIC} "إنشاء اختصار على سطح المكتب"
LangString DESC_SecDesktop ${LANG_ENGLISH} "Create desktop shortcut"

LangString DESC_SecStartMenu ${LANG_ARABIC} "إنشاء اختصارات في قائمة ابدأ"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Create start menu shortcuts"

LangString DESC_SecFileAssoc ${LANG_ARABIC} "ربط ملفات النظام بالتطبيق"
LangString DESC_SecFileAssoc ${LANG_ENGLISH} "Associate system files with application"

LangString DESC_SecDataDir ${LANG_ARABIC} "إنشاء مجلدات البيانات"
LangString DESC_SecDataDir ${LANG_ENGLISH} "Create data directories"

; تعيين وصف الأقسام
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} $(DESC_SecFileAssoc)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDataDir} $(DESC_SecDataDir)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; دالة تشغيل بعد التثبيت
Function .onInstSuccess
    MessageBox MB_YESNO "تم تثبيت نظام إدارة شركة التوصيل العراقية بنجاح. هل تريد تشغيله الآن؟" IDNO no_run
    Exec "$INSTDIR\${PRODUCT_NAME}.exe"
    no_run:
FunctionEnd

; دالة فحص المتطلبات
Function .onInit
    ; فحص نظام التشغيل
    ${IfNot} ${AtLeastWin7}
        MessageBox MB_OK "هذا البرنامج يتطلب Windows 7 أو أحدث"
        Abort
    ${EndIf}
    
    ; فحص المساحة المتاحة
    ${DriveSpace} "$INSTDIR" "/D=F /S=M" $0
    IntCmp $0 100 space_ok space_ok no_space
    no_space:
        MessageBox MB_OK "المساحة المتاحة غير كافية. يتطلب 100 ميجابايت على الأقل"
        Abort
    space_ok:
FunctionEnd
