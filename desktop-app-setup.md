# دليل تحويل نظام إدارة شركة التوصيل العراقية إلى برنامج سطح مكتب
## Desktop Application Setup Guide

---

## 🖥️ **الطريقة الأولى: استخدام Electron (الموصى بها)**

### **المتطلبات:**
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### **خطوات التحويل:**

#### **الخطوة 1: تثبيت Node.js**
1. قم بتحميل Node.js من: https://nodejs.org/
2. ثبت النسخة LTS (Long Term Support)
3. تأكد من التثبيت بفتح Command Prompt وكتابة:
```bash
node --version
npm --version
```

#### **الخطوة 2: إعداد مشروع Electron**
1. افتح Command Prompt في مجلد المشروع
2. قم بتشغيل الأوامر التالية:

```bash
# إنشاء ملف package.json
npm init -y

# تثبيت Electron
npm install electron --save-dev

# تثبيت electron-builder لإنشاء ملف exe
npm install electron-builder --save-dev

# تثبيت electron-serve لتشغيل الملفات المحلية
npm install electron-serve --save
```

#### **الخطوة 3: إنشاء ملف main.js (ملف التشغيل الرئيسي)**
سيتم إنشاء هذا الملف تلقائياً في الخطوة التالية.

#### **الخطوة 4: تحديث package.json**
سيتم تحديث هذا الملف تلقائياً في الخطوة التالية.

---

## 🔧 **الطريقة الثانية: استخدام Tauri (خفيف وسريع)**

### **المتطلبات:**
- Rust (لغة البرمجة)
- Node.js

### **خطوات التحويل:**
1. تثبيت Rust من: https://rustup.rs/
2. تثبيت Tauri CLI:
```bash
npm install -g @tauri-apps/cli
```

3. إنشاء مشروع Tauri:
```bash
npm create tauri-app
```

---

## 📦 **الطريقة الثالثة: استخدام Neutralino.js (الأخف)**

### **المتطلبات:**
- Node.js

### **خطوات التحويل:**
1. تثبيت Neutralino CLI:
```bash
npm install -g @neutralinojs/neu
```

2. إنشاء مشروع جديد:
```bash
neu create iraqi-delivery-desktop
```

---

## 🌐 **الطريقة الرابعة: استخدام PWA (Progressive Web App)**

### **المزايا:**
- سهولة التطوير
- يعمل على جميع أنظمة التشغيل
- تحديثات تلقائية

### **خطوات التحويل:**
1. إضافة Service Worker
2. إنشاء Web App Manifest
3. تفعيل التثبيت على سطح المكتب

---

## ⚡ **الطريقة السريعة: استخدام nwjs**

### **المتطلبات:**
- Node.js

### **خطوات التحويل:**
```bash
# تثبيت nw
npm install -g nw

# تشغيل التطبيق
nw .
```

---

## 🎯 **التوصية:**

**أنصح باستخدام Electron** لأنه:
- الأكثر شيوعاً واستقراراً
- دعم ممتاز للتطبيقات العربية
- سهولة التطوير والصيانة
- إمكانيات متقدمة (إشعارات، قوائم، اختصارات)
- يستخدمه تطبيقات مشهورة مثل VS Code, Discord, WhatsApp Desktop

---

## 📋 **الخطوات التالية:**

1. **اختر الطريقة المناسبة** (أنصح بـ Electron)
2. **أخبرني بالطريقة المختارة** وسأقوم بإنشاء جميع الملفات المطلوبة
3. **سأقوم بإعداد التطبيق** مع جميع الميزات المطلوبة
4. **سأوضح لك كيفية إنشاء ملف exe** قابل للتوزيع

---

## 💡 **ملاحظات مهمة:**

- **حجم التطبيق:** Electron (~150MB), Tauri (~10MB), Neutralino (~5MB)
- **الأداء:** Tauri (الأسرع), Electron (جيد), Neutralino (متوسط)
- **سهولة التطوير:** Electron (الأسهل), Neutralino (متوسط), Tauri (متقدم)
- **التوافق:** جميعها تدعم Windows, macOS, Linux

أي طريقة تفضل أن نبدأ بها؟
