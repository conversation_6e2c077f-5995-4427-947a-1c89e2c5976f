// Modal System for Delivery Management
class ModalManager {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        this.createModalContainer();
        this.setupEventListeners();
    }

    createModalContainer() {
        if (document.getElementById('modal-container')) return;

        const modalContainer = document.createElement('div');
        modalContainer.id = 'modal-container';
        modalContainer.className = 'modal-overlay';
        modalContainer.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"></h3>
                    <button class="modal-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body"></div>
                <div class="modal-footer"></div>
            </div>
        `;
        document.body.appendChild(modalContainer);
    }

    setupEventListeners() {
        // Close modal when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal();
            }
            if (e.target.classList.contains('modal-close-btn') || e.target.closest('.modal-close-btn')) {
                this.closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal();
            }
        });
    }

    showModal(title, content, footer = '') {
        const modal = document.getElementById('modal-container');
        const modalTitle = modal.querySelector('.modal-title');
        const modalBody = modal.querySelector('.modal-body');
        const modalFooter = modal.querySelector('.modal-footer');

        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        modalFooter.innerHTML = footer;

        modal.style.display = 'flex';
        this.activeModal = modal;

        // Focus first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    closeModal() {
        const modal = document.getElementById('modal-container');
        if (modal) {
            modal.style.display = 'none';
            this.activeModal = null;
        }
    }

    // Add Order Modal
    showAddOrderModal() {
        const content = `
            <form id="add-order-form" class="modal-form">
                <div class="form-section">
                    <h4>معلومات العميل والطلب</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-name">اسم العميل:</label>
                            <input type="text" id="customer-name" class="neu-input" required placeholder="أدخل اسم العميل">
                        </div>
                        <div class="form-group">
                            <label for="order-price">سعر الطلب (د.ع):</label>
                            <input type="number" id="order-price" class="neu-input" required min="0" step="1000" placeholder="0">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>معلومات المرسل</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sender-name">اسم المرسل:</label>
                            <input type="text" id="sender-name" class="neu-input" required placeholder="أدخل اسم المرسل">
                        </div>
                        <div class="form-group">
                            <label for="sender-phone">رقم المرسل:</label>
                            <input type="tel" id="sender-phone" class="neu-input" required placeholder="+964...">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>معلومات المستلم</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="recipient-name">اسم المستلم:</label>
                            <input type="text" id="recipient-name" class="neu-input" required placeholder="أدخل اسم المستلم">
                        </div>
                        <div class="form-group">
                            <label for="recipient-phone">رقم المستلم:</label>
                            <input type="tel" id="recipient-phone" class="neu-input" required placeholder="+964...">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="delivery-address">العنوان:</label>
                        <textarea id="delivery-address" class="neu-input" rows="3" required placeholder="أدخل عنوان التوصيل بالتفصيل"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="delivery-region">المنطقة:</label>
                        <select id="delivery-region" class="neu-input" required>
                            <option value="">اختر المنطقة...</option>
                            <option value="1">بغداد - الكرخ</option>
                            <option value="2">بغداد - الرصافة</option>
                            <option value="3">بغداد - الصدر</option>
                            <option value="4">بغداد - الكاظمية</option>
                            <option value="5">بغداد - الأعظمية</option>
                            <option value="6">بغداد - المنصور</option>
                            <option value="7">بغداد - الدورة</option>
                            <option value="8">بغداد - الشعلة</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h4>تفاصيل إضافية</h4>
                    <div class="form-group">
                        <label for="order-notes">ملاحظات:</label>
                        <textarea id="order-notes" class="neu-input" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="delivery-fee">رسوم التوصيل (د.ع):</label>
                            <input type="number" id="delivery-fee" class="neu-input" required min="0" step="500" value="5000">
                        </div>
                        <div class="form-group">
                            <label for="order-priority">الأولوية:</label>
                            <select id="order-priority" class="neu-input" required>
                                <option value="normal">عادي</option>
                                <option value="urgent">عاجل</option>
                                <option value="express">سريع</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إلغاء
            </button>
            <button type="submit" form="add-order-form" class="neu-btn primary">
                <i class="fas fa-plus"></i>
                إضافة الطلب
            </button>
        `;

        this.showModal('إضافة طلب جديد', content, footer);

        // Setup form submission
        document.getElementById('add-order-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddOrder();
        });

        // Auto-calculate total amount
        this.setupAmountCalculation();
    }

    setupAmountCalculation() {
        const deliveryFee = document.getElementById('delivery-fee');
        const codAmount = document.getElementById('cod-amount');

        const updateTotal = () => {
            const fee = parseFloat(deliveryFee.value) || 0;
            const cod = parseFloat(codAmount.value) || 0;
            const total = fee + cod;
            
            // You can display total somewhere if needed
            console.log(`إجمالي المبلغ: ${total} د.ع`);
        };

        if (deliveryFee) deliveryFee.addEventListener('input', updateTotal);
        if (codAmount) codAmount.addEventListener('input', updateTotal);
    }

    handleAddOrder() {
        const formData = this.getFormData('add-order-form');

        // Validate required fields
        if (!this.validateOrderForm(formData)) {
            return;
        }

        // Generate order number and receipt number
        const orderNumber = this.generateOrderNumber();
        const receiptNumber = this.generateReceiptNumber();

        // Create new order object
        const newOrder = {
            id: orderNumber,
            receiptNumber: receiptNumber,
            customerName: formData.customerName,
            senderName: formData.senderName,
            senderPhone: formData.senderPhone,
            recipientName: formData.recipientName,
            recipientPhone: formData.recipientPhone,
            address: formData.deliveryAddress,
            region: document.getElementById('delivery-region').selectedOptions[0]?.text || 'غير محدد',
            regionId: parseInt(formData.deliveryRegion),
            orderPrice: parseFloat(formData.orderPrice),
            deliveryFee: parseFloat(formData.deliveryFee),
            totalAmount: parseFloat(formData.orderPrice) + parseFloat(formData.deliveryFee),
            notes: formData.orderNotes || '',
            priority: formData.orderPriority,
            status: 'pending',
            createdAt: new Date().toISOString().slice(0, 19).replace('T', ' '),
            updatedAt: new Date().toISOString().slice(0, 19).replace('T', ' ')
        };

        // Add to orders list using the new system
        if (window.OrdersManager) {
            window.OrdersManager.addOrder(newOrder);
        }

        // Log activity
        this.logOrderActivity(orderNumber, 'created', 'تم إنشاء الطلب', {
            customerName: newOrder.customerName,
            recipientName: newOrder.recipientName,
            totalAmount: newOrder.totalAmount,
            receiptNumber: receiptNumber
        });

        this.closeModal();
        this.showNotification(`تم إضافة الطلب بنجاح برقم الوصل: ${receiptNumber}`, 'success');
    }

    validateOrderForm(formData) {
        const requiredFields = [
            'customerName', 'senderName', 'senderPhone',
            'recipientName', 'recipientPhone', 'deliveryAddress', 'deliveryRegion',
            'orderPrice', 'deliveryFee', 'orderPriority'
        ];

        for (const field of requiredFields) {
            if (!formData[field] || formData[field].toString().trim() === '') {
                this.showNotification(`يرجى ملء جميع الحقول المطلوبة`, 'error');
                return false;
            }
        }

        // Validate phone numbers (more flexible format)
        const phoneRegex = /^(\+964|0)[0-9]{10}$/;
        if (!phoneRegex.test(formData.senderPhone.replace(/\s/g, ''))) {
            this.showNotification('رقم هاتف المرسل غير صحيح (يجب أن يبدأ بـ +964 أو 0)', 'error');
            return false;
        }
        if (!phoneRegex.test(formData.recipientPhone.replace(/\s/g, ''))) {
            this.showNotification('رقم هاتف المستلم غير صحيح (يجب أن يبدأ بـ +964 أو 0)', 'error');
            return false;
        }

        // Validate amounts
        if (parseFloat(formData.orderPrice) < 0) {
            this.showNotification('سعر الطلب يجب أن يكون أكبر من الصفر', 'error');
            return false;
        }

        if (parseFloat(formData.deliveryFee) <= 0) {
            this.showNotification('رسوم التوصيل يجب أن تكون أكبر من صفر', 'error');
            return false;
        }

        return true;
    }

    getFormData(formId) {
        const form = document.getElementById(formId);
        const formData = new FormData(form);
        const data = {};

        // Get all form inputs
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            const key = input.id.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
            data[key] = input.value;
        });

        return data;
    }

    generateOrderNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

        return `ORD-${year}-${month}${day}-${random}`;
    }

    generateReceiptNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');

        return `${year}${month}${day}${hours}${minutes}${random}`;
    }

    logOrderActivity(orderId, action, description, details = {}) {
        if (!window.ActivityLogger) return;

        window.ActivityLogger.log({
            orderId: orderId,
            action: action,
            description: description,
            details: details,
            userId: 'admin', // Get from current user session
            timestamp: new Date().toISOString()
        });
    }

    // Show Order Details Modal
    showOrderDetailsModal(orderId) {
        const order = window.OrdersManager?.orders.find(o => o.id === orderId);
        if (!order) {
            this.showNotification('الطلب غير موجود', 'error');
            return;
        }

        const content = `
            <div class="order-details-container">
                <div class="details-section">
                    <h4>معلومات الطلب</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>رقم الطلب:</label>
                            <span>${order.id}</span>
                        </div>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span class="status-badge ${order.status}">${this.getStatusText(order.status)}</span>
                        </div>
                        <div class="detail-item">
                            <label>الأولوية:</label>
                            <span class="priority-badge ${order.priority}">${this.getPriorityText(order.priority)}</span>
                        </div>
                        <div class="detail-item">
                            <label>نوع التوصيل:</label>
                            <span>${this.getDeliveryTypeText(order.deliveryType)}</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ الإنشاء:</label>
                            <span>${this.formatDateTime(order.createdAt)}</span>
                        </div>
                        <div class="detail-item">
                            <label>المندوب:</label>
                            <span>${order.driver || 'غير معين'}</span>
                        </div>
                    </div>
                </div>

                <div class="details-section">
                    <h4>معلومات العميل والمستلم</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>العميل:</label>
                            <span>${order.customer}</span>
                        </div>
                        <div class="detail-item">
                            <label>هاتف العميل:</label>
                            <span>${order.customerPhone}</span>
                        </div>
                        <div class="detail-item">
                            <label>المستلم:</label>
                            <span>${order.recipient}</span>
                        </div>
                        <div class="detail-item">
                            <label>هاتف المستلم:</label>
                            <span>${order.recipientPhone}</span>
                        </div>
                        <div class="detail-item">
                            <label>عنوان الاستلام:</label>
                            <span>${order.senderAddress}</span>
                        </div>
                        <div class="detail-item">
                            <label>عنوان التسليم:</label>
                            <span>${order.recipientAddress}</span>
                        </div>
                    </div>
                </div>

                <div class="details-section">
                    <h4>التفاصيل المالية</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>رسوم التوصيل:</label>
                            <span>${this.formatCurrency(order.amount)}</span>
                        </div>
                        <div class="detail-item">
                            <label>الدفع عند الاستلام:</label>
                            <span>${this.formatCurrency(order.codAmount)}</span>
                        </div>
                        <div class="detail-item">
                            <label>إجمالي المبلغ:</label>
                            <span class="total-amount">${this.formatCurrency(order.totalAmount)}</span>
                        </div>
                    </div>
                </div>

                <div class="details-section">
                    <h4>وصف الطرد</h4>
                    <p class="package-description">${order.packageDescription}</p>
                    ${order.specialInstructions ? `
                        <h4>تعليمات خاصة</h4>
                        <p class="special-instructions">${order.specialInstructions}</p>
                    ` : ''}
                </div>
            </div>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إغلاق
            </button>
            <button type="button" class="neu-btn info" onclick="window.ActivityLogger.showActivityLogModal('${orderId}')">
                <i class="fas fa-history"></i>
                سجل الحركات
            </button>
            <button type="button" class="neu-btn primary" onclick="window.ModalManager.showEditOrderModal('${orderId}')">
                <i class="fas fa-edit"></i>
                تعديل
            </button>
        `;

        this.showModal(`تفاصيل الطلب ${orderId}`, content, footer);
    }

    // Add Driver Modal
    showAddDriverModal() {
        const content = `
            <form id="add-driver-form" class="modal-form">
                <div class="form-section">
                    <h4>المعلومات الشخصية</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="driver-name">الاسم الكامل:</label>
                            <input type="text" id="driver-name" class="neu-input" required placeholder="أحمد محمد المندوب">
                        </div>
                        <div class="form-group">
                            <label for="driver-phone">رقم الهاتف <span class="optional-label">(اختياري)</span>:</label>
                            <input type="tel" id="driver-phone" class="neu-input" placeholder="+964...">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="driver-region">المنطقة المخصصة:</label>
                            <select id="driver-region" class="neu-input" required>
                                <option value="">اختر المنطقة...</option>
                                <option value="1">بغداد - الكرخ</option>
                                <option value="2">بغداد - الرصافة</option>
                                <option value="3">بغداد - الصدر</option>
                                <option value="4">بغداد - الكاظمية</option>
                                <option value="5">بغداد - الأعظمية</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="driver-address">العنوان:</label>
                        <textarea id="driver-address" class="neu-input" rows="2" required></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <h4>إعدادات العمولة</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="commission-amount">مبلغ العمولة (د.ع):</label>
                            <input type="number" id="commission-amount" class="neu-input" required min="5000" max="50000" step="1000" value="15000">
                            <small>مبلغ ثابت لكل طلب مكتمل</small>
                        </div>
                        <div class="form-group">
                            <label for="driver-status">الحالة:</label>
                            <select id="driver-status" class="neu-input" required>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إلغاء
            </button>
            <button type="submit" form="add-driver-form" class="neu-btn primary">
                <i class="fas fa-user-plus"></i>
                إضافة المندوب
            </button>
        `;

        this.showModal('إضافة مندوب جديد', content, footer);

        // Setup form submission
        document.getElementById('add-driver-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddDriver();
        });
    }

    handleAddDriver() {
        const formData = this.getFormData('add-driver-form');

        if (!this.validateDriverForm(formData)) {
            return;
        }

        const newDriver = {
            id: Date.now(),
            name: formData.driverName,
            phone: formData.driverPhone || 'غير محدد',
            address: formData.driverAddress,
            regionId: parseInt(formData.driverRegion),
            regionName: document.getElementById('driver-region').selectedOptions[0]?.text || 'غير محدد',
            commissionAmount: parseFloat(formData.commissionAmount),
            rating: 5.0,
            totalDeliveries: 0,
            completedDeliveries: 0,
            isAvailable: true,
            status: formData.driverStatus,
            joinDate: new Date().toISOString().split('T')[0],
            lastActive: new Date().toISOString().slice(0, 19).replace('T', ' '),
            monthlyEarnings: 0
        };

        if (window.DriversManager) {
            window.DriversManager.drivers.push(newDriver);
            window.DriversManager.renderDrivers();
            window.DriversManager.updateStats();
        }

        this.closeModal();
        this.showNotification('تم إضافة المندوب بنجاح', 'success');
    }

    validateDriverForm(formData) {
        const requiredFields = [
            'driverName', 'driverRegion', 'driverAddress', 'commissionAmount', 'driverStatus'
        ];

        for (const field of requiredFields) {
            if (!formData[field] || formData[field].trim() === '') {
                this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return false;
            }
        }

        // Validate phone number if provided (optional)
        if (formData.driverPhone && formData.driverPhone.trim() !== '') {
            const phoneRegex = /^\+964[0-9]{10}$/;
            if (!phoneRegex.test(formData.driverPhone)) {
                this.showNotification('رقم الهاتف غير صحيح (يجب أن يبدأ بـ +964 ويحتوي على 10 أرقام)', 'error');
                return false;
            }
        }

        // Validate commission amount
        if (parseFloat(formData.commissionAmount) < 5000) {
            this.showNotification('مبلغ العمولة يجب أن يكون 5000 د.ع على الأقل', 'error');
            return false;
        }

        return true;
    }

    // Add Customer Modal
    showAddCustomerModal() {
        const content = `
            <form id="add-customer-form" class="modal-form">
                <div class="form-section">
                    <h4>نوع العميل</h4>
                    <div class="form-group">
                        <label for="customer-type">نوع العميل:</label>
                        <select id="customer-type" class="neu-input" required onchange="window.ModalManager.toggleCustomerType()">
                            <option value="individual">فردي</option>
                            <option value="company">شركة</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h4>المعلومات الأساسية</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-name">الاسم:</label>
                            <input type="text" id="customer-name" class="neu-input" required placeholder="اسم العميل">
                        </div>
                        <div class="form-group" id="company-name-group" style="display: none;">
                            <label for="company-name">اسم الشركة:</label>
                            <input type="text" id="company-name" class="neu-input" placeholder="اسم الشركة">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-phone">رقم الهاتف:</label>
                            <input type="tel" id="customer-phone" class="neu-input" required placeholder="+964...">
                        </div>
                        <div class="form-group">
                            <label for="customer-email">البريد الإلكتروني:</label>
                            <input type="email" id="customer-email" class="neu-input" required placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="customer-address">العنوان:</label>
                        <textarea id="customer-address" class="neu-input" rows="2" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="customer-region">المنطقة:</label>
                        <select id="customer-region" class="neu-input" required>
                            <option value="">اختر المنطقة...</option>
                            <option value="1">بغداد - الكرخ</option>
                            <option value="2">بغداد - الرصافة</option>
                            <option value="3">بغداد - الصدر</option>
                            <option value="4">بغداد - الكاظمية</option>
                            <option value="5">بغداد - الأعظمية</option>
                        </select>
                    </div>
                </div>

                <div class="form-section" id="commission-section" style="display: none;">
                    <h4>عمولة العميل</h4>
                    <div class="form-group">
                        <label for="commission-amount">مبلغ العمولة (د.ع):</label>
                        <input type="number" id="commission-amount" class="neu-input" min="0" step="500" placeholder="0">
                        <small>مبلغ يخصم من فاتورة العميل لكل طلب</small>
                    </div>
                </div>
            </form>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إلغاء
            </button>
            <button type="submit" form="add-customer-form" class="neu-btn primary">
                <i class="fas fa-user-plus"></i>
                إضافة العميل
            </button>
        `;

        this.showModal('إضافة عميل جديد', content, footer);

        // Setup form submission
        document.getElementById('add-customer-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddCustomer();
        });
    }

    toggleCustomerType() {
        const customerType = document.getElementById('customer-type').value;
        const companyNameGroup = document.getElementById('company-name-group');
        const commissionSection = document.getElementById('commission-section');

        if (customerType === 'company') {
            companyNameGroup.style.display = 'block';
            commissionSection.style.display = 'block';
            document.getElementById('company-name').required = true;
        } else {
            companyNameGroup.style.display = 'none';
            commissionSection.style.display = 'none';
            document.getElementById('company-name').required = false;
        }
    }

    handleAddCustomer() {
        const formData = this.getFormData('add-customer-form');

        if (!this.validateCustomerForm(formData)) {
            return;
        }

        const newCustomer = {
            id: Date.now(),
            name: formData.customerName,
            type: formData.customerType,
            companyName: formData.companyName || null,
            phone: formData.customerPhone,
            email: formData.customerEmail,
            address: formData.customerAddress,
            regionId: parseInt(formData.customerRegion),
            regionName: document.getElementById('customer-region').selectedOptions[0]?.text || 'غير محدد',
            commissionAmount: formData.commissionAmount ? parseFloat(formData.commissionAmount) : null,
            isActive: true,
            joinDate: new Date().toISOString().split('T')[0],
            lastOrderDate: null,
            totalOrders: 0,
            totalAmount: 0,
            averageOrderValue: 0
        };

        if (window.CustomersManager) {
            window.CustomersManager.customers.push(newCustomer);
            window.CustomersManager.renderCustomers();
            window.CustomersManager.updateStats();
        }

        this.closeModal();
        this.showNotification('تم إضافة العميل بنجاح', 'success');
    }

    validateCustomerForm(formData) {
        const requiredFields = ['customerName', 'customerPhone', 'customerEmail', 'customerAddress', 'customerRegion'];

        for (const field of requiredFields) {
            if (!formData[field] || formData[field].trim() === '') {
                this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return false;
            }
        }

        // If company type, company name is required
        if (formData.customerType === 'company' && (!formData.companyName || formData.companyName.trim() === '')) {
            this.showNotification('يرجى إدخال اسم الشركة', 'error');
            return false;
        }

        // Validate phone number
        const phoneRegex = /^\+964[0-9]{10}$/;
        if (!phoneRegex.test(formData.customerPhone)) {
            this.showNotification('رقم الهاتف غير صحيح', 'error');
            return false;
        }

        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.customerEmail)) {
            this.showNotification('البريد الإلكتروني غير صحيح', 'error');
            return false;
        }

        return true;
    }

    // Show Transaction Details Modal
    showTransactionDetailsModal(transactionId) {
        const transaction = window.FinanceManager?.transactions.find(t => t.id == transactionId);
        if (!transaction) {
            this.showNotification('المعاملة غير موجودة', 'error');
            return;
        }

        const content = `
            <div class="transaction-details-container">
                <div class="details-section">
                    <h4>معلومات المعاملة</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>رقم المعاملة:</label>
                            <span>${transaction.id}</span>
                        </div>
                        <div class="detail-item">
                            <label>التاريخ والوقت:</label>
                            <span>${this.formatDateTime(transaction.date)}</span>
                        </div>
                        <div class="detail-item">
                            <label>نوع المعاملة:</label>
                            <span class="transaction-type ${transaction.type}">${this.getTransactionTypeText(transaction.type)}</span>
                        </div>
                        <div class="detail-item">
                            <label>المبلغ:</label>
                            <span class="amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                                ${this.formatCurrency(Math.abs(transaction.amount))}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>الرصيد بعد المعاملة:</label>
                            <span>${this.formatCurrency(transaction.balance)}</span>
                        </div>
                    </div>
                </div>

                <div class="details-section">
                    <h4>تفاصيل المعاملة</h4>
                    <p class="transaction-description">${transaction.description}</p>

                    ${transaction.orderId ? `
                        <div class="related-order">
                            <strong>الطلب المرتبط:</strong>
                            <a href="#" onclick="window.ModalManager.showOrderDetailsModal('${transaction.orderId}')">${transaction.orderId}</a>
                        </div>
                    ` : ''}

                    ${transaction.driverId ? `
                        <div class="related-driver">
                            <strong>المندوب المرتبط:</strong>
                            <span>مندوب رقم ${transaction.driverId}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إغلاق
            </button>
            <button type="button" class="neu-btn primary" onclick="window.ModalManager.printTransaction(${transactionId})">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        `;

        this.showModal(`تفاصيل المعاملة ${transactionId}`, content, footer);
    }

    getTransactionTypeText(type) {
        const types = {
            'revenue': 'إيراد',
            'commission': 'عمولة',
            'expense': 'مصروف'
        };
        return types[type] || type;
    }

    printTransaction(transactionId) {
        alert(`سيتم طباعة المعاملة ${transactionId}`);
    }

    // Add Region Modal
    showAddRegionModal() {
        const content = `
            <form id="add-region-form" class="modal-form">
                <div class="form-section">
                    <h4>معلومات المنطقة</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="region-name">اسم المنطقة:</label>
                            <input type="text" id="region-name" class="neu-input" required placeholder="مثال: الكرخ">
                        </div>
                        <div class="form-group">
                            <label for="region-city">المدينة:</label>
                            <input type="text" id="region-city" class="neu-input" required placeholder="مثال: بغداد">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="region-province">المحافظة:</label>
                            <select id="region-province" class="neu-input" required>
                                <option value="">اختر المحافظة...</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="نينوى">نينوى</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="بابل">بابل</option>
                                <option value="ديالى">ديالى</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="واسط">واسط</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                                <option value="كركوك">كركوك</option>
                                <option value="دهوك">دهوك</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="ميسان">ميسان</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="delivery-fee">رسوم التوصيل (د.ع):</label>
                            <input type="number" id="delivery-fee" class="neu-input" required min="1000" step="500" value="5000">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="region-description">وصف المنطقة:</label>
                        <textarea id="region-description" class="neu-input" rows="2" placeholder="وصف مختصر للمنطقة..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="region-status">الحالة:</label>
                        <select id="region-status" class="neu-input" required>
                            <option value="true">نشطة</option>
                            <option value="false">غير نشطة</option>
                        </select>
                    </div>
                </div>
            </form>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إلغاء
            </button>
            <button type="submit" form="add-region-form" class="neu-btn primary">
                <i class="fas fa-map-marker-alt"></i>
                إضافة المنطقة
            </button>
        `;

        this.showModal('إضافة منطقة جديدة', content, footer);

        // Setup form submission
        document.getElementById('add-region-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddRegion();
        });
    }

    handleAddRegion() {
        const formData = this.getFormData('add-region-form');

        if (!this.validateRegionForm(formData)) {
            return;
        }

        const newRegion = {
            id: Date.now(),
            name: formData.regionName,
            city: formData.regionCity,
            province: formData.regionProvince,
            deliveryFee: parseFloat(formData.deliveryFee),
            description: formData.regionDescription || `منطقة ${formData.regionName} في ${formData.regionProvince}`,
            isActive: formData.regionStatus === 'true',
            driversCount: 0,
            ordersCount: 0,
            createdAt: new Date().toISOString().split('T')[0]
        };

        if (window.RegionsManager) {
            window.RegionsManager.regions.push(newRegion);
            window.RegionsManager.renderRegions();
            window.RegionsManager.updateStats();
        }

        this.closeModal();
        this.showNotification('تم إضافة المنطقة بنجاح', 'success');
    }

    validateRegionForm(formData) {
        const requiredFields = ['regionName', 'regionCity', 'regionProvince', 'deliveryFee', 'regionStatus'];

        for (const field of requiredFields) {
            if (!formData[field] || formData[field].trim() === '') {
                this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return false;
            }
        }

        // Validate delivery fee
        if (parseFloat(formData.deliveryFee) < 1000) {
            this.showNotification('رسوم التوصيل يجب أن تكون 1000 د.ع على الأقل', 'error');
            return false;
        }

        return true;
    }

    // Generate Invoice Modal
    showGenerateInvoiceModal() {
        const customers = window.CustomersManager?.customers.filter(c => c.type === 'company' && c.isActive) || [];

        const content = `
            <form id="generate-invoice-form" class="modal-form">
                <div class="form-section">
                    <h4>معلومات الفاتورة</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="invoice-customer">العميل:</label>
                            <select id="invoice-customer" class="neu-input" required>
                                <option value="">اختر العميل...</option>
                                ${customers.map(customer =>
                                    `<option value="${customer.id}">${customer.companyName || customer.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="invoice-period">فترة الفاتورة:</label>
                            <select id="invoice-period" class="neu-input" required>
                                <option value="current_month">الشهر الحالي</option>
                                <option value="last_month">الشهر الماضي</option>
                                <option value="custom">فترة مخصصة</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row" id="custom-period-row" style="display: none;">
                        <div class="form-group">
                            <label for="period-from">من تاريخ:</label>
                            <input type="date" id="period-from" class="neu-input">
                        </div>
                        <div class="form-group">
                            <label for="period-to">إلى تاريخ:</label>
                            <input type="date" id="period-to" class="neu-input">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="due-date">تاريخ الاستحقاق:</label>
                            <input type="date" id="due-date" class="neu-input" required>
                        </div>
                        <div class="form-group">
                            <label for="additional-fees">رسوم إضافية (د.ع):</label>
                            <input type="number" id="additional-fees" class="neu-input" min="0" step="1000" value="0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="invoice-notes">ملاحظات:</label>
                        <textarea id="invoice-notes" class="neu-input" rows="2" placeholder="ملاحظات إضافية للفاتورة..."></textarea>
                    </div>
                </div>
            </form>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إلغاء
            </button>
            <button type="submit" form="generate-invoice-form" class="neu-btn primary">
                <i class="fas fa-file-invoice"></i>
                إنشاء الفاتورة
            </button>
        `;

        this.showModal('إنشاء فاتورة جديدة', content, footer);

        // Setup form submission
        document.getElementById('generate-invoice-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleGenerateInvoice();
        });

        // Setup period change handler
        document.getElementById('invoice-period').addEventListener('change', (e) => {
            const customRow = document.getElementById('custom-period-row');
            if (e.target.value === 'custom') {
                customRow.style.display = 'flex';
            } else {
                customRow.style.display = 'none';
            }
        });

        // Set default due date (30 days from now)
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        document.getElementById('due-date').value = dueDate.toISOString().split('T')[0];
    }

    handleGenerateInvoice() {
        const formData = this.getFormData('generate-invoice-form');

        if (!this.validateInvoiceForm(formData)) {
            return;
        }

        // Generate invoice ID
        const invoiceId = this.generateInvoiceNumber();

        // Get customer data
        const customer = window.CustomersManager?.customers.find(c => c.id == formData.invoiceCustomer);
        if (!customer) {
            this.showNotification('العميل غير موجود', 'error');
            return;
        }

        // Calculate invoice data (mock calculation)
        const ordersCount = Math.floor(Math.random() * 20) + 5;
        const totalAmount = ordersCount * 50000; // Assuming 50,000 per order
        const commissionAmount = customer.commissionAmount ? (ordersCount * customer.commissionAmount) : 0;
        const additionalFees = parseFloat(formData.additionalFees) || 0;
        const netAmount = totalAmount - commissionAmount + additionalFees;

        const newInvoice = {
            id: invoiceId,
            customerId: customer.id,
            customerName: customer.companyName || customer.name,
            customerType: customer.type,
            issueDate: new Date().toISOString().split('T')[0],
            dueDate: formData.dueDate,
            ordersCount: ordersCount,
            totalAmount: totalAmount,
            commissionAmount: commissionAmount,
            additionalFees: additionalFees,
            netAmount: netAmount,
            status: 'pending',
            paidDate: null,
            notes: formData.invoiceNotes || '',
            orders: [] // Would be populated with actual orders
        };

        if (window.BillingManager) {
            window.BillingManager.invoices.unshift(newInvoice);
            window.BillingManager.renderInvoices();
            window.BillingManager.updateStats();
        }

        this.closeModal();
        this.showNotification('تم إنشاء الفاتورة بنجاح', 'success');
    }

    validateInvoiceForm(formData) {
        const requiredFields = ['invoiceCustomer', 'invoicePeriod', 'dueDate'];

        for (const field of requiredFields) {
            if (!formData[field] || formData[field].trim() === '') {
                this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
                return false;
            }
        }

        // Validate custom period if selected
        if (formData.invoicePeriod === 'custom') {
            if (!formData.periodFrom || !formData.periodTo) {
                this.showNotification('يرجى تحديد فترة الفاتورة', 'error');
                return false;
            }
            if (new Date(formData.periodFrom) >= new Date(formData.periodTo)) {
                this.showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
                return false;
            }
        }

        // Validate due date
        if (new Date(formData.dueDate) <= new Date()) {
            this.showNotification('تاريخ الاستحقاق يجب أن يكون في المستقبل', 'error');
            return false;
        }

        return true;
    }

    generateInvoiceNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');

        return `INV-${year}-${month}${day}-${random}`;
    }

    // Show Invoice Details Modal
    showInvoiceDetailsModal(invoiceId) {
        const invoice = window.BillingManager?.invoices.find(i => i.id === invoiceId);
        if (!invoice) {
            this.showNotification('الفاتورة غير موجودة', 'error');
            return;
        }

        const content = `
            <div class="invoice-details-container">
                <div class="invoice-header-details">
                    <div class="invoice-info">
                        <h3>فاتورة رقم: ${invoice.id}</h3>
                        <div class="invoice-meta">
                            <div class="meta-item">
                                <label>العميل:</label>
                                <span>${invoice.customerName}</span>
                            </div>
                            <div class="meta-item">
                                <label>تاريخ الإصدار:</label>
                                <span>${this.formatDate(invoice.issueDate)}</span>
                            </div>
                            <div class="meta-item">
                                <label>تاريخ الاستحقاق:</label>
                                <span>${this.formatDate(invoice.dueDate)}</span>
                            </div>
                            <div class="meta-item">
                                <label>الحالة:</label>
                                <span class="status-badge ${invoice.status}">${this.getInvoiceStatusText(invoice.status)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="invoice-breakdown">
                    <h4>تفاصيل الفاتورة</h4>
                    <table class="breakdown-table">
                        <thead>
                            <tr>
                                <th>البيان</th>
                                <th>الكمية</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>رسوم التوصيل</td>
                                <td>${invoice.ordersCount} طلب</td>
                                <td>${this.formatCurrency(invoice.totalAmount)}</td>
                            </tr>
                            ${invoice.commissionAmount > 0 ? `
                                <tr>
                                    <td>خصم عمولة العميل</td>
                                    <td>-</td>
                                    <td>-${this.formatCurrency(invoice.commissionAmount)}</td>
                                </tr>
                            ` : ''}
                            ${invoice.additionalFees > 0 ? `
                                <tr>
                                    <td>رسوم إضافية</td>
                                    <td>-</td>
                                    <td>${this.formatCurrency(invoice.additionalFees)}</td>
                                </tr>
                            ` : ''}
                            <tr class="total-row">
                                <td><strong>صافي المبلغ المستحق</strong></td>
                                <td>-</td>
                                <td><strong>${this.formatCurrency(invoice.netAmount)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                ${invoice.notes ? `
                    <div class="invoice-notes">
                        <h4>ملاحظات</h4>
                        <p>${invoice.notes}</p>
                    </div>
                ` : ''}
            </div>
        `;

        const footer = `
            <button type="button" class="neu-btn secondary" onclick="window.ModalManager.closeModal()">
                إغلاق
            </button>
            <button type="button" class="neu-btn info" onclick="window.BillingManager.printInvoice('${invoiceId}')">
                <i class="fas fa-print"></i>
                طباعة
            </button>
            ${invoice.status !== 'paid' ? `
                <button type="button" class="neu-btn success" onclick="window.BillingManager.markAsPaid('${invoiceId}'); window.ModalManager.closeModal();">
                    <i class="fas fa-check"></i>
                    تسديد
                </button>
            ` : ''}
        `;

        this.showModal(`تفاصيل الفاتورة ${invoiceId}`, content, footer);
    }

    getInvoiceStatusText(status) {
        const statuses = {
            'pending': 'معلقة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة'
        };
        return statuses[status] || status;
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'assigned': 'معين',
            'picked_up': 'تم الاستلام',
            'in_transit': 'قيد التوصيل',
            'delivered': 'تم التسليم',
            'returned': 'مرتجع',
            'cancelled': 'ملغي',
            'postponed': 'مؤجل'
        };
        return statuses[status] || status;
    }

    getPriorityText(priority) {
        const priorities = {
            'normal': 'عادي',
            'urgent': 'عاجل',
            'express': 'سريع'
        };
        return priorities[priority] || priority;
    }

    getDeliveryTypeText(type) {
        const types = {
            'standard': 'عادي',
            'same_day': 'نفس اليوم',
            'express': 'سريع'
        };
        return types[type] || type;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-IQ') + ' ' + date.toLocaleTimeString('ar-IQ', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
}

// Initialize Modal Manager
window.ModalManager = new ModalManager();
