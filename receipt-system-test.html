<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام رقم الوصل والباركود المحسن</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .test-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .test-progress {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            font-weight: 600;
            color: #495057;
        }

        .test-content {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .test-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }

        .test-name {
            font-weight: 600;
            color: #495057;
        }

        .test-description {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .test-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .status-pending {
            background: #6c757d;
        }

        .status-running {
            background: #ffc107;
            animation: pulse 1.5s infinite;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .test-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .summary-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .success-number { color: #28a745; }
        .error-number { color: #dc3545; }
        .total-number { color: #495057; }
        .percentage-number { color: #17a2b8; }

        .test-log {
            background: #343a40;
            color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .action-btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .action-btn.warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .action-btn.danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .test-container {
                margin: 10px;
                border-radius: 15px;
            }

            .test-header h1 {
                font-size: 2rem;
            }

            .test-content {
                padding: 20px;
            }

            .test-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .test-status {
                align-self: flex-end;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> اختبار نظام رقم الوصل والباركود المحسن</h1>
            <p>اختبار شامل لجميع الميزات والوظائف الجديدة</p>
        </div>

        <div class="test-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">جاهز للبدء - 0% مكتمل</div>
        </div>

        <div class="test-content">
            <div class="action-buttons">
                <button class="action-btn" id="run-all-tests">
                    <i class="fas fa-play"></i>
                    تشغيل جميع الاختبارات
                </button>
                <button class="action-btn success" id="run-basic-tests">
                    <i class="fas fa-check"></i>
                    الاختبارات الأساسية
                </button>
                <button class="action-btn warning" id="run-advanced-tests">
                    <i class="fas fa-cogs"></i>
                    الاختبارات المتقدمة
                </button>
                <button class="action-btn danger" id="reset-tests">
                    <i class="fas fa-redo"></i>
                    إعادة تعيين
                </button>
            </div>

            <!-- Basic System Tests -->
            <div class="test-section">
                <h2><i class="fas fa-cog"></i> اختبارات النظام الأساسي</h2>
                
                <div class="test-item" data-test="load-receipt-system">
                    <div>
                        <div class="test-name">تحميل نظام إدارة أرقام الوصل</div>
                        <div class="test-description">التحقق من تحميل وتهيئة ReceiptSystem بشكل صحيح</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-load-receipt-system">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('load-receipt-system')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="load-barcode-system">
                    <div>
                        <div class="test-name">تحميل نظام الباركود المتقدم</div>
                        <div class="test-description">التحقق من تحميل وتهيئة AdvancedBarcodeSystem بشكل صحيح</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-load-barcode-system">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('load-barcode-system')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="load-api-client">
                    <div>
                        <div class="test-name">تحميل عميل API</div>
                        <div class="test-description">التحقق من تحميل وتهيئة ReceiptAPIClient بشكل صحيح</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-load-api-client">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('load-api-client')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="load-advanced-features">
                    <div>
                        <div class="test-name">تحميل الميزات المتقدمة</div>
                        <div class="test-description">التحقق من تحميل وتهيئة AdvancedReceiptFeatures بشكل صحيح</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-load-advanced-features">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('load-advanced-features')">اختبار</button>
                    </div>
                </div>
            </div>

            <!-- Receipt Generation Tests -->
            <div class="test-section">
                <h2><i class="fas fa-receipt"></i> اختبارات إنشاء أرقام الوصل</h2>
                
                <div class="test-item" data-test="generate-auto-receipt">
                    <div>
                        <div class="test-name">إنشاء رقم وصل تلقائي</div>
                        <div class="test-description">اختبار إنشاء رقم وصل تلقائي بالتنسيق الصحيح</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-generate-auto-receipt">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('generate-auto-receipt')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="generate-manual-receipt">
                    <div>
                        <div class="test-name">إنشاء رقم وصل يدوي</div>
                        <div class="test-description">اختبار إنشاء رقم وصل يدوي مع التحقق من التكرار</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-generate-manual-receipt">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('generate-manual-receipt')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="validate-receipt-format">
                    <div>
                        <div class="test-name">التحقق من تنسيق رقم الوصل</div>
                        <div class="test-description">اختبار التحقق من صحة تنسيق أرقام الوصل</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-validate-receipt-format">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('validate-receipt-format')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="check-duplicate-receipt">
                    <div>
                        <div class="test-name">فحص تكرار أرقام الوصل</div>
                        <div class="test-description">اختبار منع إنشاء أرقام وصل مكررة</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-check-duplicate-receipt">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('check-duplicate-receipt')">اختبار</button>
                    </div>
                </div>
            </div>

            <!-- Barcode Tests -->
            <div class="test-section">
                <h2><i class="fas fa-qrcode"></i> اختبارات نظام الباركود</h2>
                
                <div class="test-item" data-test="generate-code128">
                    <div>
                        <div class="test-name">إنشاء باركود Code128</div>
                        <div class="test-description">اختبار إنشاء باركود بنوع Code128</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-generate-code128">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('generate-code128')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="generate-qr-code">
                    <div>
                        <div class="test-name">إنشاء رمز QR</div>
                        <div class="test-description">اختبار إنشاء رمز QR للبيانات</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-generate-qr-code">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('generate-qr-code')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="barcode-settings">
                    <div>
                        <div class="test-name">إعدادات الباركود</div>
                        <div class="test-description">اختبار تطبيق إعدادات مختلفة للباركود</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-barcode-settings">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('barcode-settings')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="barcode-validation">
                    <div>
                        <div class="test-name">التحقق من بيانات الباركود</div>
                        <div class="test-description">اختبار التحقق من صحة البيانات قبل إنشاء الباركود</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-barcode-validation">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('barcode-validation')">اختبار</button>
                    </div>
                </div>
            </div>

            <!-- Integration Tests -->
            <div class="test-section">
                <h2><i class="fas fa-link"></i> اختبارات التكامل</h2>
                
                <div class="test-item" data-test="orders-integration">
                    <div>
                        <div class="test-name">التكامل مع نظام الطلبات</div>
                        <div class="test-description">اختبار ربط أرقام الوصل مع الطلبات</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-orders-integration">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('orders-integration')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="search-integration">
                    <div>
                        <div class="test-name">التكامل مع نظام البحث</div>
                        <div class="test-description">اختبار البحث برقم الوصل في نظام الطلبات</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-search-integration">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('search-integration')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="print-integration">
                    <div>
                        <div class="test-name">التكامل مع نظام الطباعة</div>
                        <div class="test-description">اختبار طباعة أرقام الوصل والباركود</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-print-integration">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('print-integration')">اختبار</button>
                    </div>
                </div>
            </div>

            <!-- Advanced Features Tests -->
            <div class="test-section">
                <h2><i class="fas fa-star"></i> اختبارات الميزات المتقدمة</h2>
                
                <div class="test-item" data-test="export-functionality">
                    <div>
                        <div class="test-name">وظائف التصدير</div>
                        <div class="test-description">اختبار تصدير أرقام الوصل بصيغ مختلفة</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-export-functionality">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('export-functionality')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="import-functionality">
                    <div>
                        <div class="test-name">وظائف الاستيراد</div>
                        <div class="test-description">اختبار استيراد أرقام الوصل من ملفات خارجية</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-import-functionality">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('import-functionality')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="statistics-generation">
                    <div>
                        <div class="test-name">إنشاء الإحصائيات</div>
                        <div class="test-description">اختبار إنشاء إحصائيات مفصلة لأرقام الوصل</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-statistics-generation">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('statistics-generation')">اختبار</button>
                    </div>
                </div>

                <div class="test-item" data-test="batch-operations">
                    <div>
                        <div class="test-name">العمليات المجمعة</div>
                        <div class="test-description">اختبار إنشاء وإدارة أرقام الوصل بشكل مجمع</div>
                    </div>
                    <div class="test-status">
                        <div class="status-icon status-pending" id="status-batch-operations">
                            <i class="fas fa-clock"></i>
                        </div>
                        <button class="test-button" onclick="runSingleTest('batch-operations')">اختبار</button>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="test-results" id="test-results" style="display: none;">
                <h2><i class="fas fa-chart-bar"></i> نتائج الاختبارات</h2>
                
                <div class="test-summary">
                    <div class="summary-card">
                        <div class="summary-number total-number" id="total-tests">0</div>
                        <div class="summary-label">إجمالي الاختبارات</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number success-number" id="passed-tests">0</div>
                        <div class="summary-label">اختبارات ناجحة</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number error-number" id="failed-tests">0</div>
                        <div class="summary-label">اختبارات فاشلة</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number percentage-number" id="success-rate">0%</div>
                        <div class="summary-label">معدل النجاح</div>
                    </div>
                </div>

                <div class="test-log" id="test-log"></div>
            </div>
        </div>
    </div>

    <!-- Load external libraries -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Load our systems -->
    <script src="assets/js/receipt-system.js"></script>
    <script src="assets/js/advanced-barcode.js"></script>
    <script src="assets/js/receipt-api-client.js"></script>
    <script src="assets/js/advanced-receipt-features.js"></script>

    <script>
        // Enhanced Receipt System Test Suite
        class ReceiptSystemTestSuite {
            constructor() {
                this.tests = new Map();
                this.results = {
                    total: 0,
                    passed: 0,
                    failed: 0,
                    running: false
                };
                this.logMessages = [];
                this.init();
            }

            init() {
                this.setupTests();
                this.setupEventListeners();
                this.updateUI();
            }

            setupEventListeners() {
                document.getElementById('run-all-tests').addEventListener('click', () => {
                    this.runAllTests();
                });

                document.getElementById('run-basic-tests').addEventListener('click', () => {
                    this.runTestCategory('basic');
                });

                document.getElementById('run-advanced-tests').addEventListener('click', () => {
                    this.runTestCategory('advanced');
                });

                document.getElementById('reset-tests').addEventListener('click', () => {
                    this.resetTests();
                });
            }

            setupTests() {
                // Basic System Tests
                this.addTest('load-receipt-system', 'basic', 'تحميل نظام إدارة أرقام الوصل', async () => {
                    if (typeof window.ReceiptSystem === 'undefined') {
                        throw new Error('ReceiptSystem غير محمل');
                    }
                    if (typeof window.ReceiptSystem.generateReceiptNumber !== 'function') {
                        throw new Error('وظائف ReceiptSystem غير متوفرة');
                    }
                    return 'تم تحميل ReceiptSystem بنجاح';
                });

                this.addTest('load-barcode-system', 'basic', 'تحميل نظام الباركود المتقدم', async () => {
                    if (typeof window.AdvancedBarcodeSystem === 'undefined') {
                        throw new Error('AdvancedBarcodeSystem غير محمل');
                    }
                    if (typeof window.AdvancedBarcodeSystem.generateBarcode !== 'function') {
                        throw new Error('وظائف AdvancedBarcodeSystem غير متوفرة');
                    }
                    return 'تم تحميل AdvancedBarcodeSystem بنجاح';
                });

                this.addTest('load-api-client', 'basic', 'تحميل عميل API', async () => {
                    if (typeof window.ReceiptAPIClient === 'undefined') {
                        throw new Error('ReceiptAPIClient غير محمل');
                    }
                    if (typeof window.ReceiptAPIClient.generateReceiptNumber !== 'function') {
                        throw new Error('وظائف ReceiptAPIClient غير متوفرة');
                    }
                    return 'تم تحميل ReceiptAPIClient بنجاح';
                });

                this.addTest('load-advanced-features', 'basic', 'تحميل الميزات المتقدمة', async () => {
                    if (typeof window.AdvancedReceiptFeatures === 'undefined') {
                        throw new Error('AdvancedReceiptFeatures غير محمل');
                    }
                    if (typeof window.AdvancedReceiptFeatures.exportReceiptNumbers !== 'function') {
                        throw new Error('وظائف AdvancedReceiptFeatures غير متوفرة');
                    }
                    return 'تم تحميل AdvancedReceiptFeatures بنجاح';
                });

                // Receipt Generation Tests
                this.addTest('generate-auto-receipt', 'basic', 'إنشاء رقم وصل تلقائي', async () => {
                    const receiptNumber = window.ReceiptSystem.generateReceiptNumber();
                    if (!receiptNumber) {
                        throw new Error('فشل في إنشاء رقم الوصل');
                    }
                    if (!receiptNumber.match(/^IQ-\d{4}-\d{6}$/)) {
                        throw new Error('تنسيق رقم الوصل غير صحيح: ' + receiptNumber);
                    }
                    return `تم إنشاء رقم الوصل: ${receiptNumber}`;
                });

                this.addTest('generate-manual-receipt', 'basic', 'إنشاء رقم وصل يدوي', async () => {
                    const customNumber = 'IQ-2024-999999';
                    try {
                        const receiptNumber = window.ReceiptSystem.generateReceiptNumber(true, customNumber);
                        if (receiptNumber !== customNumber) {
                            throw new Error('رقم الوصل المُنشأ لا يطابق المطلوب');
                        }
                        return `تم إنشاء رقم الوصل اليدوي: ${receiptNumber}`;
                    } catch (error) {
                        if (error.message.includes('already exists')) {
                            return 'تم اكتشاف التكرار بنجاح (رقم الوصل موجود مسبقاً)';
                        }
                        throw error;
                    }
                });

                this.addTest('validate-receipt-format', 'basic', 'التحقق من تنسيق رقم الوصل', async () => {
                    const validFormats = ['IQ-2024-000001', 'IRQ-2024-123456', 'BG-2024-999999'];
                    const invalidFormats = ['IQ-24-001', 'IQ2024000001', 'IQ-2024-12345'];

                    for (const format of validFormats) {
                        if (!window.ReceiptSystem.validateReceiptFormat(format)) {
                            throw new Error(`تنسيق صحيح تم رفضه: ${format}`);
                        }
                    }

                    for (const format of invalidFormats) {
                        if (window.ReceiptSystem.validateReceiptFormat(format)) {
                            throw new Error(`تنسيق خاطئ تم قبوله: ${format}`);
                        }
                    }

                    return 'تم التحقق من جميع التنسيقات بنجاح';
                });

                // Add more tests...
                this.addTest('generate-code128', 'basic', 'إنشاء باركود Code128', async () => {
                    const canvas = document.createElement('canvas');
                    const testData = 'IQ-2024-000001';

                    const success = window.AdvancedBarcodeSystem.generateBarcode(testData, 'code128', canvas);
                    if (!success) {
                        throw new Error('فشل في إنشاء باركود Code128');
                    }

                    return 'تم إنشاء باركود Code128 بنجاح';
                });

                this.addTest('export-functionality', 'advanced', 'وظائف التصدير', async () => {
                    const testData = [
                        { receipt_number: 'IQ-2024-000001', status: 'active' },
                        { receipt_number: 'IQ-2024-000002', status: 'used' }
                    ];

                    try {
                        window.AdvancedReceiptFeatures.exportToCSV(testData);
                        return 'تم اختبار تصدير CSV بنجاح';
                    } catch (error) {
                        throw new Error('فشل في تصدير CSV: ' + error.message);
                    }
                });
            }

            addTest(id, category, name, testFunction) {
                this.tests.set(id, {
                    id,
                    category,
                    name,
                    testFunction,
                    status: 'pending',
                    result: null,
                    error: null
                });
            }

            async runAllTests() {
                this.results.running = true;
                this.updateButtons(true);
                this.showResults();

                const testIds = Array.from(this.tests.keys());
                await this.runTests(testIds);

                this.results.running = false;
                this.updateButtons(false);
            }

            async runTestCategory(category) {
                this.results.running = true;
                this.updateButtons(true);
                this.showResults();

                const testIds = Array.from(this.tests.keys()).filter(id =>
                    this.tests.get(id).category === category
                );
                await this.runTests(testIds);

                this.results.running = false;
                this.updateButtons(false);
            }

            async runTests(testIds) {
                this.results.total = testIds.length;
                this.results.passed = 0;
                this.results.failed = 0;

                for (let i = 0; i < testIds.length; i++) {
                    const testId = testIds[i];
                    await this.runSingleTest(testId);

                    const progress = ((i + 1) / testIds.length) * 100;
                    this.updateProgress(progress);

                    // Small delay to show progress
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                this.updateSummary();
            }

            async runSingleTest(testId) {
                const test = this.tests.get(testId);
                if (!test) return;

                this.updateTestStatus(testId, 'running');
                this.log(`بدء اختبار: ${test.name}`);

                try {
                    const result = await test.testFunction();
                    test.status = 'success';
                    test.result = result;
                    test.error = null;
                    this.results.passed++;

                    this.updateTestStatus(testId, 'success');
                    this.log(`✅ نجح: ${test.name} - ${result}`);

                } catch (error) {
                    test.status = 'error';
                    test.result = null;
                    test.error = error.message;
                    this.results.failed++;

                    this.updateTestStatus(testId, 'error');
                    this.log(`❌ فشل: ${test.name} - ${error.message}`);
                }
            }

            updateTestStatus(testId, status) {
                const statusIcon = document.getElementById(`status-${testId}`);
                if (!statusIcon) return;

                statusIcon.className = `status-icon status-${status}`;

                switch (status) {
                    case 'pending':
                        statusIcon.innerHTML = '<i class="fas fa-clock"></i>';
                        break;
                    case 'running':
                        statusIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                        break;
                    case 'success':
                        statusIcon.innerHTML = '<i class="fas fa-check"></i>';
                        break;
                    case 'error':
                        statusIcon.innerHTML = '<i class="fas fa-times"></i>';
                        break;
                }
            }

            updateProgress(percentage) {
                const progressFill = document.getElementById('progress-fill');
                const progressText = document.getElementById('progress-text');

                progressFill.style.width = `${percentage}%`;
                progressText.textContent = `${Math.round(percentage)}% مكتمل`;
            }

            updateSummary() {
                document.getElementById('total-tests').textContent = this.results.total;
                document.getElementById('passed-tests').textContent = this.results.passed;
                document.getElementById('failed-tests').textContent = this.results.failed;

                const successRate = this.results.total > 0 ?
                    Math.round((this.results.passed / this.results.total) * 100) : 0;
                document.getElementById('success-rate').textContent = `${successRate}%`;
            }

            showResults() {
                document.getElementById('test-results').style.display = 'block';
            }

            updateButtons(disabled) {
                const buttons = document.querySelectorAll('.action-btn, .test-button');
                buttons.forEach(button => {
                    button.disabled = disabled;
                });
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString('ar-IQ');
                const logMessage = `[${timestamp}] ${message}`;
                this.logMessages.push(logMessage);

                const logElement = document.getElementById('test-log');
                if (logElement) {
                    logElement.textContent = this.logMessages.join('\n');
                    logElement.scrollTop = logElement.scrollHeight;
                }
            }

            resetTests() {
                this.tests.forEach(test => {
                    test.status = 'pending';
                    test.result = null;
                    test.error = null;
                });

                this.results = { total: 0, passed: 0, failed: 0, running: false };
                this.logMessages = [];

                this.updateUI();
                this.updateProgress(0);

                // Reset all test status icons
                this.tests.forEach((test, testId) => {
                    this.updateTestStatus(testId, 'pending');
                });

                document.getElementById('test-results').style.display = 'none';
                this.log('تم إعادة تعيين جميع الاختبارات');
            }

            updateUI() {
                this.updateSummary();
                this.updateButtons(this.results.running);
            }
        }

        // Global function for single test execution
        function runSingleTest(testId) {
            if (window.testSuite) {
                window.testSuite.runSingleTest(testId);
            }
        }

        // Initialize test suite when page loads
        document.addEventListener('DOMContentLoaded', function() {
            window.testSuite = new ReceiptSystemTestSuite();
        });
    </script>
</body>
</html>
