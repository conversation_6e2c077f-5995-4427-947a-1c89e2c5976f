# تثبيت Node.js أولاً
## Install Node.js First - Required Step

**🚨 مطلوب قبل بناء التطبيق 🚨**

---

## 🎯 **لماذا نحتاج Node.js؟**

**Node.js** هو بيئة تشغيل JavaScript مطلوبة لبناء تطبيق سطح المكتب. بدونه لا يمكن تحويل النظام إلى ملف `.exe`.

### **ما سيحدث بعد التثبيت:**
- ✅ إمكانية تشغيل أوامر `npm`
- ✅ تثبيت مكتبة Electron
- ✅ بناء ملف `.exe` للتطبيق
- ✅ إنشاء مثبت احترافي

---

## 📥 **خطوات تثبيت Node.js**

### **الخطوة 1: التحميل**
1. اذهب إلى: **https://nodejs.org/**
2. ستجد زرين:
   - **LTS** (مُوصى به) ← اختر هذا
   - **Current** (أحدث إصدار)
3. اضغط على **"Download for Windows"**
4. سيتم تحميل ملف بحجم ~30 MB

### **الخطوة 2: التثبيت**
1. شغل الملف المُحمل (مثل: `node-v18.17.0-x64.msi`)
2. اتبع معالج التثبيت:
   ```
   Welcome → Next
   License Agreement → I accept → Next  
   Destination Folder → Next (اتركه كما هو)
   Custom Setup → Next (اتركه كما هو)
   Tools for Native Modules → Next
   Ready to Install → Install
   ```
3. انتظر انتهاء التثبيت (2-3 دقائق)
4. اضغط **"Finish"**

### **الخطوة 3: التحقق من التثبيت**
1. افتح **Command Prompt** (cmd)
   - اضغط `Win + R`
   - اكتب `cmd`
   - اضغط Enter
2. اكتب الأوامر التالية:
   ```cmd
   node --version
   ```
   يجب أن تظهر: `v18.17.0` (أو رقم مشابه)
   
   ```cmd
   npm --version
   ```
   يجب أن تظهر: `9.6.7` (أو رقم مشابه)

3. إذا ظهرت الأرقام = ✅ **التثبيت ناجح!**
4. إذا ظهر خطأ = ❌ **أعد التثبيت**

---

## 🚀 **بعد تثبيت Node.js**

### **الآن يمكنك:**

#### **الطريقة السهلة:**
1. انقر نقراً مزدوجاً على: **`build-desktop-app.bat`**
2. اختر **"Run as Administrator"**
3. انتظر انتهاء العملية

#### **الطريقة اليدوية:**
```cmd
# افتح Command Prompt كمدير
# انتقل لمجلد المشروع
cd "C:\Users\<USER>\Downloads\حاسبة"

# تثبيت التبعيات
npm install

# بناء التطبيق
npm run build-win
```

---

## ⏱️ **الجدول الزمني المتوقع**

| المرحلة | الوقت المتوقع |
|---------|---------------|
| تحميل Node.js | 2-5 دقائق |
| تثبيت Node.js | 2-3 دقائق |
| تثبيت التبعيات | 3-5 دقائق |
| بناء التطبيق | 5-10 دقائق |
| **المجموع** | **12-23 دقيقة** |

---

## 🔧 **استكشاف الأخطاء**

### **❌ "تم رفض الوصول" أثناء التثبيت**
**الحل**: شغل المثبت كمدير (Right-click → Run as Administrator)

### **❌ "node is not recognized" بعد التثبيت**
**الحل**: 
1. أعد تشغيل Command Prompt
2. إذا لم يعمل، أعد تثبيت Node.js وتأكد من تحديد "Add to PATH"

### **❌ التثبيت بطيء جداً**
**الحل**: 
1. تأكد من اتصال إنترنت جيد
2. أغلق برامج الحماية مؤقتاً
3. جرب في وقت آخر (قد تكون الخوادم مزدحمة)

---

## 📋 **قائمة التحقق**

### **قبل المتابعة، تأكد من:**
- [ ] تم تحميل Node.js من الموقع الرسمي
- [ ] تم تثبيت Node.js بنجاح
- [ ] أمر `node --version` يعمل
- [ ] أمر `npm --version` يعمل
- [ ] لديك اتصال إنترنت جيد
- [ ] لديك 2 GB مساحة فارغة على القرص

### **إذا كانت جميع النقاط ✅ = جاهز للمتابعة!**

---

## 📞 **تحتاج مساعدة؟**

### **🆘 الدعم السريع:**
- **WhatsApp**: +964-XXX-XXXX
- **Email**: <EMAIL>
- **Telegram**: @IraqiDeliverySupport

### **📚 مراجع مفيدة:**
- **الموقع الرسمي لـ Node.js**: https://nodejs.org/
- **دليل التثبيت المفصل**: `DESKTOP_INSTALLATION_GUIDE.md`
- **التثبيت السريع**: `QUICK_DESKTOP_SETUP.md`

---

## 🎯 **الخطوة التالية**

**بعد تثبيت Node.js بنجاح:**
1. ارجع إلى ملف: **`QUICK_DESKTOP_SETUP.md`**
2. أو شغل ملف: **`build-desktop-app.bat`**
3. اتبع التعليمات لبناء التطبيق

---

**© 2024 نظام إدارة شركة التوصيل العراقية**

**🚀 Node.js هو الخطوة الأولى نحو تطبيق سطح مكتب احترافي! 🚀**
