/* Dashboard Layout and Component Fixes */

/* ===== DASHBOARD LAYOUT FIXES ===== */

/* 1. Fix Sidebar Layout */
.sidebar {
    width: 280px;
    min-height: 100vh;
    background: var(--bg-secondary);
    border-right: 1px solid #e1e5e9;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar.mobile {
    transform: translateX(-100%);
}

.sidebar.mobile.mobile-open {
    transform: translateX(0);
}

/* 2. Fix Sidebar Header */
.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--text-primary);
    font-weight: 700;
    font-size: var(--text-lg);
}

.sidebar-header .logo i {
    font-size: var(--text-2xl);
    color: var(--primary-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* 3. Fix Navigation Menu */
.nav-menu {
    padding: var(--space-4) 0;
}

.nav-item {
    margin-bottom: var(--space-1);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    margin-right: var(--space-4);
    position: relative;
}

.nav-link:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.nav-item.active .nav-link {
    background: var(--primary-gradient);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.nav-link i {
    font-size: var(--text-lg);
    width: 20px;
    text-align: center;
}

/* 4. Fix Main Content Area */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    background: var(--bg-primary);
    transition: var(--transition-normal);
}

.sidebar.collapsed + .main-content {
    margin-left: 80px;
}

/* 5. Fix Header */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid #e1e5e9;
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 50;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
}

.page-title h1 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--space-1);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* 6. Fix Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.search-btn,
.notifications-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    position: relative;
}

.search-btn:hover,
.notifications-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.notifications-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--danger-color);
    color: var(--text-white);
    font-size: var(--text-xs);
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
}

/* 7. Fix User Menu */
.user-menu {
    position: relative;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.user-menu-toggle:hover {
    background: var(--bg-primary);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-weight: 600;
}

.user-info {
    text-align: right;
}

.user-name {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.user-role {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin: 0;
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid #e1e5e9;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
    z-index: 1000;
}

.user-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    border-bottom: 1px solid #f1f3f4;
}

.user-menu-item:last-child {
    border-bottom: none;
}

.user-menu-item:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.user-menu-item.danger:hover {
    background: var(--danger-color);
    color: var(--text-white);
}

/* 8. Fix Page Content */
.page-content {
    padding: var(--space-6);
}

/* 9. Fix Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    cursor: pointer;
    border: 1px solid #f1f3f4;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card.clickable:hover {
    border-color: var(--primary-color);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
    font-size: var(--text-2xl);
    color: var(--text-white);
}

.stat-icon.orders {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.delivered {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.stat-icon.pending {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-icon.postponed {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.stat-icon.returns {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
}

.stat-content h3 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--space-1) 0;
}

.stat-content p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--space-2) 0;
}

.stat-change {
    font-size: var(--text-xs);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change.warning {
    color: var(--warning-color);
}

/* 10. Fix Tables */
.table-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid #f1f3f4;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-header h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: var(--space-4);
    text-align: right;
    border-bottom: 1px solid #f1f3f4;
}

.data-table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.data-table td {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.data-table tr:hover {
    background: var(--bg-primary);
}

/* 11. Fix Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn.primary {
    background: var(--primary-gradient);
    color: var(--text-white);
}

.btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid #e1e5e9;
}

.btn.success {
    background: var(--success-color);
    color: var(--text-white);
}

.btn.warning {
    background: var(--warning-color);
    color: var(--text-white);
}

.btn.danger {
    background: var(--danger-color);
    color: var(--text-white);
}

.btn.sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn.lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
}

/* 12. Responsive Design for Dashboard */
@media (max-width: 1024px) {
    .sidebar {
        width: 260px;
    }
    
    .main-content {
        margin-left: 260px;
    }
    
    .sidebar.collapsed + .main-content {
        margin-left: 70px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--space-4);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .header {
        padding: var(--space-3) var(--space-4);
    }
    
    .page-content {
        padding: var(--space-4);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }
    
    .stat-card {
        padding: var(--space-4);
    }
    
    .user-info {
        display: none;
    }
}

@media (max-width: 480px) {
    .header {
        padding: var(--space-2) var(--space-3);
    }
    
    .page-content {
        padding: var(--space-3);
    }
    
    .table-container {
        margin: 0 -var(--space-3);
        border-radius: 0;
    }
    
    .data-table th,
    .data-table td {
        padding: var(--space-2);
        font-size: var(--text-xs);
    }
}
