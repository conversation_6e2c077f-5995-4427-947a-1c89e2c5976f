# إنشاء أيقونات التطبيق
## Application Icons Creation Guide

**الملفات المطلوبة:**
- `icon.ico` (Windows - 256x256)
- `icon.icns` (macOS - 512x512)  
- `icon.png` (Linux - 512x512)

---

## 🎨 **تصميم الأيقونة المقترح**

### **العناصر:**
- 🚚 **شاحنة توصيل** (العنصر الرئيسي)
- 🇮🇶 **ألوان العلم العراقي** (أحمر، أبيض، أسود)
- 📦 **صندوق** (يرمز للطلبات)
- ⚡ **خطوط سرعة** (يرمز للسرعة)

### **الألوان المقترحة:**
- **الأساسي**: #2E7D32 (أخضر)
- **الثانوي**: #1976D2 (أزرق)
- **التمييز**: #F57C00 (برتقالي)

---

## 🔧 **إنشاء الأيقونات**

### **الطريقة 1: استخدام أدوات مجانية**
1. **Canva**: https://canva.com
2. **GIMP**: https://gimp.org
3. **Paint.NET**: https://getpaint.net

### **الطريقة 2: مولدات الأيقونات**
1. **Favicon.io**: https://favicon.io
2. **IconGenerator**: https://icongenerator.net
3. **AppIcon**: https://appicon.co

---

## 📐 **المقاسات المطلوبة**

### **Windows (.ico):**
- 16x16, 32x32, 48x48, 64x64, 128x128, 256x256

### **macOS (.icns):**
- 16x16, 32x32, 128x128, 256x256, 512x512

### **Linux (.png):**
- 512x512 (PNG عالي الجودة)

---

## 🚀 **أيقونة مؤقتة**

حتى يتم إنشاء الأيقونة المخصصة، يمكن استخدام:
- **أيقونة النظام الافتراضية** من Electron
- **أيقونة شاحنة بسيطة** من مكتبة الأيقونات المجانية

---

**ملاحظة**: الأيقونات ليست ضرورية لعمل التطبيق، لكنها تحسن المظهر الاحترافي.
