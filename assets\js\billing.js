// Billing System
class BillingManager {
    constructor() {
        this.invoices = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            search: '',
            status: '',
            dateFrom: '',
            dateTo: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Search and filter functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'invoices-search') {
                this.filters.search = e.target.value;
                this.filterInvoices();
            }
            if (e.target.id === 'date-from-filter') {
                this.filters.dateFrom = e.target.value;
                this.filterInvoices();
            }
            if (e.target.id === 'date-to-filter') {
                this.filters.dateTo = e.target.value;
                this.filterInvoices();
            }
        });

        document.addEventListener('change', (e) => {
            if (e.target.id === 'status-filter') {
                this.filters.status = e.target.value;
                this.filterInvoices();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.generate-invoice-btn') || e.target.closest('.generate-invoice-btn')) {
                e.preventDefault();
                this.showGenerateInvoiceModal();
            }
            if (e.target.matches('.view-invoice-btn') || e.target.closest('.view-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.view-invoice-btn').getAttribute('data-invoice-id');
                this.showInvoiceDetails(invoiceId);
            }
            if (e.target.matches('.print-invoice-btn') || e.target.closest('.print-invoice-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.print-invoice-btn').getAttribute('data-invoice-id');
                this.printInvoice(invoiceId);
            }
            if (e.target.matches('.mark-paid-btn') || e.target.closest('.mark-paid-btn')) {
                e.preventDefault();
                const invoiceId = e.target.closest('.mark-paid-btn').getAttribute('data-invoice-id');
                this.markAsPaid(invoiceId);
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const billingHTML = `
            <div class="billing-header">
                <div class="billing-title">
                    <h1>نظام الفواتير</h1>
                    <p>إدارة فواتير العملاء والمدفوعات</p>
                </div>
                <div class="billing-actions">
                    <button class="neu-btn primary generate-invoice-btn">
                        <i class="fas fa-file-invoice"></i>
                        إنشاء فاتورة
                    </button>
                    <button class="neu-btn secondary export-btn">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="billing-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="invoices-search" class="neu-input" placeholder="رقم الفاتورة أو اسم العميل...">
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending">معلقة</option>
                            <option value="paid">مدفوعة</option>
                            <option value="overdue">متأخرة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" id="date-from-filter" class="neu-input">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" id="date-to-filter" class="neu-input">
                    </div>
                </div>
            </div>

            <div class="billing-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-invoices">0</h3>
                        <p>إجمالي الفواتير</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="paid-invoices">0</h3>
                        <p>فواتير مدفوعة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="pending-invoices">0</h3>
                        <p>فواتير معلقة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="overdue-invoices">0</h3>
                        <p>فواتير متأخرة</p>
                    </div>
                </div>
            </div>

            <div class="invoices-table-container">
                <div class="table-header">
                    <h3>قائمة الفواتير</h3>
                    <div class="table-actions">
                        <button class="neu-btn small refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table class="invoices-table" id="invoices-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>عدد الطلبات</th>
                                <th>المبلغ الإجمالي</th>
                                <th>العمولة</th>
                                <th>صافي المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoices-tbody">
                            <!-- Invoices will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination" id="invoices-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = billingHTML;
        this.renderInvoices();
        this.updateStats();
    }

    loadSampleData() {
        this.invoices = [
            {
                id: 'INV-2024-001',
                customerId: 3,
                customerName: 'مطعم بغداد الأصيل',
                customerType: 'company',
                issueDate: '2024-12-01',
                dueDate: '2024-12-31',
                ordersCount: 15,
                totalAmount: 750000,
                commissionAmount: 60000,
                netAmount: 690000,
                status: 'paid',
                paidDate: '2024-12-25',
                orders: [
                    { id: 'ORD-2024-001', amount: 50000, commission: 4000 },
                    { id: 'ORD-2024-002', amount: 50000, commission: 4000 }
                ]
            },
            {
                id: 'INV-2024-002',
                customerId: 4,
                customerName: 'شركة التوصيل السريع',
                customerType: 'company',
                issueDate: '2024-12-15',
                dueDate: '2025-01-15',
                ordersCount: 8,
                totalAmount: 400000,
                commissionAmount: 28000,
                netAmount: 372000,
                status: 'pending',
                paidDate: null,
                orders: [
                    { id: 'ORD-2024-003', amount: 50000, commission: 3500 },
                    { id: 'ORD-2024-004', amount: 50000, commission: 3500 }
                ]
            },
            {
                id: 'INV-2024-003',
                customerId: 5,
                customerName: 'صيدلية دجلة',
                customerType: 'company',
                issueDate: '2024-11-01',
                dueDate: '2024-12-01',
                ordersCount: 12,
                totalAmount: 600000,
                commissionAmount: 54000,
                netAmount: 546000,
                status: 'overdue',
                paidDate: null,
                orders: [
                    { id: 'ORD-2024-005', amount: 50000, commission: 4500 },
                    { id: 'ORD-2024-006', amount: 50000, commission: 4500 }
                ]
            }
        ];
    }

    renderInvoices() {
        const tbody = document.getElementById('invoices-tbody');
        if (!tbody) return;

        const filteredInvoices = this.getFilteredInvoices();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const invoicesToShow = filteredInvoices.slice(startIndex, endIndex);

        tbody.innerHTML = invoicesToShow.map(invoice => `
            <tr>
                <td>
                    <div class="invoice-id">
                        <strong>${invoice.id}</strong>
                    </div>
                </td>
                <td>
                    <div class="customer-info">
                        <strong>${invoice.customerName}</strong>
                        <small>${invoice.customerType === 'company' ? 'شركة' : 'فردي'}</small>
                    </div>
                </td>
                <td>${this.formatDate(invoice.issueDate)}</td>
                <td>${this.formatDate(invoice.dueDate)}</td>
                <td>
                    <span class="orders-count">${invoice.ordersCount}</span>
                </td>
                <td>
                    <span class="total-amount">${this.formatCurrency(invoice.totalAmount)}</span>
                </td>
                <td>
                    <span class="commission-amount">${this.formatCurrency(invoice.commissionAmount)}</span>
                </td>
                <td>
                    <span class="net-amount">${this.formatCurrency(invoice.netAmount)}</span>
                </td>
                <td>
                    <span class="status-badge ${invoice.status}">
                        ${this.getStatusText(invoice.status)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-invoice-btn" data-invoice-id="${invoice.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn print-invoice-btn" data-invoice-id="${invoice.id}" title="طباعة">
                            <i class="fas fa-print"></i>
                        </button>
                        ${invoice.status === 'pending' || invoice.status === 'overdue' ? `
                            <button class="action-btn mark-paid-btn" data-invoice-id="${invoice.id}" title="تسديد">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination(filteredInvoices.length);
    }

    getFilteredInvoices() {
        return this.invoices.filter(invoice => {
            const matchesSearch = !this.filters.search || 
                invoice.id.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                invoice.customerName.toLowerCase().includes(this.filters.search.toLowerCase());
            
            const matchesStatus = !this.filters.status || invoice.status === this.filters.status;
            
            const matchesDateFrom = !this.filters.dateFrom || 
                new Date(invoice.issueDate) >= new Date(this.filters.dateFrom);
            const matchesDateTo = !this.filters.dateTo || 
                new Date(invoice.issueDate) <= new Date(this.filters.dateTo);

            return matchesSearch && matchesStatus && matchesDateFrom && matchesDateTo;
        });
    }

    renderPagination(totalItems) {
        const pagination = document.getElementById('invoices-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    updateStats() {
        const totalInvoices = document.getElementById('total-invoices');
        const paidInvoices = document.getElementById('paid-invoices');
        const pendingInvoices = document.getElementById('pending-invoices');
        const overdueInvoices = document.getElementById('overdue-invoices');

        if (totalInvoices) totalInvoices.textContent = this.invoices.length;
        if (paidInvoices) paidInvoices.textContent = this.invoices.filter(i => i.status === 'paid').length;
        if (pendingInvoices) pendingInvoices.textContent = this.invoices.filter(i => i.status === 'pending').length;
        if (overdueInvoices) overdueInvoices.textContent = this.invoices.filter(i => i.status === 'overdue').length;
    }

    filterInvoices() {
        this.currentPage = 1;
        this.renderInvoices();
    }

    getStatusText(status) {
        const statuses = {
            'pending': 'معلقة',
            'paid': 'مدفوعة',
            'overdue': 'متأخرة'
        };
        return statuses[status] || status;
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    showGenerateInvoiceModal() {
        if (window.ModalManager) {
            window.ModalManager.showGenerateInvoiceModal();
        } else {
            alert('سيتم إضافة نافذة إنشاء فاتورة قريباً');
        }
    }

    showInvoiceDetails(invoiceId) {
        if (window.ModalManager) {
            window.ModalManager.showInvoiceDetailsModal(invoiceId);
        } else {
            alert(`سيتم إضافة نافذة تفاصيل الفاتورة ${invoiceId} قريباً`);
        }
    }

    printInvoice(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (!invoice) return;

        const printWindow = window.open('', '_blank');
        const printContent = this.generateInvoicePrintContent(invoice);
        
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }

    generateInvoicePrintContent(invoice) {
        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.id}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
                    .invoice-header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 20px; margin-bottom: 20px; }
                    .invoice-details { margin-bottom: 20px; }
                    .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .invoice-table th, .invoice-table td { border: 1px solid #000; padding: 8px; text-align: right; }
                    .invoice-table th { background-color: #f0f0f0; }
                    .invoice-footer { text-align: center; margin-top: 30px; }
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>شركة التوصيل العراقية</h1>
                    <h2>فاتورة رقم: ${invoice.id}</h2>
                </div>
                <div class="invoice-details">
                    <p><strong>العميل:</strong> ${invoice.customerName}</p>
                    <p><strong>تاريخ الإصدار:</strong> ${this.formatDate(invoice.issueDate)}</p>
                    <p><strong>تاريخ الاستحقاق:</strong> ${this.formatDate(invoice.dueDate)}</p>
                </div>
                <table class="invoice-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>إجمالي رسوم التوصيل (${invoice.ordersCount} طلب)</td>
                            <td>${this.formatCurrency(invoice.totalAmount)}</td>
                        </tr>
                        <tr>
                            <td>خصم عمولة العميل</td>
                            <td>-${this.formatCurrency(invoice.commissionAmount)}</td>
                        </tr>
                        <tr style="font-weight: bold;">
                            <td>صافي المبلغ المستحق</td>
                            <td>${this.formatCurrency(invoice.netAmount)}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="invoice-footer">
                    <p>شكراً لتعاملكم معنا</p>
                </div>
            </body>
            </html>
        `;
    }

    markAsPaid(invoiceId) {
        const invoice = this.invoices.find(i => i.id === invoiceId);
        if (invoice) {
            invoice.status = 'paid';
            invoice.paidDate = new Date().toISOString().split('T')[0];
            this.renderInvoices();
            this.updateStats();
            alert('تم تسديد الفاتورة بنجاح');
        }
    }
}

// Initialize Billing Manager
window.BillingManager = new BillingManager();
