const { app, BrowserWindow, Menu, ipcMain, dialog, shell, nativeTheme, Notification } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const Store = require('electron-store');

// إعداد التخزين المحلي
const store = new Store();

// متغيرات عامة
let mainWindow;
let splashWindow;
let backupInterval;
const isDev = process.env.NODE_ENV === 'development';

// إعدادات التطبيق
const APP_CONFIG = {
    name: 'نظام إدارة شركة التوصيل العراقية',
    version: '2.0.0',
    author: 'Iraqi Delivery Management System',
    description: 'نظام متكامل لإدارة شركات التوصيل في العراق',
    website: 'https://iraqi-delivery.com',
    supportEmail: '<EMAIL>'
};

// إعداد التطبيق
app.setName('نظام إدارة شركة التوصيل العراقية');

// منع تشغيل عدة نسخ من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // إذا حاول المستخدم تشغيل نسخة ثانية، ركز على النافذة الموجودة
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

// إنشاء نافذة البداية
function createSplashWindow() {
    splashWindow = new BrowserWindow({
        width: 450,
        height: 350,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        resizable: false,
        center: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        }
    });

    splashWindow.loadFile('splash.html');

    splashWindow.on('closed', () => {
        splashWindow = null;
    });

    // إخفاء نافذة البداية تلقائياً بعد 3 ثواني
    setTimeout(() => {
        if (splashWindow) {
            splashWindow.close();
        }
        createMainWindow();
    }, 3000);
}

// إنشاء النافذة الرئيسية
function createMainWindow() {
    // الحصول على إعدادات النافذة المحفوظة
    const windowBounds = store.get('windowBounds', {
        width: 1400,
        height: 900,
        x: undefined,
        y: undefined
    });

    mainWindow = new BrowserWindow({
        width: windowBounds.width,
        height: windowBounds.height,
        x: windowBounds.x,
        y: windowBounds.y,
        minWidth: 1200,
        minHeight: 800,
        show: false, // لا تظهر النافذة حتى تكتمل
        icon: getAppIcon(),
        title: APP_CONFIG.name,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: true,
            allowRunningInsecureContent: false,
            experimentalFeatures: false,
            spellcheck: false
        },
        titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
        autoHideMenuBar: false, // إظهار شريط القوائم
        backgroundColor: '#f5f7fa',
        center: true,
        maximizable: true,
        fullscreenable: true
    });

    // تحميل الصفحة الرئيسية
    mainWindow.loadFile('index.html');

    // إظهار النافذة عند اكتمال التحميل
    mainWindow.once('ready-to-show', () => {
        if (splashWindow) {
            splashWindow.close();
        }
        mainWindow.show();

        // التركيز على النافذة
        if (process.platform === 'darwin') {
            app.dock.show();
        }

        // إظهار إشعار الترحيب
        showNotification('مرحباً بك', 'تم تشغيل نظام إدارة شركة التوصيل العراقية بنجاح');

        // تهيئة النسخ الاحتياطية التلقائية
        initializeAutoBackup();

        // فتح أدوات المطور في وضع التطوير
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // حفظ موضع وحجم النافذة عند الإغلاق
    mainWindow.on('close', () => {
        const bounds = mainWindow.getBounds();
        store.set('windowBounds', bounds);
    });

    mainWindow.on('closed', () => {
        mainWindow = null;
        // إيقاف النسخ الاحتياطية التلقائية
        if (backupInterval) {
            clearInterval(backupInterval);
        }
    });

    // معالج تصغير النافذة
    mainWindow.on('minimize', () => {
        if (process.platform === 'darwin') {
            app.dock.hide();
        }
    });

    // معالج استعادة النافذة
    mainWindow.on('restore', () => {
        if (process.platform === 'darwin') {
            app.dock.show();
        }
    });

    // منع التنقل إلى مواقع خارجية
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);

        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });

    // منع فتح نوافذ جديدة
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // إعداد القائمة
    createMenu();
}

// إنشاء قائمة التطبيق
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'جديد',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new');
                    }
                },
                {
                    label: 'فتح',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] },
                                { name: 'All Files', extensions: ['*'] }
                            ]
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-open', result.filePaths[0]);
                        }
                    }
                },
                {
                    label: 'حفظ',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'طباعة',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => {
                        mainWindow.webContents.print();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'تحرير',
            submenu: [
                { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                { type: 'separator' },
                { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' },
                { label: 'تحديد الكل', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                { type: 'separator' },
                { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                { type: 'separator' },
                { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
            ]
        },
        {
            label: 'نافذة',
            submenu: [
                { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: 'نظام إدارة شركة التوصيل العراقية',
                            detail: `الإصدار: ${app.getVersion()}\nمطور بواسطة: فريق التطوير العراقي\nحقوق الطبع محفوظة © 2024`,
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'دليل المستخدم',
                    click: () => {
                        shell.openExternal('https://iraqi-delivery.com/help');
                    }
                },
                {
                    label: 'الدعم التقني',
                    click: () => {
                        shell.openExternal('mailto:<EMAIL>');
                    }
                }
            ]
        }
    ];

    // تعديل القائمة لنظام macOS
    if (process.platform === 'darwin') {
        template.unshift({
            label: app.getName(),
            submenu: [
                { label: 'حول ' + app.getName(), role: 'about' },
                { type: 'separator' },
                { label: 'الخدمات', role: 'services', submenu: [] },
                { type: 'separator' },
                { label: 'إخفاء ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
                { label: 'إخفاء الآخرين', accelerator: 'Command+Shift+H', role: 'hideothers' },
                { label: 'إظهار الكل', role: 'unhide' },
                { type: 'separator' },
                { label: 'خروج', accelerator: 'Command+Q', click: () => app.quit() }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// معالجات الأحداث
app.whenReady().then(() => {
    createSplashWindow();
    
    setTimeout(() => {
        createMainWindow();
        createMenu();
    }, 2000); // إظهار شاشة البداية لمدة ثانيتين

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// معالجات IPC (التواصل بين العمليات)
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
    const result = await dialog.showSaveDialog(mainWindow, {
        filters: [
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'CSV Files', extensions: ['csv'] },
            { name: 'All Files', extensions: ['*'] }
        ]
    });
    return result;
});

ipcMain.handle('show-open-dialog', async () => {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
            { name: 'JSON Files', extensions: ['json'] },
            { name: 'CSV Files', extensions: ['csv'] },
            { name: 'All Files', extensions: ['*'] }
        ]
    });
    return result;
});

ipcMain.handle('write-file', async (event, filePath, data) => {
    try {
        fs.writeFileSync(filePath, data, 'utf8');
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        return { success: true, data };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// معالج لإظهار رسائل النظام
ipcMain.handle('show-message-box', async (event, options) => {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
});

// معالج لفتح الروابط الخارجية
ipcMain.handle('open-external', async (event, url) => {
    shell.openExternal(url);
});

// ===== الوظائف المساعدة الجديدة =====

// الحصول على أيقونة التطبيق
function getAppIcon() {
    const iconPath = path.join(__dirname, 'assets/icons');

    if (process.platform === 'win32') {
        return path.join(iconPath, 'icon.ico');
    } else if (process.platform === 'darwin') {
        return path.join(iconPath, 'icon.icns');
    } else {
        return path.join(iconPath, 'icon.png');
    }
}

// إظهار إشعار النظام
function showNotification(title, body, options = {}) {
    if (Notification.isSupported()) {
        const notification = new Notification({
            title: title,
            body: body,
            icon: getAppIcon(),
            silent: false,
            ...options
        });

        notification.show();

        notification.on('click', () => {
            if (mainWindow) {
                if (mainWindow.isMinimized()) {
                    mainWindow.restore();
                }
                mainWindow.focus();
            }
        });

        return notification;
    }
}

// تهيئة النسخ الاحتياطية التلقائية
function initializeAutoBackup() {
    // إنشاء نسخة احتياطية كل 30 دقيقة
    backupInterval = setInterval(() => {
        createAutoBackup();
    }, 30 * 60 * 1000);

    // إنشاء نسخة احتياطية فورية عند بدء التشغيل
    setTimeout(() => {
        createAutoBackup();
    }, 5000);
}

// إنشاء نسخة احتياطية تلقائية
function createAutoBackup() {
    try {
        const backupDir = path.join(os.homedir(), 'Documents', 'Iraqi Delivery Backups');

        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupDir, `backup-${timestamp}.json`);

        // جمع البيانات من التخزين المحلي
        const backupData = {
            timestamp: new Date().toISOString(),
            version: APP_CONFIG.version,
            data: store.store
        };

        fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));

        console.log(`تم إنشاء نسخة احتياطية: ${backupFile}`);

        // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
        cleanupOldBackups(backupDir);

    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
    }
}

// تنظيف النسخ الاحتياطية القديمة
function cleanupOldBackups(backupDir) {
    try {
        const files = fs.readdirSync(backupDir)
            .filter(file => file.startsWith('backup-') && file.endsWith('.json'))
            .map(file => ({
                name: file,
                path: path.join(backupDir, file),
                time: fs.statSync(path.join(backupDir, file)).mtime
            }))
            .sort((a, b) => b.time - a.time);

        // حذف الملفات الزائدة عن 10
        if (files.length > 10) {
            files.slice(10).forEach(file => {
                fs.unlinkSync(file.path);
                console.log(`تم حذف النسخة الاحتياطية القديمة: ${file.name}`);
            });
        }
    } catch (error) {
        console.error('خطأ في تنظيف النسخ الاحتياطية:', error);
    }
}

// الحصول على معلومات النظام
function getSystemInfo() {
    return {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        electronVersion: process.versions.electron,
        chromeVersion: process.versions.chrome,
        osType: os.type(),
        osRelease: os.release(),
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpus: os.cpus().length,
        uptime: os.uptime()
    };
}

// ===== معالجات IPC إضافية =====

// معالج لإظهار الإشعارات
ipcMain.handle('show-notification', async (event, title, body, options) => {
    return showNotification(title, body, options);
});

// معالج لإنشاء نسخة احتياطية يدوية
ipcMain.handle('create-backup', async (event) => {
    try {
        createAutoBackup();
        return { success: true, message: 'تم إنشاء النسخة الاحتياطية بنجاح' };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// معالج للحصول على معلومات النظام
ipcMain.handle('get-system-info', async (event) => {
    return getSystemInfo();
});

// معالج للحصول على معلومات التطبيق
ipcMain.handle('get-app-info', async (event) => {
    return APP_CONFIG;
});

// معالج لإعادة تشغيل التطبيق
ipcMain.handle('restart-app', async (event) => {
    app.relaunch();
    app.exit();
});

// معالج لتصغير النافذة إلى شريط المهام
ipcMain.handle('minimize-to-tray', async (event) => {
    if (mainWindow) {
        mainWindow.minimize();
    }
});

// معالج لتبديل وضع الشاشة الكاملة
ipcMain.handle('toggle-fullscreen', async (event) => {
    if (mainWindow) {
        mainWindow.setFullScreen(!mainWindow.isFullScreen());
        return mainWindow.isFullScreen();
    }
    return false;
});

// معالج لطباعة الصفحة
ipcMain.handle('print-page', async (event, options = {}) => {
    if (mainWindow) {
        mainWindow.webContents.print(options);
    }
});

// معالج لحفظ ملف PDF
ipcMain.handle('save-pdf', async (event, options = {}) => {
    if (mainWindow) {
        try {
            const result = await dialog.showSaveDialog(mainWindow, {
                defaultPath: 'تقرير.pdf',
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (!result.canceled) {
                const data = await mainWindow.webContents.printToPDF(options);
                fs.writeFileSync(result.filePath, data);
                return { success: true, filePath: result.filePath };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    return { success: false, error: 'النافذة غير متاحة' };
});

// معالج للحصول على مسار التطبيق
ipcMain.handle('get-app-path', () => {
    return app.getAppPath();
});

// معالج للحصول على مسار البيانات
ipcMain.handle('get-user-data-path', () => {
    return app.getPath('userData');
});

// تعيين بروتوكول مخصص للتطبيق
if (process.defaultApp) {
    if (process.argv.length >= 2) {
        app.setAsDefaultProtocolClient('iraqi-delivery', process.execPath, [path.resolve(process.argv[1])]);
    }
} else {
    app.setAsDefaultProtocolClient('iraqi-delivery');
}
