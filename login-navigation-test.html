<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول والتنقل - نظام إدارة شركة التوصيل العراقية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-test {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .feature-test h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-working { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-pending { background: #f39c12; }
        .credentials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .credentials-table th,
        .credentials-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .credentials-table th {
            background: #667eea;
            color: white;
        }
        .credentials-table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-shield-alt"></i> اختبار تسجيل الدخول والتنقل</h1>
            <p>اختبار شامل لواجهة تسجيل الدخول وروابط التنقل</p>
            <button class="test-button" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
            <button class="test-button" onclick="openMainSystem()">
                <i class="fas fa-external-link-alt"></i> فتح النظام الرئيسي
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-users"></i> بيانات تسجيل الدخول المتاحة</h3>
            <table class="credentials-table">
                <thead>
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>كلمة المرور</th>
                        <th>نوع المستخدم</th>
                        <th>الوصف</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>admin</td>
                        <td>admin123</td>
                        <td>admin</td>
                        <td>مدير النظام</td>
                    </tr>
                    <tr>
                        <td>مدير</td>
                        <td>123456</td>
                        <td>admin</td>
                        <td>المدير العام</td>
                    </tr>
                    <tr>
                        <td>employee</td>
                        <td>employee123</td>
                        <td>employee</td>
                        <td>موظف</td>
                    </tr>
                    <tr>
                        <td>موظف</td>
                        <td>123456</td>
                        <td>employee</td>
                        <td>موظف النظام</td>
                    </tr>
                    <tr>
                        <td>driver</td>
                        <td>driver123</td>
                        <td>driver</td>
                        <td>مندوب</td>
                    </tr>
                    <tr>
                        <td>مندوب</td>
                        <td>123456</td>
                        <td>driver</td>
                        <td>مندوب التوصيل</td>
                    </tr>
                    <tr>
                        <td>company</td>
                        <td>company123</td>
                        <td>company</td>
                        <td>شركة</td>
                    </tr>
                    <tr>
                        <td>شركة</td>
                        <td>123456</td>
                        <td>company</td>
                        <td>شركة التوصيل</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>واجهة تسجيل الدخول <span class="status-indicator status-pending" id="login-interface"></span></h4>
                    <button class="test-button" onclick="testLoginInterface()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>التحقق من البيانات <span class="status-indicator status-pending" id="login-validation"></span></h4>
                    <button class="test-button" onclick="testLoginValidation()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>حفظ بيانات المستخدم <span class="status-indicator status-pending" id="user-storage"></span></h4>
                    <button class="test-button" onclick="testUserStorage()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>تسجيل الخروج <span class="status-indicator status-pending" id="logout-function"></span></h4>
                    <button class="test-button" onclick="testLogout()">اختبار</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-bars"></i> اختبار روابط التنقل</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>لوحة التحكم <span class="status-indicator status-pending" id="nav-dashboard"></span></h4>
                    <button class="test-button" onclick="testNavigation('dashboard')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إدارة الطلبات <span class="status-indicator status-pending" id="nav-orders"></span></h4>
                    <button class="test-button" onclick="testNavigation('orders')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إدارة المندوبين <span class="status-indicator status-pending" id="nav-drivers"></span></h4>
                    <button class="test-button" onclick="testNavigation('drivers')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إدارة العملاء <span class="status-indicator status-pending" id="nav-customers"></span></h4>
                    <button class="test-button" onclick="testNavigation('customers')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>الإدارة المالية <span class="status-indicator status-pending" id="nav-finance"></span></h4>
                    <button class="test-button" onclick="testNavigation('finance')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>تتبع الطلبات <span class="status-indicator status-pending" id="nav-tracking"></span></h4>
                    <button class="test-button" onclick="testNavigation('tracking')">اختبار</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-cogs"></i> اختبار الوظائف المتقدمة</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>نظام الإشعارات <span class="status-indicator status-pending" id="notifications-system"></span></h4>
                    <button class="test-button" onclick="testNotifications()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>البحث السريع <span class="status-indicator status-pending" id="search-function"></span></h4>
                    <button class="test-button" onclick="testSearch()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>قائمة المستخدم <span class="status-indicator status-pending" id="user-menu"></span></h4>
                    <button class="test-button" onclick="testUserMenu()">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>التصميم المتجاوب <span class="status-indicator status-pending" id="responsive-design"></span></h4>
                    <button class="test-button" onclick="testResponsive()">اختبار</button>
                </div>
            </div>
        </div>

        <div id="test-results"></div>
    </div>

    <script>
        let testResults = [];

        function runAllTests() {
            testResults = [];
            
            // اختبار تسجيل الدخول
            testLoginInterface();
            testLoginValidation();
            testUserStorage();
            testLogout();
            
            // اختبار التنقل
            testNavigation('dashboard');
            testNavigation('orders');
            testNavigation('drivers');
            testNavigation('customers');
            testNavigation('finance');
            testNavigation('tracking');
            
            // اختبار الوظائف المتقدمة
            testNotifications();
            testSearch();
            testUserMenu();
            testResponsive();
            
            // عرض النتائج
            setTimeout(showResults, 2000);
        }

        function testLoginInterface() {
            try {
                // فحص وجود عناصر واجهة تسجيل الدخول
                const loginPage = document.querySelector('#login-page');
                const loginForm = document.querySelector('#login-form');
                const usernameField = document.querySelector('#username');
                const passwordField = document.querySelector('#password');
                const userTypeField = document.querySelector('#user-type');
                
                if (loginPage && loginForm && usernameField && passwordField && userTypeField) {
                    updateStatus('login-interface', 'working');
                    addTestResult('واجهة تسجيل الدخول', true, 'جميع العناصر موجودة');
                } else {
                    updateStatus('login-interface', 'error');
                    addTestResult('واجهة تسجيل الدخول', false, 'بعض العناصر مفقودة');
                }
            } catch (error) {
                updateStatus('login-interface', 'error');
                addTestResult('واجهة تسجيل الدخول', false, error.message);
            }
        }

        function testLoginValidation() {
            try {
                // فحص وجود نظام التحقق
                if (typeof DeliveryManagementSystem !== 'undefined') {
                    const app = new DeliveryManagementSystem();
                    const isValid = app.validateCredentials('admin', 'admin123', 'admin');
                    
                    if (isValid) {
                        updateStatus('login-validation', 'working');
                        addTestResult('التحقق من البيانات', true, 'نظام التحقق يعمل بشكل صحيح');
                    } else {
                        updateStatus('login-validation', 'error');
                        addTestResult('التحقق من البيانات', false, 'نظام التحقق لا يعمل');
                    }
                } else {
                    updateStatus('login-validation', 'error');
                    addTestResult('التحقق من البيانات', false, 'النظام غير متاح');
                }
            } catch (error) {
                updateStatus('login-validation', 'error');
                addTestResult('التحقق من البيانات', false, error.message);
            }
        }

        function testUserStorage() {
            try {
                // فحص localStorage
                if (typeof Storage !== "undefined") {
                    localStorage.setItem('test', 'value');
                    const testValue = localStorage.getItem('test');
                    localStorage.removeItem('test');
                    
                    if (testValue === 'value') {
                        updateStatus('user-storage', 'working');
                        addTestResult('حفظ بيانات المستخدم', true, 'localStorage يعمل بشكل صحيح');
                    } else {
                        updateStatus('user-storage', 'error');
                        addTestResult('حفظ بيانات المستخدم', false, 'localStorage لا يعمل');
                    }
                } else {
                    updateStatus('user-storage', 'error');
                    addTestResult('حفظ بيانات المستخدم', false, 'localStorage غير مدعوم');
                }
            } catch (error) {
                updateStatus('user-storage', 'error');
                addTestResult('حفظ بيانات المستخدم', false, error.message);
            }
        }

        function testLogout() {
            try {
                // فحص وجود أزرار تسجيل الخروج
                const logoutBtn = document.querySelector('#logout-btn');
                const logoutItem = document.querySelector('.logout-item');
                
                if (logoutBtn || logoutItem) {
                    updateStatus('logout-function', 'working');
                    addTestResult('تسجيل الخروج', true, 'أزرار تسجيل الخروج موجودة');
                } else {
                    updateStatus('logout-function', 'error');
                    addTestResult('تسجيل الخروج', false, 'أزرار تسجيل الخروج مفقودة');
                }
            } catch (error) {
                updateStatus('logout-function', 'error');
                addTestResult('تسجيل الخروج', false, error.message);
            }
        }

        function testNavigation(page) {
            try {
                // فحص وجود رابط التنقل
                const navLink = document.querySelector(`[data-page="${page}"]`);
                
                if (navLink) {
                    updateStatus(`nav-${page}`, 'working');
                    addTestResult(`تنقل ${page}`, true, 'الرابط موجود ومُعرف بشكل صحيح');
                } else {
                    updateStatus(`nav-${page}`, 'error');
                    addTestResult(`تنقل ${page}`, false, 'الرابط مفقود أو غير مُعرف');
                }
            } catch (error) {
                updateStatus(`nav-${page}`, 'error');
                addTestResult(`تنقل ${page}`, false, error.message);
            }
        }

        function testNotifications() {
            try {
                // فحص وجود نظام الإشعارات
                if (typeof DeliveryManagementSystem !== 'undefined') {
                    updateStatus('notifications-system', 'working');
                    addTestResult('نظام الإشعارات', true, 'النظام متاح ويمكن استخدامه');
                } else {
                    updateStatus('notifications-system', 'error');
                    addTestResult('نظام الإشعارات', false, 'النظام غير متاح');
                }
            } catch (error) {
                updateStatus('notifications-system', 'error');
                addTestResult('نظام الإشعارات', false, error.message);
            }
        }

        function testSearch() {
            try {
                // فحص وجود زر البحث
                const searchBtn = document.querySelector('.search-btn');
                
                if (searchBtn) {
                    updateStatus('search-function', 'working');
                    addTestResult('البحث السريع', true, 'زر البحث موجود');
                } else {
                    updateStatus('search-function', 'error');
                    addTestResult('البحث السريع', false, 'زر البحث مفقود');
                }
            } catch (error) {
                updateStatus('search-function', 'error');
                addTestResult('البحث السريع', false, error.message);
            }
        }

        function testUserMenu() {
            try {
                // فحص وجود قائمة المستخدم
                const userMenu = document.querySelector('.user-menu');
                const userMenuToggle = document.querySelector('.user-menu-toggle');
                
                if (userMenu && userMenuToggle) {
                    updateStatus('user-menu', 'working');
                    addTestResult('قائمة المستخدم', true, 'القائمة موجودة ومُعدة بشكل صحيح');
                } else {
                    updateStatus('user-menu', 'error');
                    addTestResult('قائمة المستخدم', false, 'القائمة مفقودة أو غير مُعدة');
                }
            } catch (error) {
                updateStatus('user-menu', 'error');
                addTestResult('قائمة المستخدم', false, error.message);
            }
        }

        function testResponsive() {
            try {
                // فحص التصميم المتجاوب
                const viewport = window.innerWidth;
                const mobileToggle = document.querySelector('.mobile-menu-toggle');
                
                if (viewport < 768 && mobileToggle) {
                    updateStatus('responsive-design', 'working');
                    addTestResult('التصميم المتجاوب', true, 'يتكيف مع الشاشات الصغيرة');
                } else if (viewport >= 768) {
                    updateStatus('responsive-design', 'working');
                    addTestResult('التصميم المتجاوب', true, 'يعمل على الشاشات الكبيرة');
                } else {
                    updateStatus('responsive-design', 'error');
                    addTestResult('التصميم المتجاوب', false, 'لا يتكيف مع حجم الشاشة');
                }
            } catch (error) {
                updateStatus('responsive-design', 'error');
                addTestResult('التصميم المتجاوب', false, error.message);
            }
        }

        function openMainSystem() {
            window.open('index.html', '_blank');
        }

        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status-indicator status-${status}`;
            }
        }

        function addTestResult(testName, passed, details) {
            testResults.push({
                name: testName,
                passed: passed,
                details: details,
                timestamp: new Date().toLocaleString('ar-IQ')
            });
        }

        function showResults() {
            const resultsContainer = document.getElementById('test-results');
            let html = '<div class="test-section"><h3>نتائج الاختبار الشامل</h3>';
            
            const passedTests = testResults.filter(t => t.passed).length;
            const totalTests = testResults.length;
            const successRate = Math.round((passedTests / totalTests) * 100);
            
            html += `<div class="test-result ${successRate >= 80 ? 'test-pass' : successRate >= 60 ? 'test-warning' : 'test-fail'}">`;
            html += `<strong>النتيجة الإجمالية: ${passedTests}/${totalTests} اختبار ناجح (${successRate}%)</strong>`;
            html += `</div>`;
            
            testResults.forEach(result => {
                const resultClass = result.passed ? 'test-pass' : 'test-fail';
                const icon = result.passed ? 'fa-check' : 'fa-times';
                html += `
                    <div class="test-result ${resultClass}">
                        <i class="fas ${icon}"></i>
                        ${result.name}: ${result.details}
                        <small style="display: block; margin-top: 5px;">${result.timestamp}</small>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsContainer.innerHTML = html;
        }
    </script>
</body>
</html>
