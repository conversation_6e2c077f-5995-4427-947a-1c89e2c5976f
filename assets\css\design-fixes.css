/* Design Fixes and Improvements for Iraqi Delivery Management System */

/* ===== CRITICAL DESIGN FIXES ===== */

/* 1. Fix Color Consistency Issues */
:root {
    /* Unified Color Palette */
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f8f9fa;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    /* Consistent Background Colors */
    --bg-primary: #f5f6fa;
    --bg-secondary: #ffffff;
    --bg-tertiary: #e6e7ee;
    
    /* Text Colors with Better Contrast */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --text-white: #ffffff;
    
    /* Enhanced Shadows for Better Depth */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
    --shadow-xl: 0 16px 32px rgba(0,0,0,0.2), 0 8px 16px rgba(0,0,0,0.15);
    
    /* Neumorphic Shadows - Fixed */
    --neu-shadow-light: #ffffff;
    --neu-shadow-dark: #d1d9e6;
    --neu-shadow-outset: 8px 8px 16px var(--neu-shadow-dark), -8px -8px 16px var(--neu-shadow-light);
    --neu-shadow-inset: inset 8px 8px 16px var(--neu-shadow-dark), inset -8px -8px 16px var(--neu-shadow-light);
    --neu-shadow-hover: 12px 12px 24px var(--neu-shadow-dark), -12px -12px 24px var(--neu-shadow-light);
    
    /* Consistent Spacing Scale */
    --space-1: 0.25rem;  /* 4px */
    --space-2: 0.5rem;   /* 8px */
    --space-3: 0.75rem;  /* 12px */
    --space-4: 1rem;     /* 16px */
    --space-5: 1.25rem;  /* 20px */
    --space-6: 1.5rem;   /* 24px */
    --space-8: 2rem;     /* 32px */
    --space-10: 2.5rem;  /* 40px */
    --space-12: 3rem;    /* 48px */
    --space-16: 4rem;    /* 64px */
    
    /* Typography Scale */
    --text-xs: 0.75rem;   /* 12px */
    --text-sm: 0.875rem;  /* 14px */
    --text-base: 1rem;    /* 16px */
    --text-lg: 1.125rem;  /* 18px */
    --text-xl: 1.25rem;   /* 20px */
    --text-2xl: 1.5rem;   /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    --text-4xl: 2.25rem;  /* 36px */
    
    /* Border Radius Scale */
    --radius-sm: 0.375rem;  /* 6px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* 2. Fix Base Typography */
body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 3. Fix Login Page Layout Issues */
.login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
    z-index: 1000;
}

.login-container {
    width: 100%;
    max-width: 420px;
}

.login-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-2xl);
    padding: var(--space-10);
    box-shadow: var(--shadow-xl);
    text-align: center;
    transform: translateY(0);
    transition: var(--transition-normal);
}

.login-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* 4. Fix Logo and Header Alignment */
.login-header .logo {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    color: var(--text-white);
    font-size: var(--text-3xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.login-header .logo:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-xl);
}

.login-header h1 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.login-header p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
}

/* 5. Fix Form Input Styling */
.form-group {
    margin-bottom: var(--space-6);
    text-align: right;
}

.form-group label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: var(--space-4);
    color: var(--text-muted);
    font-size: var(--text-sm);
    z-index: 2;
}

.input-group input,
.select-group select {
    width: 100%;
    padding: var(--space-4) var(--space-12) var(--space-4) var(--space-4);
    border: 2px solid #e1e5e9;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-family: inherit;
    transition: var(--transition-fast);
}

.input-group input:focus,
.select-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-group input::placeholder {
    color: var(--text-muted);
}

/* 6. Fix Button Styling */
.login-btn {
    width: 100%;
    padding: var(--space-4) var(--space-6);
    background: var(--primary-gradient);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 7. Fix Select Dropdown Styling */
.select-group {
    position: relative;
}

.select-group select {
    appearance: none;
    cursor: pointer;
    padding-left: var(--space-12);
}

.select-group i {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
    font-size: var(--text-sm);
}

/* 8. Fix Checkbox and Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--space-6) 0;
    font-size: var(--text-sm);
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--text-secondary);
}

.checkbox-container input {
    margin-left: var(--space-2);
    accent-color: var(--primary-color);
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.forgot-password:hover {
    text-decoration: underline;
    color: #5a6fd8;
}

/* 9. Fix Toggle Password Button */
.toggle-password {
    position: absolute;
    left: var(--space-4);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    z-index: 2;
}

.toggle-password:hover {
    background: rgba(0,0,0,0.05);
    color: var(--text-secondary);
}

/* 10. Fix Footer Styling */
.login-footer {
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid #e1e5e9;
}

.login-footer p {
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin: 0;
}

/* 11. Fix Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-content {
    text-align: center;
    padding: var(--space-8);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e1e5e9;
    border-top: 4px solid var(--primary-color);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin: 0 auto var(--space-6);
}

.loading-content h2 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.loading-content p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* 12. Fix Dashboard Layout Issues */
.dashboard {
    display: none;
    min-height: 100vh;
    background: var(--bg-primary);
}

.dashboard:not(.hidden) {
    display: flex;
}

/* 13. Responsive Design Fixes */
@media (max-width: 768px) {
    .login-card {
        padding: var(--space-8) var(--space-6);
        margin: var(--space-4);
    }
    
    .login-header .logo {
        width: 60px;
        height: 60px;
        font-size: var(--text-2xl);
    }
    
    .login-header h1 {
        font-size: var(--text-xl);
    }
    
    .input-group input,
    .select-group select {
        padding: var(--space-3) var(--space-10) var(--space-3) var(--space-3);
    }
    
    .login-btn {
        padding: var(--space-3) var(--space-5);
    }
}

@media (max-width: 480px) {
    .login-page {
        padding: var(--space-2);
    }
    
    .login-card {
        padding: var(--space-6) var(--space-4);
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }
}

/* 14. Fix Hidden Class */
.hidden {
    display: none !important;
}

/* 15. Accessibility Improvements */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 16. Print Styles */
@media print {
    .login-page,
    .loading-screen {
        display: none !important;
    }
}
