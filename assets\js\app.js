// Main Application Controller
class DeliveryManagementSystem {
    constructor() {
        this.currentPage = 'dashboard';
        this.currentUser = null;
        this.isLoggedIn = false;
        this.managers = {};
        this.init();
    }

    init() {
        this.initializeManagers();
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    initializeManagers() {
        // Initialize all managers
        this.managers.dashboard = new DashboardManager();
        this.managers.modal = new ModalManager();

        // Initialize other managers when their classes are available
        if (typeof OrdersManager !== 'undefined') {
            this.managers.orders = new OrdersManager();
        }
        if (typeof DriversManager !== 'undefined') {
            this.managers.drivers = new DriversManager();
        }
        if (typeof CustomersManager !== 'undefined') {
            this.managers.customers = new CustomersManager();
        }
        if (typeof FinanceManager !== 'undefined') {
            this.managers.finance = new FinanceManager();
        }
        if (typeof TrackingManager !== 'undefined') {
            this.managers.tracking = new TrackingManager();
        }
        if (typeof ReportsManager !== 'undefined') {
            this.managers.reports = new ReportsManager();
        }
        if (typeof NotificationsManager !== 'undefined') {
            this.managers.notifications = new NotificationsManager();
        }
        if (typeof SettingsManager !== 'undefined') {
            this.managers.settings = new SettingsManager();
        }
    }

    setupEventListeners() {
        // Navigation links
        document.addEventListener('click', (e) => {
            // Handle navigation links
            if (e.target.matches('.nav-link') || e.target.closest('.nav-link')) {
                e.preventDefault();
                const link = e.target.closest('.nav-link');
                const page = link.getAttribute('data-page');
                if (page) {
                    this.loadPage(page);
                }
            }

            // Handle logout
            if (e.target.matches('#logout-btn') || e.target.closest('#logout-btn') ||
                e.target.matches('.logout-item') || e.target.closest('.logout-item')) {
                e.preventDefault();
                this.logout();
            }

            // Handle sidebar toggle
            if (e.target.matches('.sidebar-toggle') || e.target.closest('.sidebar-toggle')) {
                e.preventDefault();
                this.toggleSidebar();
            }

            // Handle mobile menu toggle
            if (e.target.matches('.mobile-menu-toggle') || e.target.closest('.mobile-menu-toggle')) {
                e.preventDefault();
                this.toggleMobileMenu();
            }

            // Handle user menu toggle
            if (e.target.matches('.user-menu-toggle') || e.target.closest('.user-menu-toggle')) {
                e.preventDefault();
                this.toggleUserMenu();
            }

            // Handle notification button
            if (e.target.matches('.notification-btn') || e.target.closest('.notification-btn')) {
                e.preventDefault();
                this.showNotifications();
            }

            // Handle search button
            if (e.target.matches('.search-btn') || e.target.closest('.search-btn')) {
                e.preventDefault();
                this.showSearchModal();
            }

            // Handle quick action buttons
            if (e.target.matches('.quick-action-btn') || e.target.closest('.quick-action-btn')) {
                e.preventDefault();
                const action = e.target.closest('.quick-action-btn').getAttribute('data-action');
                this.handleQuickAction(action);
            }

            // Handle stat card clicks
            if (e.target.matches('.stat-card') || e.target.closest('.stat-card')) {
                e.preventDefault();
                const card = e.target.closest('.stat-card');
                this.handleStatCardClick(card);
            }

            // Handle "view all" links
            if (e.target.matches('.view-all-link') || e.target.closest('.view-all-link')) {
                e.preventDefault();
                const link = e.target.closest('.view-all-link');
                const page = link.getAttribute('data-page');
                if (page) {
                    this.loadPage(page);
                }
            }
        });

        // Handle login form
        document.addEventListener('submit', (e) => {
            if (e.target.id === 'login-form') {
                e.preventDefault();
                this.handleLogin(e.target);
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.handleEscape();
            }
        });
    }

    checkAuthStatus() {
        // Hide loading screen first
        this.hideLoadingScreen();

        // Check if user is already logged in
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            try {
                this.currentUser = JSON.parse(savedUser);
                this.isLoggedIn = true;
                this.showDashboard();
            } catch (error) {
                console.error('Error parsing saved user data:', error);
                localStorage.removeItem('currentUser');
                this.showLogin();
            }
        } else {
            this.showLogin();
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }, 1000);
        }
    }

    handleLogin(form) {
        const formData = new FormData(form);
        const username = formData.get('username')?.trim();
        const password = formData.get('password')?.trim();
        const userType = formData.get('user_type');
        const rememberMe = formData.get('remember-me');

        // Validate input
        if (!username || !password || !userType) {
            this.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // Show loading state
        const loginBtn = form.querySelector('.login-btn');
        const originalText = loginBtn.innerHTML;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
        loginBtn.disabled = true;

        // Simulate loading delay
        setTimeout(() => {
            // Simple authentication (in production, this should be server-side)
            if (this.validateCredentials(username, password, userType)) {
                const validCredentials = {
                    'admin': { name: 'مدير النظام' },
                    'مدير': { name: 'المدير العام' },
                    'employee': { name: 'موظف' },
                    'موظف': { name: 'موظف النظام' },
                    'driver': { name: 'مندوب' },
                    'مندوب': { name: 'مندوب التوصيل' },
                    'company': { name: 'شركة' },
                    'شركة': { name: 'شركة التوصيل' }
                };

                this.currentUser = {
                    username: username,
                    displayName: validCredentials[username.toLowerCase()]?.name || username,
                    userType: userType,
                    loginTime: new Date().toISOString()
                };
                this.isLoggedIn = true;

                // Save user data if remember me is checked
                if (rememberMe) {
                    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                }

                this.showDashboard();
                this.showNotification(`مرحباً ${this.currentUser.displayName}، تم تسجيل الدخول بنجاح`, 'success');
            } else {
                this.showNotification('اسم المستخدم أو كلمة المرور أو نوع المستخدم غير صحيح', 'error');

                // Reset form
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
            }
        }, 1000);
    }

    validateCredentials(username, password, userType) {
        // Simple validation (replace with real authentication)
        const validCredentials = {
            // Admin users
            'admin': { password: 'admin123', type: 'admin', name: 'مدير النظام' },
            'مدير': { password: '123456', type: 'admin', name: 'المدير العام' },

            // Employee users
            'employee': { password: 'employee123', type: 'employee', name: 'موظف' },
            'موظف': { password: '123456', type: 'employee', name: 'موظف النظام' },

            // Driver users
            'driver': { password: 'driver123', type: 'driver', name: 'مندوب' },
            'مندوب': { password: '123456', type: 'driver', name: 'مندوب التوصيل' },

            // Company users
            'company': { password: 'company123', type: 'company', name: 'شركة' },
            'شركة': { password: '123456', type: 'company', name: 'شركة التوصيل' }
        };

        const user = validCredentials[username.toLowerCase()];
        return user && user.password === password && user.type === userType;
    }

    showLogin() {
        const loginPage = document.getElementById('login-page');
        const dashboard = document.getElementById('dashboard');

        if (loginPage) {
            loginPage.classList.remove('hidden');
            loginPage.style.display = 'flex';
        }

        if (dashboard) {
            dashboard.classList.add('hidden');
            dashboard.style.display = 'none';
        }

        // Setup login form handlers
        this.setupLoginHandlers();
    }

    showDashboard() {
        const loginPage = document.getElementById('login-page');
        const dashboard = document.getElementById('dashboard');

        if (loginPage) {
            loginPage.classList.add('hidden');
            loginPage.style.display = 'none';
        }

        if (dashboard) {
            dashboard.classList.remove('hidden');
            dashboard.style.display = 'flex';
        }

        // Update user info in header
        this.updateUserInfo();

        // Load dashboard content
        this.loadPage('dashboard');
    }

    setupLoginHandlers() {
        // Toggle password visibility
        const togglePassword = document.querySelector('.toggle-password');
        if (togglePassword) {
            togglePassword.addEventListener('click', () => {
                const passwordInput = document.getElementById('password');
                const icon = togglePassword.querySelector('i');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        // Handle forgot password
        const forgotPassword = document.querySelector('.forgot-password');
        if (forgotPassword) {
            forgotPassword.addEventListener('click', (e) => {
                e.preventDefault();
                this.showNotification('ميزة استعادة كلمة المرور قيد التطوير', 'info');
            });
        }
    }

    updateUserInfo() {
        if (this.currentUser) {
            const userNameElement = document.getElementById('current-user-name');
            const userRoleElement = document.getElementById('current-user-role');

            if (userNameElement) {
                userNameElement.textContent = this.currentUser.displayName || this.currentUser.username;
            }

            if (userRoleElement) {
                const roleNames = {
                    'admin': 'مدير النظام',
                    'employee': 'موظف',
                    'driver': 'مندوب',
                    'company': 'شركة'
                };
                userRoleElement.textContent = roleNames[this.currentUser.userType] || 'مستخدم';
            }

            // Update page title with user info
            const pageTitle = document.getElementById('page-title');
            if (pageTitle && !pageTitle.textContent.includes('لوحة التحكم')) {
                // Keep current page title
            }
        }
    }

    loadPage(pageName) {
        // Update current page
        this.currentPage = pageName;

        // Update navigation active state
        this.updateNavigation(pageName);

        // Update page title
        this.updatePageTitle(pageName);

        // Load page content
        this.loadPageContent(pageName);
    }

    updateNavigation(activePage) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current page
        const activeNavItem = document.querySelector(`[data-page="${activePage}"]`)?.closest('.nav-item');
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }

    updatePageTitle(pageName) {
        const pageTitles = {
            'dashboard': 'لوحة التحكم',
            'orders': 'إدارة الطلبات',
            'drivers': 'إدارة المندوبين',
            'customers': 'إدارة العملاء',
            'finance': 'الإدارة المالية',
            'tracking': 'تتبع الطلبات',
            'reports': 'التقارير',
            'notifications': 'الإشعارات',
            'settings': 'الإعدادات',
            'regions': 'إدارة المناطق',
            'returns': 'إدارة المرتجعات'
        };

        const titleElement = document.getElementById('page-title');
        if (titleElement) {
            titleElement.textContent = pageTitles[pageName] || 'صفحة غير معروفة';
        }
    }

    loadPageContent(pageName) {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        // Show loading state
        pageContent.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

        // Load content based on page
        setTimeout(() => {
            switch (pageName) {
                case 'dashboard':
                    this.managers.dashboard.loadContent();
                    break;
                case 'orders':
                    if (this.managers.orders) {
                        this.managers.orders.loadContent();
                    } else {
                        this.loadFallbackContent('orders', 'إدارة الطلبات');
                    }
                    break;
                case 'drivers':
                    if (this.managers.drivers) {
                        this.managers.drivers.loadContent();
                    } else {
                        this.loadFallbackContent('drivers', 'إدارة المندوبين');
                    }
                    break;
                case 'customers':
                    if (this.managers.customers) {
                        this.managers.customers.loadContent();
                    } else {
                        this.loadFallbackContent('customers', 'إدارة العملاء');
                    }
                    break;
                case 'finance':
                    if (this.managers.finance) {
                        this.managers.finance.loadContent();
                    } else {
                        this.loadFallbackContent('finance', 'الإدارة المالية');
                    }
                    break;
                case 'tracking':
                    if (this.managers.tracking) {
                        this.managers.tracking.loadContent();
                    } else {
                        this.loadFallbackContent('tracking', 'تتبع الطلبات');
                    }
                    break;
                case 'reports':
                    if (this.managers.reports) {
                        this.managers.reports.loadContent();
                    } else {
                        this.loadFallbackContent('reports', 'التقارير');
                    }
                    break;
                case 'notifications':
                    if (this.managers.notifications) {
                        this.managers.notifications.loadContent();
                    } else {
                        this.loadFallbackContent('notifications', 'الإشعارات');
                    }
                    break;
                case 'settings':
                    if (this.managers.settings) {
                        this.managers.settings.loadContent();
                    } else {
                        this.loadFallbackContent('settings', 'الإعدادات');
                    }
                    break;
                default:
                    this.loadFallbackContent(pageName, 'صفحة غير معروفة');
            }
        }, 300);
    }

    loadFallbackContent(pageName, pageTitle) {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        pageContent.innerHTML = `
            <div class="fallback-content">
                <div class="fallback-header">
                    <h2>${pageTitle}</h2>
                    <p>هذه الصفحة قيد التطوير</p>
                    <small>الصفحة المطلوبة: ${pageName}</small>
                </div>
                <div class="fallback-actions">
                    <button class="neu-btn primary" onclick="window.app.loadPage('dashboard')">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </button>
                    <button class="neu-btn secondary" onclick="window.app.showNotification('سيتم إضافة هذه الصفحة قريباً', 'info')">
                        <i class="fas fa-info-circle"></i>
                        المزيد من المعلومات
                    </button>
                </div>
            </div>
        `;
    }

    logout() {
        // Confirm logout
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            // Clear user data
            this.currentUser = null;
            this.isLoggedIn = false;
            localStorage.removeItem('currentUser');

            // Reset form
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.reset();
            }

            // Show login screen
            this.showLogin();
            this.showNotification('تم تسجيل الخروج بنجاح', 'info');
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
        }
    }

    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('mobile-open');
        }
    }

    toggleUserMenu() {
        const userMenu = document.querySelector('.user-menu-dropdown');
        if (userMenu) {
            userMenu.classList.toggle('show');
        }
    }

    showNotifications() {
        if (this.managers.modal) {
            this.managers.modal.showNotificationsModal();
        } else {
            this.showNotification('نظام الإشعارات قيد التطوير', 'info');
        }
    }

    showSearchModal() {
        if (this.managers.modal) {
            this.managers.modal.showSearchModal();
        } else {
            this.showNotification('نظام البحث قيد التطوير', 'info');
        }
    }

    handleQuickAction(action) {
        switch (action) {
            case 'add-order':
                if (this.managers.modal) {
                    this.managers.modal.showAddOrderModal();
                } else {
                    this.showNotification('نظام إضافة الطلبات قيد التطوير', 'info');
                }
                break;
            case 'add-driver':
                if (this.managers.modal) {
                    this.managers.modal.showAddDriverModal();
                } else {
                    this.showNotification('نظام إضافة المندوبين قيد التطوير', 'info');
                }
                break;
            case 'add-customer':
                if (this.managers.modal) {
                    this.managers.modal.showAddCustomerModal();
                } else {
                    this.showNotification('نظام إضافة العملاء قيد التطوير', 'info');
                }
                break;
            case 'generate-report':
                this.loadPage('reports');
                break;
            default:
                this.showNotification('إجراء غير معروف', 'warning');
        }
    }

    handleStatCardClick(card) {
        const cardType = card.getAttribute('data-type');
        const cardFilter = card.getAttribute('data-filter');

        switch (cardType) {
            case 'orders':
                this.loadPage('orders');
                if (this.managers.orders && cardFilter) {
                    // Apply filter if available
                    setTimeout(() => {
                        this.managers.orders.applyFilter(cardFilter);
                    }, 500);
                }
                break;
            case 'drivers':
                this.loadPage('drivers');
                break;
            case 'customers':
                this.loadPage('customers');
                break;
            case 'revenue':
                this.loadPage('finance');
                break;
            default:
                this.showNotification('نوع البطاقة غير معروف', 'warning');
        }
    }

    handleResize() {
        // Handle responsive behavior
        const sidebar = document.querySelector('.sidebar');
        if (window.innerWidth <= 768) {
            sidebar?.classList.add('mobile');
        } else {
            sidebar?.classList.remove('mobile', 'mobile-open');
        }
    }

    handleEscape() {
        // Close any open modals or dropdowns
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            this.managers.modal?.closeModal();
        }

        const openDropdown = document.querySelector('.user-menu-dropdown.show');
        if (openDropdown) {
            openDropdown.classList.remove('show');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);

        // Handle close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }

    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'error': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new DeliveryManagementSystem();
});