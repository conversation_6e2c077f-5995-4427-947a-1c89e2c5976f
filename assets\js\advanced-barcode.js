// Advanced Barcode Generation System
class AdvancedBarcodeSystem {
    constructor() {
        this.supportedTypes = {
            'code128': {
                name: 'Code 128',
                description: 'باركود خطي عالي الكثافة',
                format: 'CODE128',
                maxLength: 80,
                allowedChars: /^[\x00-\x7F]*$/
            },
            'code39': {
                name: 'Code 39',
                description: 'باركود خطي تقليدي',
                format: 'CODE39',
                maxLength: 43,
                allowedChars: /^[A-Z0-9\-. $/+%]*$/
            },
            'qr': {
                name: 'QR Code',
                description: 'رمز استجابة سريعة ثنائي الأبعاد',
                format: 'QR',
                maxLength: 4296,
                allowedChars: /^.*$/
            },
            'ean13': {
                name: 'EAN-13',
                description: 'رمز المنتج الأوروبي',
                format: 'EAN13',
                maxLength: 13,
                allowedChars: /^\d{13}$/
            },
            'datamatrix': {
                name: 'Data Matrix',
                description: 'مصفوفة بيانات ثنائية الأبعاد',
                format: 'DataMatrix',
                maxLength: 2335,
                allowedChars: /^.*$/
            }
        };
        
        this.defaultSettings = {
            width: 2,
            height: 100,
            displayValue: true,
            fontSize: 14,
            textMargin: 5,
            margin: 10,
            background: '#ffffff',
            lineColor: '#000000'
        };
        
        this.currentSettings = { ...this.defaultSettings };
        this.init();
    }

    init() {
        this.loadBarcodeLibraries();
        this.setupEventListeners();
        this.initializeUI();
    }

    loadBarcodeLibraries() {
        // Load JsBarcode for linear barcodes
        if (typeof JsBarcode === 'undefined') {
            this.loadScript('https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js', 'JsBarcode');
        }

        // Load QRCode.js for QR codes
        if (typeof QRCode === 'undefined') {
            this.loadScript('https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js', 'QRCode');
        }
    }

    loadScript(src, globalName) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                console.log(`${globalName} library loaded successfully`);
                resolve();
            };
            script.onerror = () => {
                console.error(`Failed to load ${globalName} library`);
                reject();
            };
            document.head.appendChild(script);
        });
    }

    setupEventListeners() {
        // Barcode type selection
        document.addEventListener('change', (e) => {
            if (e.target.id === 'barcode-type-select') {
                this.handleBarcodeTypeChange(e.target.value);
            }
            if (e.target.classList.contains('barcode-setting')) {
                this.updateBarcodeSettings();
            }
        });

        // Real-time preview
        document.addEventListener('input', (e) => {
            if (e.target.id === 'barcode-data-input') {
                this.validateAndPreview(e.target.value);
            }
            if (e.target.classList.contains('barcode-setting')) {
                this.updateBarcodeSettings();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.generate-barcode-btn') || e.target.closest('.generate-barcode-btn')) {
                e.preventDefault();
                this.generateBarcode();
            }
            if (e.target.matches('.download-barcode-btn') || e.target.closest('.download-barcode-btn')) {
                e.preventDefault();
                this.downloadBarcode();
            }
            if (e.target.matches('.print-barcode-btn') || e.target.closest('.print-barcode-btn')) {
                e.preventDefault();
                this.printBarcode();
            }
            if (e.target.matches('.reset-settings-btn') || e.target.closest('.reset-settings-btn')) {
                e.preventDefault();
                this.resetSettings();
            }
            if (e.target.matches('.batch-generate-btn') || e.target.closest('.batch-generate-btn')) {
                e.preventDefault();
                this.showBatchGenerateModal();
            }
        });
    }

    initializeUI() {
        // Initialize barcode type selector if it exists
        const typeSelect = document.getElementById('barcode-type-select');
        if (typeSelect) {
            this.populateBarcodeTypes(typeSelect);
        }

        // Initialize settings panel
        this.initializeSettingsPanel();
    }

    populateBarcodeTypes(selectElement) {
        selectElement.innerHTML = '';
        
        Object.keys(this.supportedTypes).forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = this.supportedTypes[type].name;
            option.title = this.supportedTypes[type].description;
            selectElement.appendChild(option);
        });
    }

    initializeSettingsPanel() {
        const settingsPanel = document.getElementById('barcode-settings-panel');
        if (!settingsPanel) return;

        settingsPanel.innerHTML = `
            <div class="settings-group">
                <h4>إعدادات الباركود</h4>
                <div class="setting-item">
                    <label for="barcode-width">العرض:</label>
                    <input type="range" id="barcode-width" class="barcode-setting" min="1" max="5" step="0.5" value="${this.currentSettings.width}">
                    <span class="setting-value">${this.currentSettings.width}</span>
                </div>
                <div class="setting-item">
                    <label for="barcode-height">الارتفاع:</label>
                    <input type="range" id="barcode-height" class="barcode-setting" min="50" max="200" step="10" value="${this.currentSettings.height}">
                    <span class="setting-value">${this.currentSettings.height}</span>
                </div>
                <div class="setting-item">
                    <label for="barcode-font-size">حجم الخط:</label>
                    <input type="range" id="barcode-font-size" class="barcode-setting" min="8" max="24" step="2" value="${this.currentSettings.fontSize}">
                    <span class="setting-value">${this.currentSettings.fontSize}</span>
                </div>
                <div class="setting-item">
                    <label for="barcode-margin">الهامش:</label>
                    <input type="range" id="barcode-margin" class="barcode-setting" min="0" max="30" step="5" value="${this.currentSettings.margin}">
                    <span class="setting-value">${this.currentSettings.margin}</span>
                </div>
                <div class="setting-item">
                    <label for="show-text">عرض النص:</label>
                    <input type="checkbox" id="show-text" class="barcode-setting" ${this.currentSettings.displayValue ? 'checked' : ''}>
                </div>
                <div class="setting-item">
                    <label for="barcode-background">لون الخلفية:</label>
                    <input type="color" id="barcode-background" class="barcode-setting" value="${this.currentSettings.background}">
                </div>
                <div class="setting-item">
                    <label for="barcode-color">لون الخطوط:</label>
                    <input type="color" id="barcode-color" class="barcode-setting" value="${this.currentSettings.lineColor}">
                </div>
            </div>
        `;
    }

    handleBarcodeTypeChange(type) {
        const typeInfo = this.supportedTypes[type];
        if (!typeInfo) return;

        // Update UI based on barcode type
        this.updateTypeInfo(typeInfo);
        this.validateCurrentData();
        this.generatePreview();
    }

    updateTypeInfo(typeInfo) {
        const infoElement = document.getElementById('barcode-type-info');
        if (infoElement) {
            infoElement.innerHTML = `
                <div class="type-info">
                    <h4>${typeInfo.name}</h4>
                    <p>${typeInfo.description}</p>
                    <div class="type-specs">
                        <span>الحد الأقصى للأحرف: ${typeInfo.maxLength}</span>
                    </div>
                </div>
            `;
        }
    }

    validateAndPreview(data) {
        const validation = this.validateBarcodeData(data);
        this.displayValidation(validation);
        
        if (validation.isValid) {
            this.generatePreview(data);
        }
    }

    validateBarcodeData(data, type = null) {
        const currentType = type || document.getElementById('barcode-type-select')?.value || 'code128';
        const typeInfo = this.supportedTypes[currentType];
        
        if (!typeInfo) {
            return { isValid: false, message: 'نوع باركود غير مدعوم' };
        }

        if (!data || data.length === 0) {
            return { isValid: false, message: 'يرجى إدخال البيانات' };
        }

        if (data.length > typeInfo.maxLength) {
            return { isValid: false, message: `البيانات طويلة جداً. الحد الأقصى: ${typeInfo.maxLength} حرف` };
        }

        if (!typeInfo.allowedChars.test(data)) {
            return { isValid: false, message: 'البيانات تحتوي على أحرف غير مدعومة' };
        }

        return { isValid: true, message: 'البيانات صالحة' };
    }

    displayValidation(validation) {
        const validationElement = document.getElementById('barcode-validation');
        if (!validationElement) return;

        if (validation.isValid) {
            validationElement.innerHTML = `<span class="validation-success"><i class="fas fa-check"></i> ${validation.message}</span>`;
        } else {
            validationElement.innerHTML = `<span class="validation-error"><i class="fas fa-times"></i> ${validation.message}</span>`;
        }
    }

    updateBarcodeSettings() {
        // Update settings from UI
        this.currentSettings.width = parseFloat(document.getElementById('barcode-width')?.value) || this.defaultSettings.width;
        this.currentSettings.height = parseInt(document.getElementById('barcode-height')?.value) || this.defaultSettings.height;
        this.currentSettings.fontSize = parseInt(document.getElementById('barcode-font-size')?.value) || this.defaultSettings.fontSize;
        this.currentSettings.margin = parseInt(document.getElementById('barcode-margin')?.value) || this.defaultSettings.margin;
        this.currentSettings.displayValue = document.getElementById('show-text')?.checked ?? this.defaultSettings.displayValue;
        this.currentSettings.background = document.getElementById('barcode-background')?.value || this.defaultSettings.background;
        this.currentSettings.lineColor = document.getElementById('barcode-color')?.value || this.defaultSettings.lineColor;

        // Update setting value displays
        document.querySelectorAll('.setting-value').forEach((element, index) => {
            const values = [this.currentSettings.width, this.currentSettings.height, this.currentSettings.fontSize, this.currentSettings.margin];
            if (values[index] !== undefined) {
                element.textContent = values[index];
            }
        });

        // Regenerate preview
        this.generatePreview();
    }

    generateBarcode(data = null, type = null, canvas = null) {
        const barcodeData = data || document.getElementById('barcode-data-input')?.value;
        const barcodeType = type || document.getElementById('barcode-type-select')?.value || 'code128';
        const targetCanvas = canvas || document.getElementById('barcode-canvas');

        if (!barcodeData || !targetCanvas) {
            this.showNotification('بيانات الباركود أو العنصر المستهدف غير متوفر', 'error');
            return false;
        }

        const validation = this.validateBarcodeData(barcodeData, barcodeType);
        if (!validation.isValid) {
            this.showNotification(validation.message, 'error');
            return false;
        }

        try {
            if (barcodeType === 'qr') {
                return this.generateQRCode(barcodeData, targetCanvas);
            } else {
                return this.generateLinearBarcode(barcodeData, barcodeType, targetCanvas);
            }
        } catch (error) {
            console.error('Error generating barcode:', error);
            this.showNotification('خطأ في إنشاء الباركود: ' + error.message, 'error');
            return false;
        }
    }

    generateLinearBarcode(data, type, canvas) {
        if (typeof JsBarcode === 'undefined') {
            throw new Error('مكتبة JsBarcode غير محملة');
        }

        const typeInfo = this.supportedTypes[type];
        if (!typeInfo) {
            throw new Error('نوع باركود غير مدعوم');
        }

        JsBarcode(canvas, data, {
            format: typeInfo.format,
            width: this.currentSettings.width,
            height: this.currentSettings.height,
            displayValue: this.currentSettings.displayValue,
            fontSize: this.currentSettings.fontSize,
            textMargin: this.currentSettings.textMargin,
            margin: this.currentSettings.margin,
            background: this.currentSettings.background,
            lineColor: this.currentSettings.lineColor
        });

        return true;
    }

    generateQRCode(data, canvas) {
        return new Promise((resolve, reject) => {
            if (typeof QRCode === 'undefined') {
                reject(new Error('مكتبة QRCode غير محملة'));
                return;
            }

            // Clear canvas
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            QRCode.toCanvas(canvas, data, {
                width: Math.max(this.currentSettings.height, 200),
                margin: this.currentSettings.margin / 10,
                color: {
                    dark: this.currentSettings.lineColor,
                    light: this.currentSettings.background
                }
            }, (error) => {
                if (error) {
                    reject(error);
                } else {
                    // Add text below QR code if enabled
                    if (this.currentSettings.displayValue) {
                        ctx.fillStyle = this.currentSettings.lineColor;
                        ctx.font = `${this.currentSettings.fontSize}px Arial`;
                        ctx.textAlign = 'center';
                        ctx.fillText(data, canvas.width / 2, canvas.height - 10);
                    }
                    resolve(true);
                }
            });
        });
    }

    generatePreview(data = null) {
        const previewCanvas = document.getElementById('barcode-preview');
        if (!previewCanvas) return;

        const barcodeData = data || document.getElementById('barcode-data-input')?.value;
        if (!barcodeData) {
            // Clear preview
            const ctx = previewCanvas.getContext('2d');
            ctx.clearRect(0, 0, previewCanvas.width, previewCanvas.height);
            return;
        }

        this.generateBarcode(barcodeData, null, previewCanvas);
    }

    downloadBarcode(format = 'png') {
        const canvas = document.getElementById('barcode-canvas');
        if (!canvas) {
            this.showNotification('لا يوجد باركود للتحميل', 'error');
            return;
        }

        const link = document.createElement('a');
        link.download = `barcode_${new Date().getTime()}.${format}`;
        link.href = canvas.toDataURL(`image/${format}`);
        link.click();
    }

    printBarcode() {
        const canvas = document.getElementById('barcode-canvas');
        if (!canvas) {
            this.showNotification('لا يوجد باركود للطباعة', 'error');
            return;
        }

        const printWindow = window.open('', '_blank');
        const barcodeDataURL = canvas.toDataURL();
        const barcodeData = document.getElementById('barcode-data-input')?.value || '';

        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>طباعة الباركود</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 20px; 
                        margin: 0;
                    }
                    .barcode-container { 
                        max-width: 600px; 
                        margin: 0 auto; 
                        border: 2px solid #000; 
                        padding: 30px; 
                        background: white;
                    }
                    .company-info { 
                        font-size: 18px; 
                        font-weight: bold; 
                        margin-bottom: 20px; 
                    }
                    .barcode-image { 
                        margin: 20px 0; 
                        max-width: 100%;
                    }
                    .barcode-data { 
                        font-size: 14px; 
                        margin: 15px 0; 
                        font-family: monospace;
                    }
                    .print-date { 
                        font-size: 12px; 
                        color: #666; 
                        margin-top: 20px;
                    }
                    @media print { 
                        body { margin: 0; padding: 10px; } 
                        .barcode-container { border: 1px solid #000; }
                    }
                </style>
            </head>
            <body>
                <div class="barcode-container">
                    <div class="company-info">شركة التوصيل العراقية</div>
                    <div class="barcode-image">
                        <img src="${barcodeDataURL}" alt="Barcode" />
                    </div>
                    <div class="barcode-data">البيانات: ${barcodeData}</div>
                    <div class="print-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-IQ')}</div>
                </div>
                <script>
                    window.onload = function() {
                        window.print();
                        window.onafterprint = function() {
                            window.close();
                        };
                    };
                </script>
            </body>
            </html>
        `);

        printWindow.document.close();
    }

    resetSettings() {
        this.currentSettings = { ...this.defaultSettings };
        this.initializeSettingsPanel();
        this.generatePreview();
        this.showNotification('تم إعادة تعيين الإعدادات', 'success');
    }

    showBatchGenerateModal() {
        if (window.ModalManager) {
            window.ModalManager.showBatchBarcodeModal();
        } else {
            alert('سيتم إضافة نافذة الإنشاء المجمع قريباً');
        }
    }

    generateBatchBarcodes(dataList, type = 'code128') {
        const results = [];
        
        dataList.forEach((data, index) => {
            try {
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 200;
                
                const success = this.generateBarcode(data, type, canvas);
                if (success) {
                    results.push({
                        data: data,
                        canvas: canvas,
                        dataURL: canvas.toDataURL(),
                        success: true
                    });
                } else {
                    results.push({
                        data: data,
                        success: false,
                        error: 'فشل في إنشاء الباركود'
                    });
                }
            } catch (error) {
                results.push({
                    data: data,
                    success: false,
                    error: error.message
                });
            }
        });

        return results;
    }

    exportBatchBarcodes(results, format = 'zip') {
        // This would create a ZIP file with all barcodes
        // For now, we'll download them individually
        results.forEach((result, index) => {
            if (result.success) {
                const link = document.createElement('a');
                link.download = `barcode_${index + 1}_${result.data}.png`;
                link.href = result.dataURL;
                link.click();
                
                // Add delay between downloads
                setTimeout(() => {}, 100 * index);
            }
        });
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Advanced Barcode System
window.AdvancedBarcodeSystem = new AdvancedBarcodeSystem();
