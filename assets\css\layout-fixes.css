/* Layout Fixes and Page Structure Corrections */
/* إصلاحات التخطيط وتصحيح هيكل الصفحة */

/* ===== CRITICAL LAYOUT FIXES ===== */

/* 1. Reset and Base Layout */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
    height: 100%;
    overflow-x: hidden;
    direction: rtl;
    text-align: right;
}

/* 2. Fix Dashboard Layout Structure */
.dashboard {
    display: flex;
    min-height: 100vh;
    width: 100%;
    position: relative;
}

.dashboard.hidden {
    display: none;
}

/* 3. Fix Sidebar Layout */
.sidebar {
    width: 280px;
    min-height: 100vh;
    background: #ffffff;
    border-left: 1px solid #e1e5e9;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar.mobile {
    transform: translateX(100%);
}

.sidebar.mobile.mobile-open {
    transform: translateX(0);
}

/* 4. Fix Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #2c3e50;
    font-weight: 700;
    font-size: 18px;
}

.sidebar-header .logo i {
    font-size: 24px;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.sidebar-toggle:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

/* 5. Fix Navigation Menu */
.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #7f8c8d;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 0 25px 25px 0;
    margin-left: 20px;
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    background: #f8f9fa;
    color: #2c3e50;
    transform: translateX(-5px);
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-link i {
    font-size: 18px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-link span {
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 6. Fix Sidebar Footer */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    min-width: 0;
}

.user-name {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    display: block;
    font-size: 12px;
    color: #7f8c8d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.logout-btn {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 16px;
    flex-shrink: 0;
}

.logout-btn:hover {
    background: #fee;
    color: #c0392b;
}

/* 7. Fix Main Content Area */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    background: #f5f6fa;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    width: calc(100% - 280px);
}

.sidebar.collapsed + .main-content {
    margin-right: 80px;
    width: calc(100% - 80px);
}

/* 8. Fix Header */
.main-header {
    background: #ffffff;
    border-bottom: 1px solid #e1e5e9;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    min-height: 80px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    font-size: 18px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

#page-title {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-btn {
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 16px;
    position: relative;
}

.header-btn:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.notification-badge {
    position: absolute;
    top: 6px;
    left: 6px;
    background: #e74c3c;
    color: #ffffff;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    font-weight: 600;
}

/* 9. Fix User Menu */
.user-menu {
    position: relative;
    margin-right: 12px;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-menu-toggle:hover {
    background: #f8f9fa;
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    overflow: hidden;
}

.user-menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #7f8c8d;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
    font-size: 14px;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.dropdown-item.logout-item:hover {
    background: #fee;
    color: #e74c3c;
}

.dropdown-divider {
    height: 1px;
    background: #e1e5e9;
    margin: 8px 0;
}

/* 10. Fix Page Content */
.page-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* 11. Fix Login Page Layout */
.login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    z-index: 10000;
}

.login-container {
    width: 100%;
    max-width: 420px;
}

.login-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    text-align: center;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.login-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0,0,0,0.15);
}

/* 12. Responsive Design Fixes */
@media (max-width: 1024px) {
    .sidebar {
        width: 260px;
    }
    
    .main-content {
        margin-right: 260px;
        width: calc(100% - 260px);
    }
    
    .sidebar.collapsed + .main-content {
        margin-right: 70px;
        width: calc(100% - 70px);
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        z-index: 1001;
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        width: 100%;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-header {
        padding: 12px 16px;
    }
    
    #page-title {
        font-size: 20px;
    }
    
    .page-content {
        padding: 16px;
    }
    
    .header-actions {
        gap: 8px;
    }
    
    .user-menu-dropdown {
        left: auto;
        right: 0;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .main-header {
        padding: 10px 12px;
    }
    
    #page-title {
        font-size: 18px;
    }
    
    .page-content {
        padding: 12px;
    }
    
    .sidebar-header {
        padding: 15px;
    }
    
    .sidebar-footer {
        padding: 15px;
    }
    
    .nav-link {
        padding: 10px 15px;
        margin-left: 15px;
    }
}

/* 13. Fix Hidden Class */
.hidden {
    display: none !important;
}

/* 14. Fix Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: #f5f6fa;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    padding: 40px;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e1e5e9;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.loading-content p {
    font-size: 14px;
    color: #7f8c8d;
}

/* 15. Fix Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 16. Fix Focus States */
*:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* 17. Print Styles */
@media print {
    .sidebar,
    .main-header,
    .login-page,
    .loading-screen {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        width: 100% !important;
    }
    
    .page-content {
        padding: 0 !important;
    }
}
