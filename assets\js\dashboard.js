// Dashboard Management System
class DashboardManager {
    constructor() {
        this.charts = {};
        this.refreshInterval = 30000; // 30 seconds
        this.refreshTimer = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Auto-refresh toggle
        document.addEventListener('change', (e) => {
            if (e.target.id === 'auto-refresh') {
                this.toggleAutoRefresh(e.target.checked);
            }
        });

        // Refresh button
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-btn') || e.target.closest('.refresh-btn')) {
                e.preventDefault();
                this.refreshDashboard();
            }

            // Quick actions
            if (e.target.matches('.action-btn') || e.target.closest('.action-btn')) {
                e.preventDefault();
                const action = e.target.closest('.action-btn').getAttribute('data-action');
                this.handleQuickAction(action);
            }

            // View all links
            if (e.target.matches('.view-all-link') || e.target.closest('.view-all-link')) {
                e.preventDefault();
                const page = e.target.closest('.view-all-link').getAttribute('data-page');
                if (window.app) {
                    window.app.loadPage(page);
                }
            }

            // Stat card navigation
            if (e.target.matches('.stat-card') || e.target.closest('.stat-card')) {
                e.preventDefault();
                const statCard = e.target.closest('.stat-card');
                this.handleStatCardClick(statCard);
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const dashboardHTML = `
            <div class="dashboard-header">
                <div class="dashboard-title">
                    <h1>لوحة التحكم الرئيسية</h1>
                    <p>نظرة عامة على أداء شركة التوصيل العراقية</p>
                </div>
                <div class="dashboard-actions">
                    <button class="neu-btn refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                    <label class="auto-refresh-toggle">
                        <input type="checkbox" id="auto-refresh" checked>
                        <span>تحديث تلقائي</span>
                    </label>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card neu-card clickable" data-type="orders" data-filter="all" title="انقر للانتقال إلى صفحة الطلبات">
                    <div class="stat-icon orders">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-orders">0</h3>
                        <p>إجمالي الطلبات</p>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +12%
                        </span>
                    </div>
                </div>

                <div class="stat-card neu-card clickable" data-type="orders" data-filter="delivered" title="انقر لعرض الطلبات المسلمة">
                    <div class="stat-icon delivered">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="delivered-orders">0</h3>
                        <p>طلبات مسلمة</p>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +8%
                        </span>
                    </div>
                </div>

                <div class="stat-card neu-card clickable" data-type="orders" data-filter="pending" title="انقر لعرض الطلبات المعلقة">
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="pending-orders">0</h3>
                        <p>طلبات معلقة</p>
                        <span class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -3%
                        </span>
                    </div>
                </div>

                <div class="stat-card neu-card clickable" data-type="revenue" title="انقر للانتقال إلى الصفحة المالية">
                    <div class="stat-icon revenue">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-revenue">0 د.ع</h3>
                        <p>إجمالي الإيرادات</p>
                        <span class="stat-change positive">
                            <i class="fas fa-arrow-up"></i>
                            +15%
                        </span>
                    </div>
                </div>

                <div class="stat-card neu-card clickable" data-type="orders" data-filter="postponed" title="انقر لعرض الطلبات المؤجلة">
                    <div class="stat-icon postponed">
                        <i class="fas fa-calendar-times"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="postponed-orders">0</h3>
                        <p>طلبات مؤجلة</p>
                        <span class="stat-change warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            تحتاج متابعة
                        </span>
                    </div>
                </div>

                <div class="stat-card neu-card clickable" data-type="orders" data-filter="returned" title="انقر لعرض المرتجعات">
                    <div class="stat-icon returns">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-returns">0</h3>
                        <p>إجمالي المرتجعات</p>
                        <span class="stat-change negative">
                            <i class="fas fa-arrow-down"></i>
                            -2%
                        </span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container neu-card">
                    <div class="neu-card-header">
                        <h3 class="neu-card-title">إحصائيات الطلبات الأسبوعية</h3>
                        <div class="chart-controls">
                            <select class="neu-select" id="chart-period">
                                <option value="week">أسبوعي</option>
                                <option value="month">شهري</option>
                                <option value="year">سنوي</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="orders-chart"></canvas>
                    </div>
                </div>

                <div class="chart-container neu-card">
                    <div class="neu-card-header">
                        <h3 class="neu-card-title">توزيع حالات الطلبات</h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="status-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="activities-section">
                <div class="recent-orders neu-card">
                    <div class="neu-card-header">
                        <h3 class="neu-card-title">الطلبات الأخيرة</h3>
                        <a href="#" class="view-all-link" data-page="orders">عرض الكل</a>
                    </div>
                    <div class="orders-list" id="recent-orders-list">
                        <!-- Recent orders will be loaded here -->
                    </div>
                </div>

                <div class="notifications-panel neu-card">
                    <div class="neu-card-header">
                        <h3 class="neu-card-title">الإشعارات الأخيرة</h3>
                        <a href="#" class="view-all-link" data-page="notifications">عرض الكل</a>
                    </div>
                    <div class="notifications-list" id="recent-notifications-list">
                        <!-- Recent notifications will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions neu-card">
                <div class="neu-card-header">
                    <h3 class="neu-card-title">إجراءات سريعة</h3>
                </div>
                <div class="actions-grid">
                    <button class="action-btn neu-btn" data-action="new-order">
                        <i class="fas fa-plus"></i>
                        طلب جديد
                    </button>
                    <button class="action-btn neu-btn" data-action="add-driver">
                        <i class="fas fa-user-plus"></i>
                        إضافة مندوب
                    </button>
                    <button class="action-btn neu-btn" data-action="add-customer">
                        <i class="fas fa-user-friends"></i>
                        إضافة عميل
                    </button>
                    <button class="action-btn neu-btn" data-action="view-regions">
                        <i class="fas fa-map"></i>
                        إدارة المناطق
                    </button>
                    <button class="action-btn neu-btn" data-action="generate-invoice">
                        <i class="fas fa-file-invoice"></i>
                        إنشاء فاتورة
                    </button>
                    <button class="action-btn neu-btn" data-action="driver-reports">
                        <i class="fas fa-chart-bar"></i>
                        تقارير المندوبين
                    </button>
                    <button class="action-btn neu-btn" data-action="postponed-orders">
                        <i class="fas fa-calendar-times"></i>
                        الطلبات المؤجلة
                    </button>
                    <button class="action-btn neu-btn" data-action="view-finance">
                        <i class="fas fa-money-bill-wave"></i>
                        الإدارة المالية
                    </button>
                </div>
            </div>
        `;

        pageContent.innerHTML = dashboardHTML;
        
        // Load dashboard data
        this.loadDashboardData();
        
        // Setup charts
        this.setupCharts();
        
        // Start auto-refresh
        this.startAutoRefresh();
    }

    async loadDashboardData() {
        try {
            // Simulate API call to get dashboard data
            const data = await this.fetchDashboardData();
            
            // Update statistics
            this.updateStatistics(data.statistics);
            
            // Update recent orders
            this.updateRecentOrders(data.recentOrders);
            
            // Update notifications
            this.updateRecentNotifications(data.recentNotifications);
            
            // Update charts
            this.updateCharts(data.chartData);
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات لوحة التحكم');
        }
    }

    async fetchDashboardData() {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data for demonstration
        return {
            statistics: {
                totalOrders: 1247,
                deliveredOrders: 1089,
                pendingOrders: 135,
                totalRevenue: 62350000, // 62,350,000 IQD
                postponedOrders: 23,
                totalReturns: 23
            },
            recentOrders: [
                { id: 'ORD-2024-001', customer: 'أحمد محمد البغدادي', status: 'delivered', amount: 50000, time: '10:30 ص' },
                { id: 'ORD-2024-002', customer: 'فاطمة علي الكربلائية', status: 'pending', amount: 75000, time: '11:15 ص' },
                { id: 'ORD-2024-003', customer: 'محمد حسن البصري', status: 'in_transit', amount: 40000, time: '11:45 ص' },
                { id: 'ORD-2024-004', customer: 'سارة أحمد النجفية', status: 'delivered', amount: 60000, time: '12:20 م' },
                { id: 'ORD-2024-005', customer: 'عبدالله محمد الموصلي', status: 'postponed', amount: 35000, time: '12:45 م' }
            ],
            recentNotifications: [
                { type: 'order', message: 'طلب جديد من أحمد محمد البغدادي', time: '5 دقائق', icon: 'box' },
                { type: 'delivery', message: 'تم تسليم الطلب ORD-2024-001', time: '10 دقائق', icon: 'check-circle' },
                { type: 'driver', message: 'المندوب محمد علي متاح الآن', time: '15 دقيقة', icon: 'user' },
                { type: 'payment', message: 'تم استلام دفعة بقيمة 500,000 د.ع', time: '20 دقيقة', icon: 'money-bill-wave' },
                { type: 'postponed', message: 'تم تأجيل طلب ORD-2024-005', time: '25 دقيقة', icon: 'calendar-times' }
            ],
            chartData: {
                ordersChart: {
                    labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                    datasets: [
                        {
                            label: 'طلبات جديدة',
                            data: [65, 59, 80, 81, 56, 55, 40],
                            backgroundColor: 'rgba(52, 152, 219, 0.2)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2
                        },
                        {
                            label: 'طلبات مسلمة',
                            data: [45, 49, 60, 71, 46, 45, 30],
                            backgroundColor: 'rgba(39, 174, 96, 0.2)',
                            borderColor: 'rgba(39, 174, 96, 1)',
                            borderWidth: 2
                        }
                    ]
                },
                statusChart: {
                    labels: ['مسلم', 'قيد التوصيل', 'معلق', 'ملغي'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: [
                            'rgba(39, 174, 96, 0.8)',
                            'rgba(52, 152, 219, 0.8)',
                            'rgba(243, 156, 18, 0.8)',
                            'rgba(231, 76, 60, 0.8)'
                        ]
                    }]
                }
            }
        };
    }

    updateStatistics(stats) {
        document.getElementById('total-orders').textContent = this.formatNumber(stats.totalOrders);
        document.getElementById('delivered-orders').textContent = this.formatNumber(stats.deliveredOrders);
        document.getElementById('pending-orders').textContent = this.formatNumber(stats.pendingOrders);
        document.getElementById('total-revenue').textContent = this.formatCurrency(stats.totalRevenue);
        document.getElementById('postponed-orders').textContent = this.formatNumber(stats.postponedOrders);
        document.getElementById('total-returns').textContent = this.formatNumber(stats.totalReturns);
    }

    updateRecentOrders(orders) {
        const ordersList = document.getElementById('recent-orders-list');
        if (!ordersList) return;

        const ordersHTML = orders.map(order => `
            <div class="order-item">
                <div class="order-info">
                    <div class="order-id">${order.id}</div>
                    <div class="order-customer">${order.customer}</div>
                </div>
                <div class="order-details">
                    <div class="order-amount">${this.formatCurrency(order.amount)}</div>
                    <div class="order-status status-${order.status}">
                        ${this.getStatusText(order.status)}
                    </div>
                </div>
                <div class="order-time">${order.time}</div>
            </div>
        `).join('');

        ordersList.innerHTML = ordersHTML;
    }

    updateRecentNotifications(notifications) {
        const notificationsList = document.getElementById('recent-notifications-list');
        if (!notificationsList) return;

        const notificationsHTML = notifications.map(notification => `
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">منذ ${notification.time}</div>
                </div>
            </div>
        `).join('');

        notificationsList.innerHTML = notificationsHTML;
    }

    setupCharts() {
        // This would typically use Chart.js or similar library
        // For now, we'll create placeholder charts
        this.createOrdersChart();
        this.createStatusChart();
    }

    createOrdersChart() {
        const canvas = document.getElementById('orders-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Simple placeholder chart
        ctx.fillStyle = '#3498db';
        ctx.fillRect(50, 50, 100, 100);
        ctx.fillStyle = '#27ae60';
        ctx.fillRect(200, 80, 100, 70);
        
        ctx.fillStyle = '#2c3e50';
        ctx.font = '14px Cairo';
        ctx.textAlign = 'center';
        ctx.fillText('رسم بياني للطلبات', canvas.width / 2, 30);
    }

    createStatusChart() {
        const canvas = document.getElementById('status-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Simple placeholder pie chart
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = 80;
        
        const colors = ['#27ae60', '#3498db', '#f39c12', '#e74c3c'];
        const angles = [0, Math.PI * 0.8, Math.PI * 1.2, Math.PI * 1.6];
        
        colors.forEach((color, index) => {
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, angles[index], angles[index + 1] || Math.PI * 2);
            ctx.lineTo(centerX, centerY);
            ctx.fillStyle = color;
            ctx.fill();
        });
        
        ctx.fillStyle = '#2c3e50';
        ctx.font = '14px Cairo';
        ctx.textAlign = 'center';
        ctx.fillText('توزيع حالات الطلبات', centerX, 30);
    }

    updateCharts(chartData) {
        // Update charts with new data
        // This would typically update Chart.js instances
        console.log('Updating charts with data:', chartData);
    }

    getStatusText(status) {
        const statusTexts = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'in_transit': 'قيد التوصيل',
            'cancelled': 'ملغي',
            'postponed': 'مؤجل',
            'assigned': 'معين للمندوب',
            'picked_up': 'تم الاستلام',
            'returned': 'مرتجع',
            'confirmed': 'مؤكد',
            'processing': 'قيد المعالجة'
        };
        return statusTexts[status] || status;
    }

    refreshDashboard() {
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            refreshBtn.disabled = true;
        }

        this.loadDashboardData().finally(() => {
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> تحديث';
                refreshBtn.disabled = false;
            }
        });
    }

    startAutoRefresh() {
        this.refreshTimer = setInterval(() => {
            this.loadDashboardData();
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    toggleAutoRefresh(enabled) {
        if (enabled) {
            this.startAutoRefresh();
        } else {
            this.stopAutoRefresh();
        }
    }

    handleQuickAction(action) {
        switch (action) {
            case 'new-order':
                if (window.ModalManager) {
                    window.ModalManager.showAddOrderModal();
                } else if (window.app) {
                    window.app.loadPage('orders');
                }
                break;
            case 'add-driver':
                if (window.ModalManager) {
                    window.ModalManager.showAddDriverModal();
                } else if (window.app) {
                    window.app.loadPage('drivers');
                }
                break;
            case 'add-customer':
                if (window.ModalManager) {
                    window.ModalManager.showAddCustomerModal();
                } else if (window.app) {
                    window.app.loadPage('customers');
                }
                break;
            case 'view-regions':
                if (window.app) {
                    window.app.loadPage('regions');
                }
                break;
            case 'generate-invoice':
                if (window.ModalManager) {
                    window.ModalManager.showGenerateInvoiceModal();
                } else if (window.app) {
                    window.app.loadPage('billing');
                }
                break;
            case 'driver-reports':
                if (window.DriverReportsManager) {
                    window.DriverReportsManager.loadContent();
                } else if (window.app) {
                    window.app.loadPage('finance');
                }
                break;
            case 'postponed-orders':
                if (window.app) {
                    window.app.loadPage('postponed-orders');
                }
                break;
            case 'view-finance':
                if (window.app) {
                    window.app.loadPage('finance');
                }
                break;
            default:
                this.showNotification('هذه الميزة قيد التطوير', 'info');
        }
    }

    showNotification(message, type = 'info') {
        // Use main app notification if available
        if (window.app && window.app.showNotification) {
            window.app.showNotification(message, type);
            return;
        }

        // Fallback notification
        console.log(`${type.toUpperCase()}: ${message}`);
    }

    handleStatCardClick(statCard) {
        // Get the stat card type from its classes or content
        const statIcon = statCard.querySelector('.stat-icon');
        if (!statIcon) return;

        const iconClass = statIcon.querySelector('i').className;

        if (iconClass.includes('fa-box')) {
            // Total orders card - navigate to orders page
            if (window.app) {
                window.app.loadPage('orders');
            }
        } else if (iconClass.includes('fa-check-circle')) {
            // Delivered orders card - navigate to orders page with delivered filter
            if (window.app) {
                window.app.loadPage('orders');
                // Set filter after page loads
                setTimeout(() => {
                    if (window.OrdersManager) {
                        window.OrdersManager.setStatusFilter('delivered');
                    }
                }, 100);
            }
        } else if (iconClass.includes('fa-clock')) {
            // Pending orders card - navigate to orders page with pending filter
            if (window.app) {
                window.app.loadPage('orders');
                // Set filter after page loads
                setTimeout(() => {
                    if (window.OrdersManager) {
                        window.OrdersManager.setStatusFilter('pending');
                    }
                }, 100);
            }
        } else if (iconClass.includes('fa-money-bill-wave')) {
            // Total revenue card - navigate to billing page
            if (window.app) {
                window.app.loadPage('billing');
            }
        } else if (iconClass.includes('fa-calendar-times')) {
            // Postponed orders card - navigate to postponed orders page
            if (window.app) {
                window.app.loadPage('postponed-orders');
            }
        } else if (iconClass.includes('fa-undo')) {
            // Returns card - navigate to returns page
            if (window.app) {
                window.app.loadPage('returns');
            }
        }
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    destroy() {
        this.stopAutoRefresh();
        // Clean up charts and event listeners
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });
    }
}

// Initialize Dashboard Manager
window.DashboardManager = new DashboardManager();
