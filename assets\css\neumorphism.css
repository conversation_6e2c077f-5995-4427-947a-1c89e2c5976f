/* Neumorphism Design System for Iraqi Delivery Management */

:root {
    /* Enhanced Neumorphism Colors */
    --neu-bg-primary: #e6e7ee;
    --neu-bg-secondary: #f0f0f3;
    --neu-bg-tertiary: #fafbfc;
    --neu-surface: #e6e7ee;
    --neu-surface-raised: #f0f0f3;
    --neu-surface-pressed: #dde1e7;

    /* Enhanced Shadows */
    --neu-shadow-light: #ffffff;
    --neu-shadow-dark: #d1d9e6;
    --neu-shadow-darker: #c8d0e7;
    --neu-shadow-lighter: #f8f9fa;

    /* Neumorphic Shadow Combinations */
    --neu-shadow-outset: 9px 9px 16px var(--neu-shadow-dark), -9px -9px 16px var(--neu-shadow-light);
    --neu-shadow-inset: inset 9px 9px 16px var(--neu-shadow-dark), inset -9px -9px 16px var(--neu-shadow-light);
    --neu-shadow-pressed: inset 6px 6px 12px var(--neu-shadow-dark), inset -6px -6px 12px var(--neu-shadow-light);
    --neu-shadow-hover: 12px 12px 20px var(--neu-shadow-dark), -12px -12px 20px var(--neu-shadow-light);
    --neu-shadow-active: 3px 3px 6px var(--neu-shadow-dark), -3px -3px 6px var(--neu-shadow-light);

    /* Subtle Shadows for Small Elements */
    --neu-shadow-sm: 4px 4px 8px var(--neu-shadow-dark), -4px -4px 8px var(--neu-shadow-light);
    --neu-shadow-md: 6px 6px 12px var(--neu-shadow-dark), -6px -6px 12px var(--neu-shadow-light);
    --neu-shadow-lg: 12px 12px 24px var(--neu-shadow-dark), -12px -12px 24px var(--neu-shadow-light);

    /* Border Radius for Neumorphism */
    --neu-radius-sm: 8px;
    --neu-radius-md: 12px;
    --neu-radius-lg: 16px;
    --neu-radius-xl: 20px;
    --neu-radius-xxl: 24px;
    --neu-radius-round: 50%;

    /* Transitions */
    --neu-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --neu-transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --neu-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Neumorphic Card */
.neu-card {
    background: var(--neu-surface);
    border-radius: var(--neu-radius-lg);
    box-shadow: var(--neu-shadow-outset);
    transition: var(--neu-transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.neu-card:hover {
    box-shadow: var(--neu-shadow-hover);
    transform: translateY(-2px);
}

.neu-card.pressed {
    box-shadow: var(--neu-shadow-pressed);
    transform: translateY(1px);
}

/* Neumorphic Buttons */
.neu-btn {
    background: var(--neu-surface);
    border: none;
    border-radius: var(--neu-radius-md);
    box-shadow: var(--neu-shadow-md);
    padding: 12px 24px;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--neu-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.neu-btn:hover {
    box-shadow: var(--neu-shadow-hover);
    transform: translateY(-2px);
}

.neu-btn:active {
    box-shadow: var(--neu-shadow-pressed);
    transform: translateY(0);
}

.neu-btn:focus {
    outline: none;
    box-shadow: var(--neu-shadow-md), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Button Variants */
.neu-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: var(--neu-shadow-md);
}

.neu-btn.primary:hover {
    box-shadow: var(--neu-shadow-lg);
    transform: translateY(-3px);
}

.neu-btn.secondary {
    background: var(--neu-surface-raised);
    color: var(--text-primary);
}

.neu-btn.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.neu-btn.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.neu-btn.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

.neu-btn.info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
}

/* Small and Large Button Sizes */
.neu-btn.sm {
    padding: 8px 16px;
    font-size: 0.875rem;
    border-radius: var(--neu-radius-sm);
}

.neu-btn.lg {
    padding: 16px 32px;
    font-size: 1.125rem;
    border-radius: var(--neu-radius-lg);
}

/* Neumorphic Input Fields */
.neu-input {
    background: var(--neu-surface);
    border: none;
    border-radius: var(--neu-radius-md);
    box-shadow: var(--neu-shadow-inset);
    padding: 12px 16px;
    font-family: 'Cairo', sans-serif;
    color: var(--text-primary);
    transition: var(--neu-transition);
    width: 100%;
}

.neu-input:focus {
    outline: none;
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.neu-input::placeholder {
    color: var(--text-secondary);
}

/* Neumorphic Select */
.neu-select {
    background: var(--neu-surface);
    border: none;
    border-radius: var(--neu-radius-md);
    box-shadow: var(--neu-shadow-inset);
    padding: 12px 16px;
    font-family: 'Cairo', sans-serif;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--neu-transition);
    width: 100%;
    appearance: none;
}

.neu-select:focus {
    outline: none;
    box-shadow: var(--neu-shadow-inset), 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Neumorphic Card Header */
.neu-card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--neu-shadow-dark);
    background: var(--neu-surface-raised);
    border-radius: var(--neu-radius-lg) var(--neu-radius-lg) 0 0;
}

.neu-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.neu-card-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 4px 0 0 0;
}

/* Neumorphic Card Body */
.neu-card-body {
    padding: 24px;
}

/* Neumorphic Card Footer */
.neu-card-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid var(--neu-shadow-dark);
    background: var(--neu-surface-raised);
    border-radius: 0 0 var(--neu-radius-lg) var(--neu-radius-lg);
}

/* Neumorphic Progress Bar */
.neu-progress {
    background: var(--neu-surface);
    border-radius: var(--neu-radius-round);
    box-shadow: var(--neu-shadow-inset);
    height: 8px;
    overflow: hidden;
    position: relative;
}

.neu-progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: var(--neu-radius-round);
    transition: width var(--neu-transition);
    position: relative;
}

.neu-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Neumorphic Badge */
.neu-badge {
    background: var(--neu-surface);
    border-radius: var(--neu-radius-round);
    box-shadow: var(--neu-shadow-sm);
    padding: 4px 12px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.neu-badge.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.neu-badge.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.neu-badge.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
}

.neu-badge.danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
}

/* Neumorphic Switch/Toggle */
.neu-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.neu-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.neu-switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--neu-surface);
    border-radius: var(--neu-radius-round);
    box-shadow: var(--neu-shadow-inset);
    transition: var(--neu-transition);
}

.neu-switch-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background: var(--neu-surface);
    border-radius: var(--neu-radius-round);
    box-shadow: var(--neu-shadow-sm);
    transition: var(--neu-transition);
}

.neu-switch input:checked + .neu-switch-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.neu-switch input:checked + .neu-switch-slider:before {
    transform: translateX(30px);
    background: white;
}

/* Neumorphic Checkbox */
.neu-checkbox {
    position: relative;
    display: inline-block;
    cursor: pointer;
    user-select: none;
}

.neu-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.neu-checkbox-mark {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    background: var(--neu-surface);
    border-radius: var(--neu-radius-sm);
    box-shadow: var(--neu-shadow-inset);
    transition: var(--neu-transition);
}

.neu-checkbox input:checked ~ .neu-checkbox-mark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: var(--neu-shadow-sm);
}

.neu-checkbox-mark:after {
    content: "";
    position: absolute;
    display: none;
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.neu-checkbox input:checked ~ .neu-checkbox-mark:after {
    display: block;
}

/* Neumorphic Radio Button */
.neu-radio {
    position: relative;
    display: inline-block;
    cursor: pointer;
    user-select: none;
}

.neu-radio input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.neu-radio-mark {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    background: var(--neu-surface);
    border-radius: var(--neu-radius-round);
    box-shadow: var(--neu-shadow-inset);
    transition: var(--neu-transition);
}

.neu-radio input:checked ~ .neu-radio-mark {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: var(--neu-shadow-sm);
}

.neu-radio-mark:after {
    content: "";
    position: absolute;
    display: none;
    top: 6px;
    left: 6px;
    width: 8px;
    height: 8px;
    border-radius: var(--neu-radius-round);
    background: white;
}

.neu-radio input:checked ~ .neu-radio-mark:after {
    display: block;
}

/* Neumorphic Tooltip */
.neu-tooltip {
    position: relative;
    display: inline-block;
}

.neu-tooltip .neu-tooltip-text {
    visibility: hidden;
    background: var(--neu-surface);
    color: var(--text-primary);
    text-align: center;
    border-radius: var(--neu-radius-md);
    box-shadow: var(--neu-shadow-lg);
    padding: 8px 12px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: var(--neu-transition);
    font-size: 0.875rem;
    white-space: nowrap;
}

.neu-tooltip .neu-tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--neu-surface) transparent transparent transparent;
}

.neu-tooltip:hover .neu-tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Neumorphic Divider */
.neu-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--neu-shadow-dark) 50%, transparent 100%);
    margin: 20px 0;
    position: relative;
}

.neu-divider::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--neu-shadow-light) 50%, transparent 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .neu-card {
        border-radius: var(--neu-radius-md);
        box-shadow: var(--neu-shadow-md);
    }

    .neu-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .neu-card-header {
        padding: 16px 20px 12px;
    }

    .neu-card-body {
        padding: 20px;
    }

    .neu-card-footer {
        padding: 12px 20px 16px;
    }
}

@media (max-width: 480px) {
    .neu-card {
        border-radius: var(--neu-radius-sm);
        box-shadow: var(--neu-shadow-sm);
    }

    .neu-btn {
        padding: 8px 16px;
        font-size: 0.875rem;
    }

    .neu-card-header {
        padding: 12px 16px 8px;
    }

    .neu-card-body {
        padding: 16px;
    }

    .neu-card-footer {
        padding: 8px 16px 12px;
    }
}