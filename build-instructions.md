# تعليمات بناء تطبيق سطح المكتب
## Build Instructions for Desktop Application

---

## 🚀 **خطوات بناء التطبيق:**

### **الخطوة 1: تثبيت المتطلبات**

#### **تثبيت Node.js:**
1. قم بتحميل Node.js من: https://nodejs.org/
2. ثبت النسخة LTS (الموصى بها)
3. تأكد من التثبيت:
```bash
node --version
npm --version
```

#### **تثبيت Git (اختياري):**
```bash
git --version
```

### **الخطوة 2: إعداد المشروع**

#### **في مجلد المشروع، افتح Command Prompt وقم بتشغيل:**

```bash
# تثبيت التبعيات
npm install

# أو إذا كنت تفضل yarn
yarn install
```

### **الخطوة 3: تشغيل التطبيق في وضع التطوير**

```bash
# تشغيل التطبيق
npm start

# أو
npm run dev
```

### **الخطوة 4: بناء التطبيق للإنتاج**

#### **بناء لنظام Windows:**
```bash
npm run build-win
```

#### **بناء لجميع الأنظمة:**
```bash
npm run build
```

#### **بناء محدد:**
```bash
# Windows فقط
npm run build -- --win

# macOS فقط  
npm run build -- --mac

# Linux فقط
npm run build -- --linux

# نسخة محمولة (Portable)
npm run build -- --win portable

# مثبت NSIS
npm run build -- --win nsis
```

---

## 📁 **هيكل المشروع:**

```
iraqi-delivery-system/
├── main.js                 # ملف التطبيق الرئيسي
├── preload.js             # ملف الأمان والواجهات
├── package.json           # إعدادات المشروع والتبعيات
├── splash.html            # شاشة البداية
├── installer.nsh          # إعدادات المثبت
├── index.html             # الصفحة الرئيسية
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التنسيق
│   ├── js/               # ملفات JavaScript
│   ├── icons/            # أيقونات التطبيق
│   └── images/           # الصور
├── dist/                 # ملفات البناء (تُنشأ تلقائياً)
└── node_modules/         # التبعيات (تُنشأ تلقائياً)
```

---

## 🔧 **إعدادات البناء المتقدمة:**

### **تخصيص إعدادات البناء في package.json:**

```json
{
  "build": {
    "appId": "com.iraqidelivery.desktop",
    "productName": "نظام إدارة شركة التوصيل العراقية",
    "directories": {
      "output": "dist"
    },
    "win": {
      "target": "nsis",
      "icon": "assets/icons/icon.ico"
    }
  }
}
```

### **إضافة أيقونات مخصصة:**

1. **Windows:** ضع ملف `icon.ico` في `assets/icons/`
2. **macOS:** ضع ملف `icon.icns` في `assets/icons/`
3. **Linux:** ضع ملف `icon.png` في `assets/icons/`

---

## 📦 **أنواع الملفات المُنتجة:**

### **Windows:**
- **NSIS Installer:** `نظام إدارة شركة التوصيل العراقية Setup 1.0.0.exe`
- **Portable:** `نظام إدارة شركة التوصيل العراقية 1.0.0.exe`

### **macOS:**
- **DMG:** `نظام إدارة شركة التوصيل العراقية-1.0.0.dmg`

### **Linux:**
- **AppImage:** `نظام إدارة شركة التوصيل العراقية-1.0.0.AppImage`
- **DEB:** `iraqi-delivery-system_1.0.0_amd64.deb`

---

## 🛠️ **استكشاف الأخطاء:**

### **خطأ: "npm command not found"**
```bash
# تأكد من تثبيت Node.js وإعادة تشغيل Command Prompt
node --version
```

### **خطأ: "electron-builder not found"**
```bash
# تثبيت electron-builder عالمياً
npm install -g electron-builder
```

### **خطأ في البناء:**
```bash
# مسح cache وإعادة التثبيت
npm cache clean --force
rm -rf node_modules
npm install
```

### **مشاكل الأيقونات:**
- تأكد من وجود ملفات الأيقونات في المجلد الصحيح
- استخدم أحجام صحيحة: ICO (256x256), ICNS (512x512), PNG (512x512)

---

## ⚡ **نصائح لتحسين الأداء:**

### **تقليل حجم التطبيق:**
```json
{
  "build": {
    "files": [
      "**/*",
      "!node_modules/**/*",
      "!dist/**/*",
      "!*.md"
    ]
  }
}
```

### **ضغط الملفات:**
```json
{
  "build": {
    "compression": "maximum"
  }
}
```

### **تحسين الأداء:**
```json
{
  "build": {
    "asar": true,
    "asarUnpack": "**/*.{node,dll}"
  }
}
```

---

## 🔐 **توقيع التطبيق (للنشر التجاري):**

### **Windows Code Signing:**
```json
{
  "build": {
    "win": {
      "certificateFile": "path/to/certificate.p12",
      "certificatePassword": "password"
    }
  }
}
```

### **macOS Code Signing:**
```json
{
  "build": {
    "mac": {
      "identity": "Developer ID Application: Your Name"
    }
  }
}
```

---

## 📋 **قائمة التحقق قبل البناء:**

- [ ] تم تثبيت Node.js
- [ ] تم تثبيت جميع التبعيات (`npm install`)
- [ ] تم اختبار التطبيق (`npm start`)
- [ ] تم إضافة الأيقونات المطلوبة
- [ ] تم تحديث معلومات package.json
- [ ] تم اختبار البناء المحلي
- [ ] تم فحص حجم الملف النهائي

---

## 🚀 **أوامر سريعة:**

```bash
# تطوير
npm start

# بناء سريع لـ Windows
npm run build-win

# بناء كامل
npm run build

# تنظيف وإعادة بناء
npm run clean && npm run build
```

---

## 📞 **الدعم:**

إذا واجهت أي مشاكل في البناء:

1. **تحقق من الوثائق:** https://www.electronjs.org/docs
2. **electron-builder docs:** https://www.electron.build/
3. **GitHub Issues:** https://github.com/electron-userland/electron-builder/issues

---

**🎉 بعد اتباع هذه الخطوات، ستحصل على ملف exe قابل للتشغيل والتوزيع! 🎉**
