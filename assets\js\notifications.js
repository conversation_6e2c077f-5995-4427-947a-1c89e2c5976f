// Comprehensive Notifications System
class NotificationsManager {
    constructor() {
        this.notifications = [];
        this.scheduledNotifications = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            type: '',
            status: '',
            priority: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
        this.startNotificationChecker();
    }

    setupEventListeners() {
        // Filter controls
        document.addEventListener('change', (e) => {
            if (e.target.id === 'notification-type-filter') {
                this.filters.type = e.target.value;
                this.filterNotifications();
            }
            if (e.target.id === 'notification-status-filter') {
                this.filters.status = e.target.value;
                this.filterNotifications();
            }
            if (e.target.id === 'notification-priority-filter') {
                this.filters.priority = e.target.value;
                this.filterNotifications();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.create-notification-btn') || e.target.closest('.create-notification-btn')) {
                e.preventDefault();
                this.showCreateNotificationModal();
            }
            if (e.target.matches('.mark-read-btn') || e.target.closest('.mark-read-btn')) {
                e.preventDefault();
                const notificationId = e.target.closest('.mark-read-btn').getAttribute('data-notification-id');
                this.markAsRead(notificationId);
            }
            if (e.target.matches('.mark-all-read-btn') || e.target.closest('.mark-all-read-btn')) {
                e.preventDefault();
                this.markAllAsRead();
            }
            if (e.target.matches('.delete-notification-btn') || e.target.closest('.delete-notification-btn')) {
                e.preventDefault();
                const notificationId = e.target.closest('.delete-notification-btn').getAttribute('data-notification-id');
                this.deleteNotification(notificationId);
            }
            if (e.target.matches('.refresh-notifications-btn') || e.target.closest('.refresh-notifications-btn')) {
                e.preventDefault();
                this.refreshNotifications();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const notificationsHTML = `
            <div class="notifications-header">
                <div class="notifications-title">
                    <h1>نظام الإشعارات</h1>
                    <p>إدارة الإشعارات الفورية والمجدولة</p>
                </div>
                <div class="notifications-actions">
                    <button class="neu-btn primary create-notification-btn">
                        <i class="fas fa-plus"></i>
                        إشعار جديد
                    </button>
                    <button class="neu-btn secondary mark-all-read-btn">
                        <i class="fas fa-check-double"></i>
                        تسديد الكل
                    </button>
                    <button class="neu-btn refresh-notifications-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="notifications-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>نوع الإشعار:</label>
                        <select id="notification-type-filter" class="neu-select">
                            <option value="">جميع الأنواع</option>
                            <option value="order">طلبات</option>
                            <option value="driver">مندوبين</option>
                            <option value="customer">عملاء</option>
                            <option value="system">نظام</option>
                            <option value="payment">مدفوعات</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="notification-status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="unread">غير مقروء</option>
                            <option value="read">مقروء</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الأولوية:</label>
                        <select id="notification-priority-filter" class="neu-select">
                            <option value="">جميع الأولويات</option>
                            <option value="high">عالية</option>
                            <option value="medium">متوسطة</option>
                            <option value="low">منخفضة</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="notifications-stats">
                <div class="stat-card unread">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="unread-count">0</h3>
                        <p>إشعارات غير مقروءة</p>
                    </div>
                </div>
                <div class="stat-card today">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="today-count">0</h3>
                        <p>إشعارات اليوم</p>
                    </div>
                </div>
                <div class="stat-card scheduled">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="scheduled-count">0</h3>
                        <p>إشعارات مجدولة</p>
                    </div>
                </div>
                <div class="stat-card high-priority">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="high-priority-count">0</h3>
                        <p>أولوية عالية</p>
                    </div>
                </div>
            </div>

            <div class="notifications-list-container">
                <div class="list-header">
                    <h3>قائمة الإشعارات</h3>
                    <div class="list-actions">
                        <button class="neu-btn small refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="notifications-list" id="notifications-list">
                    <!-- Notifications will be loaded here -->
                </div>
                <div class="list-pagination" id="notifications-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>

            <div class="scheduled-notifications-section">
                <div class="section-header">
                    <h3>الإشعارات المجدولة</h3>
                    <button class="neu-btn small create-scheduled-btn">
                        <i class="fas fa-plus"></i>
                        إضافة مجدول
                    </button>
                </div>
                <div class="scheduled-list" id="scheduled-notifications-list">
                    <!-- Scheduled notifications will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = notificationsHTML;
        this.renderNotifications();
        this.renderScheduledNotifications();
        this.updateStats();
    }

    loadSampleData() {
        this.notifications = [
            {
                id: 'NOT-001',
                type: 'order',
                title: 'طلب جديد',
                message: 'تم استلام طلب جديد من أحمد محمد البغدادي',
                priority: 'high',
                status: 'unread',
                timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
                relatedId: 'ORD-2024-001',
                icon: 'box'
            },
            {
                id: 'NOT-002',
                type: 'driver',
                title: 'مندوب متاح',
                message: 'المندوب محمد علي أصبح متاحاً للطلبات',
                priority: 'medium',
                status: 'unread',
                timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
                relatedId: 'DRV-002',
                icon: 'user'
            },
            {
                id: 'NOT-003',
                type: 'payment',
                title: 'دفعة مستلمة',
                message: 'تم استلام دفعة بقيمة 500,000 د.ع من شركة التوصيل السريع',
                priority: 'high',
                status: 'read',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                relatedId: 'PAY-001',
                icon: 'money-bill-wave'
            },
            {
                id: 'NOT-004',
                type: 'system',
                title: 'تحديث النظام',
                message: 'تم تحديث النظام بنجاح إلى الإصدار 2.1.0',
                priority: 'low',
                status: 'read',
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
                relatedId: null,
                icon: 'cog'
            },
            {
                id: 'NOT-005',
                type: 'customer',
                title: 'عميل جديد',
                message: 'تم تسجيل عميل جديد: مطعم بغداد الأصيل',
                priority: 'medium',
                status: 'unread',
                timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
                relatedId: 'CUS-003',
                icon: 'user-friends'
            }
        ];

        this.scheduledNotifications = [
            {
                id: 'SCH-001',
                type: 'order',
                title: 'تذكير متابعة الطلبات',
                message: 'تذكير يومي لمتابعة الطلبات المعلقة',
                priority: 'medium',
                schedule: 'daily',
                time: '09:00',
                isActive: true,
                lastSent: new Date(Date.now() - 24 * 60 * 60 * 1000)
            },
            {
                id: 'SCH-002',
                type: 'driver',
                title: 'تقرير أداء المندوبين',
                message: 'تقرير أسبوعي لأداء المندوبين',
                priority: 'low',
                schedule: 'weekly',
                time: '18:00',
                isActive: true,
                lastSent: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            },
            {
                id: 'SCH-003',
                type: 'payment',
                title: 'تذكير المدفوعات',
                message: 'تذكير بالمدفوعات المستحقة للمندوبين',
                priority: 'high',
                schedule: 'weekly',
                time: '16:00',
                isActive: true,
                lastSent: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            }
        ];
    }

    renderNotifications() {
        const container = document.getElementById('notifications-list');
        if (!container) return;

        const filteredNotifications = this.getFilteredNotifications();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const notificationsToShow = filteredNotifications.slice(startIndex, endIndex);

        container.innerHTML = notificationsToShow.map(notification => `
            <div class="notification-item ${notification.status} ${notification.priority}">
                <div class="notification-icon">
                    <i class="fas fa-${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-header">
                        <h4>${notification.title}</h4>
                        <span class="notification-time">${this.formatTimeAgo(notification.timestamp)}</span>
                    </div>
                    <p class="notification-message">${notification.message}</p>
                    <div class="notification-meta">
                        <span class="notification-type">${this.getTypeText(notification.type)}</span>
                        <span class="notification-priority priority-${notification.priority}">
                            ${this.getPriorityText(notification.priority)}
                        </span>
                    </div>
                </div>
                <div class="notification-actions">
                    ${notification.status === 'unread' ? `
                        <button class="action-btn mark-read-btn" data-notification-id="${notification.id}" title="تسديد">
                            <i class="fas fa-check"></i>
                        </button>
                    ` : ''}
                    <button class="action-btn delete-notification-btn" data-notification-id="${notification.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');

        this.renderPagination(filteredNotifications.length);
    }

    renderScheduledNotifications() {
        const container = document.getElementById('scheduled-notifications-list');
        if (!container) return;

        container.innerHTML = this.scheduledNotifications.map(notification => `
            <div class="scheduled-notification-item ${notification.isActive ? 'active' : 'inactive'}">
                <div class="scheduled-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="scheduled-content">
                    <h4>${notification.title}</h4>
                    <p>${notification.message}</p>
                    <div class="scheduled-meta">
                        <span class="schedule-type">${this.getScheduleText(notification.schedule)}</span>
                        <span class="schedule-time">${notification.time}</span>
                        <span class="last-sent">آخر إرسال: ${this.formatDate(notification.lastSent)}</span>
                    </div>
                </div>
                <div class="scheduled-actions">
                    <button class="action-btn toggle-scheduled-btn" data-notification-id="${notification.id}" title="${notification.isActive ? 'إيقاف' : 'تفعيل'}">
                        <i class="fas fa-${notification.isActive ? 'pause' : 'play'}"></i>
                    </button>
                    <button class="action-btn edit-scheduled-btn" data-notification-id="${notification.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-scheduled-btn" data-notification-id="${notification.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateStats() {
        const unreadCount = this.notifications.filter(n => n.status === 'unread').length;
        const todayCount = this.notifications.filter(n => this.isToday(n.timestamp)).length;
        const scheduledCount = this.scheduledNotifications.filter(n => n.isActive).length;
        const highPriorityCount = this.notifications.filter(n => n.priority === 'high' && n.status === 'unread').length;

        document.getElementById('unread-count').textContent = this.formatNumber(unreadCount);
        document.getElementById('today-count').textContent = this.formatNumber(todayCount);
        document.getElementById('scheduled-count').textContent = this.formatNumber(scheduledCount);
        document.getElementById('high-priority-count').textContent = this.formatNumber(highPriorityCount);
    }

    getFilteredNotifications() {
        return this.notifications.filter(notification => {
            const matchesType = !this.filters.type || notification.type === this.filters.type;
            const matchesStatus = !this.filters.status || notification.status === this.filters.status;
            const matchesPriority = !this.filters.priority || notification.priority === this.filters.priority;

            return matchesType && matchesStatus && matchesPriority;
        });
    }

    renderPagination(totalItems) {
        const pagination = document.getElementById('notifications-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);

        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    filterNotifications() {
        this.currentPage = 1;
        this.renderNotifications();
    }

    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.status = 'read';
            this.renderNotifications();
            this.updateStats();
            this.showNotification('تم تسديد الإشعار', 'success');
        }
    }

    markAllAsRead() {
        this.notifications.forEach(notification => {
            if (notification.status === 'unread') {
                notification.status = 'read';
            }
        });
        this.renderNotifications();
        this.updateStats();
        this.showNotification('تم تسديد جميع الإشعارات', 'success');
    }

    deleteNotification(notificationId) {
        if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
            this.notifications = this.notifications.filter(n => n.id !== notificationId);
            this.renderNotifications();
            this.updateStats();
            this.showNotification('تم حذف الإشعار', 'success');
        }
    }

    refreshNotifications() {
        this.renderNotifications();
        this.renderScheduledNotifications();
        this.updateStats();
        this.showNotification('تم تحديث الإشعارات', 'success');
    }

    startNotificationChecker() {
        // Check for new notifications every 30 seconds
        setInterval(() => {
            this.checkForNewNotifications();
        }, 30000);
    }

    checkForNewNotifications() {
        // Simulate checking for new notifications
        const random = Math.random();
        if (random < 0.1) { // 10% chance of new notification
            this.addNewNotification();
        }
    }

    addNewNotification() {
        const types = ['order', 'driver', 'customer', 'payment', 'system'];
        const priorities = ['high', 'medium', 'low'];
        const messages = [
            'طلب جديد تم استلامه',
            'مندوب أصبح متاحاً',
            'عميل جديد تم تسجيله',
            'دفعة جديدة تم استلامها',
            'تحديث في النظام'
        ];

        const newNotification = {
            id: 'NOT-' + Date.now(),
            type: types[Math.floor(Math.random() * types.length)],
            title: 'إشعار جديد',
            message: messages[Math.floor(Math.random() * messages.length)],
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            status: 'unread',
            timestamp: new Date(),
            relatedId: null,
            icon: 'bell'
        };

        this.notifications.unshift(newNotification);
        this.renderNotifications();
        this.updateStats();

        // Show browser notification if supported
        this.showBrowserNotification(newNotification);
    }

    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/assets/images/logo.png'
            });
        }
    }

    showCreateNotificationModal() {
        if (window.ModalManager) {
            window.ModalManager.showCreateNotificationModal();
        } else {
            alert('سيتم إضافة نافذة إنشاء إشعار قريباً');
        }
    }

    // Helper functions
    formatTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `${minutes} دقيقة`;
        if (hours < 24) return `${hours} ساعة`;
        return `${days} يوم`;
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-IQ');
    }

    isToday(date) {
        const today = new Date();
        const checkDate = new Date(date);
        return today.toDateString() === checkDate.toDateString();
    }

    getTypeText(type) {
        const types = {
            'order': 'طلبات',
            'driver': 'مندوبين',
            'customer': 'عملاء',
            'payment': 'مدفوعات',
            'system': 'نظام'
        };
        return types[type] || type;
    }

    getPriorityText(priority) {
        const priorities = {
            'high': 'عالية',
            'medium': 'متوسطة',
            'low': 'منخفضة'
        };
        return priorities[priority] || priority;
    }

    getScheduleText(schedule) {
        const schedules = {
            'daily': 'يومي',
            'weekly': 'أسبوعي',
            'monthly': 'شهري'
        };
        return schedules[schedule] || schedule;
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Notifications Manager
window.NotificationsManager = new NotificationsManager();
