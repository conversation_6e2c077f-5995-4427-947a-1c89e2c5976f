<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أرقام الوصل والباركود - نظام إدارة شركة التوصيل العراقية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .receipt-manager-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #e6e7ee;
            min-height: 100vh;
        }

        .manager-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: #e6e7ee;
            border-radius: 20px;
            box-shadow: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
        }

        .manager-header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .manager-header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .manager-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: #e6e7ee;
            border-radius: 15px;
            padding: 10px;
            box-shadow: inset 2px 2px 5px #d1d9e6, inset -2px -2px 5px #ffffff;
        }

        .tab-button {
            background: transparent;
            border: none;
            padding: 15px 30px;
            margin: 0 5px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            color: #7f8c8d;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: #3498db;
            color: white;
            box-shadow: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
        }

        .tab-content {
            display: none;
            background: #e6e7ee;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
            margin-bottom: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .receipt-generation-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .generation-panel {
            background: #e6e7ee;
            border-radius: 15px;
            padding: 25px;
            box-shadow: inset 2px 2px 5px #d1d9e6, inset -2px -2px 5px #ffffff;
        }

        .generation-panel h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .mode-selector {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .mode-option {
            flex: 1;
        }

        .mode-option input[type="radio"] {
            display: none;
        }

        .mode-option label {
            display: block;
            padding: 15px;
            background: #e6e7ee;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
        }

        .mode-option input[type="radio"]:checked + label {
            background: #3498db;
            color: white;
            box-shadow: inset 6px 6px 12px #2980b9, inset -6px -6px 12px #5dade2;
        }

        .input-section {
            margin-bottom: 20px;
        }

        .input-section label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .neu-input {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: #e6e7ee;
            box-shadow: inset 6px 6px 12px #d1d9e6, inset -6px -6px 12px #ffffff;
            font-size: 1rem;
            color: #2c3e50;
            transition: all 0.3s ease;
        }

        .neu-input:focus {
            outline: none;
            box-shadow: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
        }

        .neu-select {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: #e6e7ee;
            box-shadow: inset 6px 6px 12px #d1d9e6, inset -6px -6px 12px #ffffff;
            font-size: 1rem;
            color: #2c3e50;
            cursor: pointer;
        }

        .validation-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .validation-success {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .validation-error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .barcode-preview-section {
            text-align: center;
            margin: 30px 0;
        }

        .barcode-canvas {
            background: white;
            border-radius: 10px;
            box-shadow: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
            margin: 20px 0;
            max-width: 100%;
        }

        .barcode-settings {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .setting-group {
            background: #e6e7ee;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 2px 2px 5px #d1d9e6, inset -2px -2px 5px #ffffff;
        }

        .setting-group h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .setting-item label {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 0;
        }

        .setting-value {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
            min-width: 40px;
            text-align: center;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .neu-btn {
            background: #e6e7ee;
            border: none;
            border-radius: 10px;
            padding: 15px 25px;
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .neu-btn:hover {
            transform: translateY(-2px);
            box-shadow: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
        }

        .neu-btn:active {
            transform: translateY(0);
            box-shadow: inset 6px 6px 12px #d1d9e6, inset -6px -6px 12px #ffffff;
        }

        .neu-btn.primary {
            background: #3498db;
            color: white;
        }

        .neu-btn.success {
            background: #27ae60;
            color: white;
        }

        .neu-btn.warning {
            background: #f39c12;
            color: white;
        }

        .neu-btn.danger {
            background: #e74c3c;
            color: white;
        }

        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: #e6e7ee;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
        }

        .stat-icon {
            font-size: 2.5rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .receipt-list {
            background: #e6e7ee;
            border-radius: 15px;
            padding: 25px;
            box-shadow: inset 2px 2px 5px #d1d9e6, inset -2px -2px 5px #ffffff;
            margin: 30px 0;
        }

        .receipt-list h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .receipt-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 6px 6px 12px #d1d9e6, -6px -6px 12px #ffffff;
        }

        .receipt-table th,
        .receipt-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .receipt-table th {
            background: #3498db;
            color: white;
            font-weight: 600;
        }

        .receipt-table tr:hover {
            background: #f8f9fa;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 9px 9px 16px #d1d9e6, -9px -9px 16px #ffffff;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 12px 12px 20px #d1d9e6, -12px -12px 20px #ffffff;
        }

        @media (max-width: 768px) {
            .receipt-generation-section {
                grid-template-columns: 1fr;
            }

            .barcode-settings {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .manager-header h1 {
                font-size: 2rem;
            }

            .receipt-manager-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="goBack()" title="العودة للنظام الرئيسي">
        <i class="fas fa-arrow-right"></i>
    </button>

    <div class="receipt-manager-container">
        <div class="manager-header">
            <h1><i class="fas fa-receipt"></i> إدارة أرقام الوصل والباركود</h1>
            <p>نظام متطور لإنشاء وإدارة أرقام الوصل والباركود بأنواع متعددة</p>
        </div>

        <div class="manager-tabs">
            <button class="tab-button active" data-tab="generation">إنشاء رقم الوصل</button>
            <button class="tab-button" data-tab="barcode">إدارة الباركود</button>
            <button class="tab-button" data-tab="management">إدارة الأرقام</button>
            <button class="tab-button" data-tab="statistics">الإحصائيات</button>
        </div>

        <!-- Tab 1: Receipt Generation -->
        <div class="tab-content active" id="generation-tab">
            <div class="receipt-generation-section">
                <div class="generation-panel">
                    <h3><i class="fas fa-cog"></i> إعدادات الإنشاء</h3>
                    
                    <div class="mode-selector">
                        <div class="mode-option">
                            <input type="radio" id="auto-mode" name="receipt-mode" value="auto" checked>
                            <label for="auto-mode">
                                <i class="fas fa-magic"></i><br>
                                تلقائي
                            </label>
                        </div>
                        <div class="mode-option">
                            <input type="radio" id="manual-mode" name="receipt-mode" value="manual">
                            <label for="manual-mode">
                                <i class="fas fa-edit"></i><br>
                                يدوي
                            </label>
                        </div>
                    </div>

                    <div id="auto-receipt-section">
                        <div class="input-section">
                            <label>تنسيق رقم الوصل:</label>
                            <select id="receipt-format" class="neu-select">
                                <option value="IQ-{YEAR}-{SEQUENCE}">IQ-YYYY-XXXXXX</option>
                                <option value="IRQ-{YEAR}-{SEQUENCE}">IRQ-YYYY-XXXXXX</option>
                                <option value="BG-{YEAR}-{SEQUENCE}">BG-YYYY-XXXXXX</option>
                            </select>
                        </div>
                        <div class="input-section">
                            <label>طول الرقم التسلسلي:</label>
                            <select id="sequence-length" class="neu-select">
                                <option value="4">4 أرقام</option>
                                <option value="5">5 أرقام</option>
                                <option value="6" selected>6 أرقام</option>
                                <option value="7">7 أرقام</option>
                            </select>
                        </div>
                    </div>

                    <div id="manual-receipt-section" style="display: none;">
                        <div class="input-section">
                            <label>رقم الوصل المخصص:</label>
                            <input type="text" id="manual-receipt-input" class="neu-input" 
                                   placeholder="مثال: IQ-2024-001234" maxlength="15">
                            <div id="receipt-validation-result" class="validation-result"></div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="neu-btn primary generate-receipt-btn">
                            <i class="fas fa-plus"></i>
                            إنشاء رقم الوصل
                        </button>
                    </div>
                </div>

                <div class="generation-panel">
                    <h3><i class="fas fa-eye"></i> معاينة النتيجة</h3>
                    
                    <div id="generated-receipt-display">
                        <div class="receipt-display" style="text-align: center; padding: 30px;">
                            <i class="fas fa-receipt" style="font-size: 3rem; color: #bdc3c7; margin-bottom: 15px;"></i>
                            <p style="color: #7f8c8d;">سيظهر رقم الوصل المُنشأ هنا</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab 2: Barcode Management -->
        <div class="tab-content" id="barcode-tab">
            <div class="barcode-generation-section">
                <div class="input-section">
                    <label>البيانات المراد تحويلها لباركود:</label>
                    <input type="text" id="barcode-data-input" class="neu-input" 
                           placeholder="أدخل رقم الوصل أو أي بيانات أخرى">
                    <div id="barcode-validation" class="validation-result"></div>
                </div>

                <div class="input-section">
                    <label>نوع الباركود:</label>
                    <select id="barcode-type-select" class="neu-select">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                    <div id="barcode-type-info"></div>
                </div>

                <div class="barcode-settings">
                    <div id="barcode-settings-panel">
                        <!-- Settings will be populated by JavaScript -->
                    </div>
                </div>

                <div class="barcode-preview-section">
                    <h3>معاينة الباركود</h3>
                    <canvas id="barcode-preview" width="400" height="200" class="barcode-canvas"></canvas>
                </div>

                <div class="action-buttons">
                    <button class="neu-btn primary generate-barcode-btn">
                        <i class="fas fa-qrcode"></i>
                        إنشاء الباركود
                    </button>
                    <button class="neu-btn success download-barcode-btn">
                        <i class="fas fa-download"></i>
                        تحميل
                    </button>
                    <button class="neu-btn warning print-barcode-btn">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button class="neu-btn danger reset-settings-btn">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>

                <div class="barcode-preview-section">
                    <h3>الباركود النهائي</h3>
                    <canvas id="barcode-canvas" width="400" height="200" class="barcode-canvas"></canvas>
                </div>
            </div>
        </div>

        <!-- Tab 3: Receipt Management -->
        <div class="tab-content" id="management-tab">
            <div class="receipt-list">
                <h3><i class="fas fa-list"></i> قائمة أرقام الوصل</h3>
                
                <div class="action-buttons" style="margin-bottom: 20px;">
                    <button class="neu-btn primary">
                        <i class="fas fa-search"></i>
                        بحث متقدم
                    </button>
                    <button class="neu-btn success">
                        <i class="fas fa-file-export"></i>
                        تصدير القائمة
                    </button>
                    <button class="neu-btn warning">
                        <i class="fas fa-file-import"></i>
                        استيراد من Excel
                    </button>
                </div>

                <table class="receipt-table">
                    <thead>
                        <tr>
                            <th>رقم الوصل</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الطلب المرتبط</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="receipt-table-body">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Tab 4: Statistics -->
        <div class="tab-content" id="statistics-tab">
            <div class="statistics-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stat-number" id="total-receipts">0</div>
                    <div class="stat-label">إجمالي أرقام الوصل</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-number" id="today-receipts">0</div>
                    <div class="stat-label">أرقام اليوم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number" id="current-year-receipts">0</div>
                    <div class="stat-label">أرقام السنة الحالية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="stat-number" id="generated-barcodes">0</div>
                    <div class="stat-label">باركود مُنشأ</div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="neu-btn primary show-receipt-stats-btn">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات مفصلة
                </button>
                <button class="neu-btn success">
                    <i class="fas fa-file-pdf"></i>
                    تقرير شامل
                </button>
            </div>
        </div>
    </div>

    <!-- Load external libraries -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

    <!-- Load our systems -->
    <script src="assets/js/receipt-system.js"></script>
    <script src="assets/js/advanced-barcode.js"></script>
    <script src="assets/js/receipt-api-client.js"></script>
    <script src="assets/js/advanced-receipt-features.js"></script>

    <script>
        // Receipt and Barcode Manager UI Controller
        class ReceiptBarcodeManagerUI {
            constructor() {
                this.currentTab = 'generation';
                this.init();
            }

            init() {
                this.setupTabSwitching();
                this.setupModeToggle();
                this.populateReceiptTable();
                this.updateStatistics();
                this.initializeBarcodeSystem();
            }

            setupTabSwitching() {
                document.querySelectorAll('.tab-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const tabName = e.target.getAttribute('data-tab');
                        this.switchTab(tabName);
                    });
                });
            }

            switchTab(tabName) {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                // Add active class to selected tab and content
                document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
                document.getElementById(`${tabName}-tab`).classList.add('active');

                this.currentTab = tabName;

                // Load tab-specific content
                if (tabName === 'management') {
                    this.populateReceiptTable();
                } else if (tabName === 'statistics') {
                    this.updateStatistics();
                }
            }

            setupModeToggle() {
                document.querySelectorAll('input[name="receipt-mode"]').forEach(radio => {
                    radio.addEventListener('change', (e) => {
                        const mode = e.target.value;
                        const autoSection = document.getElementById('auto-receipt-section');
                        const manualSection = document.getElementById('manual-receipt-section');

                        if (mode === 'manual') {
                            autoSection.style.display = 'none';
                            manualSection.style.display = 'block';
                        } else {
                            autoSection.style.display = 'block';
                            manualSection.style.display = 'none';
                        }
                    });
                });
            }

            populateReceiptTable() {
                const tbody = document.getElementById('receipt-table-body');
                if (!tbody) return;

                // Get receipt numbers from ReceiptSystem
                const receipts = window.ReceiptSystem ? [...window.ReceiptSystem.receiptNumbers] : [];

                if (receipts.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 30px; color: #7f8c8d;">
                                <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                                لا توجد أرقام وصل مُنشأة بعد
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = receipts.sort().reverse().slice(0, 20).map((receipt, index) => `
                    <tr>
                        <td><strong>${receipt}</strong></td>
                        <td>${new Date().toLocaleDateString('ar-IQ')}</td>
                        <td>ORD-2024-${String(index + 1).padStart(3, '0')}</td>
                        <td><span class="status-badge active">نشط</span></td>
                        <td>
                            <button class="neu-btn small edit-receipt-btn" data-receipt="${receipt}" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="neu-btn small regenerate-barcode-btn" data-receipt="${receipt}" title="إعادة إنشاء الباركود">
                                <i class="fas fa-qrcode"></i>
                            </button>
                            <button class="neu-btn small print-receipt-btn" data-receipt="${receipt}" title="طباعة">
                                <i class="fas fa-print"></i>
                            </button>
                        </td>
                    </tr>
                `).join('');
            }

            updateStatistics() {
                if (!window.ReceiptSystem) return;

                const stats = window.ReceiptSystem.getReceiptStatistics();

                // Update stat cards
                document.getElementById('total-receipts').textContent = this.formatNumber(stats.total);
                document.getElementById('today-receipts').textContent = this.formatNumber(5); // Mock data
                document.getElementById('current-year-receipts').textContent = this.formatNumber(stats.currentYear);
                document.getElementById('generated-barcodes').textContent = this.formatNumber(stats.total);
            }

            initializeBarcodeSystem() {
                // Initialize barcode type selector
                const typeSelect = document.getElementById('barcode-type-select');
                if (typeSelect && window.AdvancedBarcodeSystem) {
                    window.AdvancedBarcodeSystem.populateBarcodeTypes(typeSelect);
                    window.AdvancedBarcodeSystem.initializeSettingsPanel();
                }
            }

            formatNumber(number) {
                if (typeof number === 'number') {
                    return number.toLocaleString('en-US');
                }
                return String(number);
            }
        }

        // Initialize the UI when page loads
        document.addEventListener('DOMContentLoaded', function() {
            window.receiptBarcodeManagerUI = new ReceiptBarcodeManagerUI();
        });

        // Back button functionality
        function goBack() {
            if (window.opener) {
                window.close();
            } else {
                window.location.href = 'index.html';
            }
        }

        // Additional event handlers for table actions
        document.addEventListener('click', function(e) {
            if (e.target.matches('.edit-receipt-btn') || e.target.closest('.edit-receipt-btn')) {
                e.preventDefault();
                const receipt = e.target.closest('.edit-receipt-btn').getAttribute('data-receipt');
                editReceipt(receipt);
            }

            if (e.target.matches('.regenerate-barcode-btn') || e.target.closest('.regenerate-barcode-btn')) {
                e.preventDefault();
                const receipt = e.target.closest('.regenerate-barcode-btn').getAttribute('data-receipt');
                regenerateBarcodeForReceipt(receipt);
            }

            if (e.target.matches('.print-receipt-btn') || e.target.closest('.print-receipt-btn')) {
                e.preventDefault();
                const receipt = e.target.closest('.print-receipt-btn').getAttribute('data-receipt');
                printReceiptBarcode(receipt);
            }
        });

        function editReceipt(receiptNumber) {
            alert(`تعديل رقم الوصل: ${receiptNumber}\nسيتم إضافة نافذة التعديل قريباً`);
        }

        function regenerateBarcodeForReceipt(receiptNumber) {
            if (window.AdvancedBarcodeSystem) {
                // Set the receipt number in the barcode input
                const barcodeInput = document.getElementById('barcode-data-input');
                if (barcodeInput) {
                    barcodeInput.value = receiptNumber;
                    window.AdvancedBarcodeSystem.generateBarcode(receiptNumber);

                    // Switch to barcode tab
                    window.receiptBarcodeManagerUI.switchTab('barcode');

                    // Show success message
                    alert('تم إنشاء الباركود لرقم الوصل: ' + receiptNumber);
                }
            }
        }

        function printReceiptBarcode(receiptNumber) {
            if (window.AdvancedBarcodeSystem) {
                // Generate barcode for printing
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = 400;
                tempCanvas.height = 200;

                window.AdvancedBarcodeSystem.generateBarcode(receiptNumber, 'code128', tempCanvas);

                // Print the barcode
                const printWindow = window.open('', '_blank');
                const barcodeDataURL = tempCanvas.toDataURL();

                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>طباعة الوصل - ${receiptNumber}</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
                            .receipt-container { max-width: 400px; margin: 0 auto; border: 2px solid #000; padding: 20px; }
                            .company-name { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
                            .receipt-number { font-size: 16px; margin: 15px 0; }
                            .barcode { margin: 20px 0; }
                            .date { font-size: 12px; color: #666; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <div class="receipt-container">
                            <div class="company-name">شركة التوصيل العراقية</div>
                            <div class="receipt-number">رقم الوصل: ${receiptNumber}</div>
                            <div class="barcode">
                                <img src="${barcodeDataURL}" alt="Barcode" />
                            </div>
                            <div class="date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-IQ')}</div>
                        </div>
                        <script>
                            window.onload = function() {
                                window.print();
                                window.onafterprint = function() {
                                    window.close();
                                };
                            };
                        </script>
                    </body>
                    </html>
                `);

                printWindow.document.close();
            }
        }

        // Initialize systems when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the Receipt and Barcode Manager
            window.receiptBarcodeManagerUI = new ReceiptBarcodeManagerUI();

            // Initialize barcode system if available
            if (typeof AdvancedBarcodeSystem !== 'undefined') {
                window.advancedBarcodeSystem = new AdvancedBarcodeSystem();
            }

            // Initialize API client if available
            if (typeof ReceiptAPIClient !== 'undefined') {
                window.receiptAPIClient = new ReceiptAPIClient();
            }

            // Initialize advanced features if available
            if (typeof AdvancedReceiptFeatures !== 'undefined') {
                window.advancedReceiptFeatures = new AdvancedReceiptFeatures();
            }
        });

        // Back button functionality
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
