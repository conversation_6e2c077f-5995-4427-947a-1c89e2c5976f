// Driver Reports System
class DriverReportsManager {
    constructor() {
        this.driverReports = [];
        this.currentPeriod = 'month';
        this.selectedDate = new Date();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Period filter
        document.addEventListener('change', (e) => {
            if (e.target.id === 'period-filter') {
                this.currentPeriod = e.target.value;
                this.updateReports();
            }
            if (e.target.id === 'date-filter') {
                this.selectedDate = new Date(e.target.value);
                this.updateReports();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pay-driver-btn') || e.target.closest('.pay-driver-btn')) {
                e.preventDefault();
                const driverId = e.target.closest('.pay-driver-btn').getAttribute('data-driver-id');
                this.payDriver(driverId);
            }
            if (e.target.matches('.view-driver-details-btn') || e.target.closest('.view-driver-details-btn')) {
                e.preventDefault();
                const driverId = e.target.closest('.view-driver-details-btn').getAttribute('data-driver-id');
                this.showDriverDetails(driverId);
            }
            if (e.target.matches('.export-reports-btn') || e.target.closest('.export-reports-btn')) {
                e.preventDefault();
                this.exportReports();
            }
            if (e.target.matches('.bulk-pay-btn') || e.target.closest('.bulk-pay-btn')) {
                e.preventDefault();
                this.showBulkPayModal();
            }
            if (e.target.matches('.commission-settings-btn') || e.target.closest('.commission-settings-btn')) {
                e.preventDefault();
                this.showCommissionSettingsModal();
            }
            if (e.target.matches('.performance-analysis-btn') || e.target.closest('.performance-analysis-btn')) {
                e.preventDefault();
                this.showPerformanceAnalysis();
            }
            if (e.target.matches('.generate-payslip-btn') || e.target.closest('.generate-payslip-btn')) {
                e.preventDefault();
                const driverId = e.target.closest('.generate-payslip-btn').getAttribute('data-driver-id');
                this.generatePayslip(driverId);
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const reportsHTML = `
            <div class="driver-reports-header">
                <div class="reports-title">
                    <h1>تقارير المندوبين</h1>
                    <p>المبالغ المستحقة وحسابات المندوبين</p>
                </div>
                <div class="reports-actions">
                    <button class="neu-btn success bulk-pay-btn">
                        <i class="fas fa-money-bill-wave"></i>
                        دفع مجمع
                    </button>
                    <button class="neu-btn warning commission-settings-btn">
                        <i class="fas fa-cog"></i>
                        إعدادات العمولة
                    </button>
                    <button class="neu-btn info performance-analysis-btn">
                        <i class="fas fa-chart-line"></i>
                        تحليل الأداء
                    </button>
                    <button class="neu-btn primary export-reports-btn">
                        <i class="fas fa-file-export"></i>
                        تصدير التقرير
                    </button>
                    <button class="neu-btn secondary print-btn">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>

            <div class="reports-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>الفترة:</label>
                        <select id="period-filter" class="neu-select">
                            <option value="day">يومي</option>
                            <option value="week">أسبوعي</option>
                            <option value="month" selected>شهري</option>
                            <option value="quarter">ربع سنوي</option>
                            <option value="year">سنوي</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>التاريخ:</label>
                        <input type="date" id="date-filter" class="neu-input" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                </div>
            </div>

            <div class="reports-overview">
                <div class="overview-card total-drivers">
                    <div class="overview-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-drivers-count">0</h3>
                        <p>إجمالي المندوبين</p>
                    </div>
                </div>
                <div class="overview-card total-deliveries">
                    <div class="overview-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-deliveries-count">0</h3>
                        <p>إجمالي التوصيلات</p>
                    </div>
                </div>
                <div class="overview-card total-commissions">
                    <div class="overview-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="total-commissions-amount">0 د.ع</h3>
                        <p>إجمالي العمولات</p>
                    </div>
                </div>
                <div class="overview-card net-revenue">
                    <div class="overview-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="net-revenue-amount">0 د.ع</h3>
                        <p>صافي إيرادات الشركة</p>
                    </div>
                </div>
            </div>

            <div class="driver-reports-table-container">
                <div class="table-header">
                    <h3>تقرير المندوبين المفصل</h3>
                </div>
                <div class="table-wrapper">
                    <table class="driver-reports-table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>عدد الطلبات</th>
                                <th>الطلبات المكتملة</th>
                                <th>نسبة النجاح</th>
                                <th>إجمالي الإيرادات</th>
                                <th>العمولة المستحقة</th>
                                <th>المبلغ الصافي للشركة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="driver-reports-tbody">
                            <!-- Driver reports will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="payment-summary">
                <div class="summary-header">
                    <h3>ملخص المدفوعات</h3>
                </div>
                <div class="summary-cards">
                    <div class="summary-card pending">
                        <div class="card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="pending-payments-amount">0 د.ع</h4>
                            <p>مدفوعات معلقة</p>
                            <small id="pending-drivers-count">0 مندوب</small>
                        </div>
                    </div>
                    <div class="summary-card paid">
                        <div class="card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="paid-payments-amount">0 د.ع</h4>
                            <p>مدفوعات مسددة</p>
                            <small id="paid-drivers-count">0 مندوب</small>
                        </div>
                    </div>
                    <div class="summary-card company-share">
                        <div class="card-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="company-share-amount">0 د.ع</h4>
                            <p>نصيب الشركة</p>
                            <small>بعد خصم العمولات</small>
                        </div>
                    </div>
                </div>
            </div>
        `;

        pageContent.innerHTML = reportsHTML;
        this.updateReports();
        this.renderDriverReports();
        this.updateSummary();
    }

    loadSampleData() {
        this.driverReports = [
            {
                driverId: 1,
                driverName: 'أحمد محمد المندوب',
                totalOrders: 45,
                completedOrders: 42,
                successRate: 93.3,
                totalRevenue: 2100000,
                commissionAmount: 630000,
                netRevenue: 1470000,
                paymentStatus: 'pending',
                lastPayment: null,
                region: 'بغداد - الكرخ'
            },
            {
                driverId: 2,
                driverName: 'محمد علي المندوب',
                totalOrders: 38,
                completedOrders: 35,
                successRate: 92.1,
                totalRevenue: 1750000,
                commissionAmount: 420000,
                netRevenue: 1330000,
                paymentStatus: 'paid',
                lastPayment: '2024-12-20',
                region: 'بغداد - الرصافة'
            },
            {
                driverId: 3,
                driverName: 'عبدالله حسن المندوب',
                totalOrders: 52,
                completedOrders: 48,
                successRate: 92.3,
                totalRevenue: 2400000,
                commissionAmount: 720000,
                netRevenue: 1680000,
                paymentStatus: 'pending',
                lastPayment: null,
                region: 'بغداد - الصدر'
            },
            {
                driverId: 4,
                driverName: 'حسين كريم المندوب',
                totalOrders: 29,
                completedOrders: 27,
                successRate: 93.1,
                totalRevenue: 1350000,
                commissionAmount: 405000,
                netRevenue: 945000,
                paymentStatus: 'pending',
                lastPayment: null,
                region: 'بغداد - الكاظمية'
            },
            {
                driverId: 5,
                driverName: 'علي أحمد المندوب',
                totalOrders: 33,
                completedOrders: 30,
                successRate: 90.9,
                totalRevenue: 1500000,
                commissionAmount: 450000,
                netRevenue: 1050000,
                paymentStatus: 'paid',
                lastPayment: '2024-12-18',
                region: 'بغداد - الأعظمية'
            }
        ];
    }

    renderDriverReports() {
        const tbody = document.getElementById('driver-reports-tbody');
        if (!tbody) return;

        tbody.innerHTML = this.driverReports.map(report => `
            <tr>
                <td>
                    <div class="driver-info">
                        <div class="driver-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="driver-details">
                            <strong>${report.driverName}</strong>
                            <small>${report.region}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="orders-count">${report.totalOrders}</span>
                </td>
                <td>
                    <span class="completed-orders">${report.completedOrders}</span>
                </td>
                <td>
                    <div class="success-rate">
                        <span class="rate-percentage">${report.successRate.toFixed(1)}%</span>
                        <div class="rate-bar">
                            <div class="rate-fill" style="width: ${report.successRate}%"></div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="revenue-amount">${this.formatCurrency(report.totalRevenue)}</span>
                </td>
                <td>
                    <span class="commission-amount">${this.formatCurrency(report.commissionAmount)}</span>
                </td>
                <td>
                    <span class="net-amount">${this.formatCurrency(report.netRevenue)}</span>
                </td>
                <td>
                    <span class="payment-status ${report.paymentStatus}">
                        ${this.getPaymentStatusText(report.paymentStatus)}
                    </span>
                    ${report.lastPayment ? `<small>آخر دفعة: ${this.formatDate(report.lastPayment)}</small>` : ''}
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-driver-details-btn" data-driver-id="${report.driverId}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${report.paymentStatus === 'pending' ? `
                            <button class="action-btn pay-driver-btn" data-driver-id="${report.driverId}" title="دفع العمولة">
                                <i class="fas fa-money-bill-wave"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateReports() {
        // Update overview cards
        const totalDrivers = this.driverReports.length;
        const totalDeliveries = this.driverReports.reduce((sum, report) => sum + report.completedOrders, 0);
        const totalCommissions = this.driverReports.reduce((sum, report) => sum + report.commissionAmount, 0);
        const netRevenue = this.driverReports.reduce((sum, report) => sum + report.netRevenue, 0);

        document.getElementById('total-drivers-count').textContent = totalDrivers;
        document.getElementById('total-deliveries-count').textContent = totalDeliveries;
        document.getElementById('total-commissions-amount').textContent = this.formatCurrency(totalCommissions);
        document.getElementById('net-revenue-amount').textContent = this.formatCurrency(netRevenue);
    }

    updateSummary() {
        const pendingReports = this.driverReports.filter(r => r.paymentStatus === 'pending');
        const paidReports = this.driverReports.filter(r => r.paymentStatus === 'paid');

        const pendingAmount = pendingReports.reduce((sum, report) => sum + report.commissionAmount, 0);
        const paidAmount = paidReports.reduce((sum, report) => sum + report.commissionAmount, 0);
        const companyShare = this.driverReports.reduce((sum, report) => sum + report.netRevenue, 0);

        document.getElementById('pending-payments-amount').textContent = this.formatCurrency(pendingAmount);
        document.getElementById('pending-drivers-count').textContent = `${pendingReports.length} مندوب`;

        document.getElementById('paid-payments-amount').textContent = this.formatCurrency(paidAmount);
        document.getElementById('paid-drivers-count').textContent = `${paidReports.length} مندوب`;

        document.getElementById('company-share-amount').textContent = this.formatCurrency(companyShare);
    }

    payDriver(driverId) {
        const report = this.driverReports.find(r => r.driverId == driverId);
        if (!report) return;

        if (confirm(`هل أنت متأكد من دفع عمولة ${report.driverName} بمبلغ ${this.formatCurrency(report.commissionAmount)}؟`)) {
            report.paymentStatus = 'paid';
            report.lastPayment = new Date().toISOString().split('T')[0];
            
            this.renderDriverReports();
            this.updateSummary();
            
            alert(`تم دفع عمولة ${report.driverName} بنجاح`);
        }
    }

    showDriverDetails(driverId) {
        if (window.ModalManager) {
            window.ModalManager.showDriverReportDetailsModal(driverId);
        } else {
            alert(`سيتم إضافة نافذة تفاصيل المندوب ${driverId} قريباً`);
        }
    }

    exportReports() {
        const csvContent = this.convertToCSV(this.driverReports);
        this.downloadCSV(csvContent, 'driver_reports.csv');
    }

    convertToCSV(reports) {
        const headers = ['المندوب', 'المنطقة', 'إجمالي الطلبات', 'الطلبات المكتملة', 'نسبة النجاح', 'إجمالي الإيرادات', 'العمولة المستحقة', 'صافي الشركة', 'حالة الدفع'];
        const rows = reports.map(report => [
            report.driverName,
            report.region,
            report.totalOrders,
            report.completedOrders,
            `${report.successRate.toFixed(1)}%`,
            report.totalRevenue,
            report.commissionAmount,
            report.netRevenue,
            this.getPaymentStatusText(report.paymentStatus)
        ]);

        return [headers, ...rows].map(row => 
            row.map(field => `"${field}"`).join(',')
        ).join('\n');
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    getPaymentStatusText(status) {
        const statuses = {
            'pending': 'معلق',
            'paid': 'مدفوع'
        };
        return statuses[status] || status;
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    exportReports() {
        const reportData = this.generateReportData();
        this.downloadCSV(reportData, `driver_reports_${this.currentPeriod}_${new Date().toISOString().split('T')[0]}.csv`);
        this.showNotification('تم تصدير تقرير المندوبين بنجاح', 'success');
    }

    generateReportData() {
        let csvContent = 'اسم المندوب,عدد الطلبات,المبلغ المستحق,العمولة,الحالة\n';

        this.driverReports.forEach(report => {
            csvContent += `${report.driverName},${report.totalDeliveries},${report.totalEarnings},${report.commissionAmount},${report.status}\n`;
        });

        return csvContent;
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showBulkPayModal() {
        const unpaidDrivers = this.driverReports.filter(report => report.totalEarnings > 0);
        if (unpaidDrivers.length === 0) {
            alert('لا توجد مبالغ مستحقة للمندوبين');
            return;
        }

        const totalAmount = unpaidDrivers.reduce((sum, report) => sum + report.totalEarnings, 0);
        const confirmPay = confirm(`هل تريد دفع جميع المبالغ المستحقة؟\nإجمالي المبلغ: ${this.formatCurrency(totalAmount)}\nعدد المندوبين: ${unpaidDrivers.length}`);

        if (confirmPay) {
            unpaidDrivers.forEach(report => {
                report.totalEarnings = 0;
                report.status = 'paid';
            });
            this.renderReports();
            this.updateOverview();
            this.showNotification(`تم دفع المبالغ لـ ${unpaidDrivers.length} مندوب`, 'success');
        }
    }

    showCommissionSettingsModal() {
        const newCommission = prompt('أدخل نسبة العمولة الجديدة (بالدينار العراقي):');
        if (newCommission && !isNaN(newCommission)) {
            const commission = parseFloat(newCommission);
            this.driverReports.forEach(report => {
                report.commissionAmount = commission;
            });
            this.renderReports();
            this.showNotification(`تم تحديث العمولة إلى ${this.formatCurrency(commission)}`, 'success');
        }
    }

    showPerformanceAnalysis() {
        const analysis = this.calculatePerformanceAnalysis();
        const analysisText = `تحليل أداء المندوبين:\n\nأفضل مندوب: ${analysis.topPerformer.name} (${analysis.topPerformer.deliveries} طلب)\nمتوسط الطلبات: ${analysis.averageDeliveries} طلب\nإجمالي الإيرادات: ${this.formatCurrency(analysis.totalRevenue)}\nالمندوبين النشطين: ${analysis.activeDrivers}`;
        alert(analysisText);
    }

    calculatePerformanceAnalysis() {
        const totalDeliveries = this.driverReports.reduce((sum, report) => sum + report.totalDeliveries, 0);
        const totalRevenue = this.driverReports.reduce((sum, report) => sum + report.totalEarnings, 0);

        const topPerformer = this.driverReports.reduce((top, current) =>
            current.totalDeliveries > top.totalDeliveries ? current : top
        );

        const activeDrivers = this.driverReports.filter(report => report.totalDeliveries > 0).length;

        return {
            topPerformer: {
                name: topPerformer.driverName,
                deliveries: topPerformer.totalDeliveries
            },
            averageDeliveries: Math.round(totalDeliveries / this.driverReports.length),
            totalRevenue,
            activeDrivers
        };
    }

    generatePayslip(driverId) {
        const report = this.driverReports.find(r => r.driverId == driverId);
        if (!report) return;

        const payslipWindow = window.open('', '_blank', 'width=600,height=800');
        payslipWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>قسيمة راتب - ${report.driverName}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                    .payslip-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                    .payslip-details { margin: 20px 0; }
                    .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 5px 0; border-bottom: 1px dotted #ccc; }
                    .total-section { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="payslip-header">
                    <h1>شركة التوصيل العراقية</h1>
                    <h2>قسيمة راتب</h2>
                    <p>الفترة: ${this.currentPeriod} - ${new Date().toLocaleDateString('ar-EG')}</p>
                </div>

                <div class="payslip-details">
                    <div class="detail-row">
                        <span><strong>اسم المندوب:</strong></span>
                        <span>${report.driverName}</span>
                    </div>
                    <div class="detail-row">
                        <span><strong>عدد الطلبات المسلمة:</strong></span>
                        <span>${this.formatNumber(report.totalDeliveries)}</span>
                    </div>
                    <div class="detail-row">
                        <span><strong>العمولة لكل طلب:</strong></span>
                        <span>${this.formatCurrency(report.commissionAmount)}</span>
                    </div>
                </div>

                <div class="total-section">
                    <div class="detail-row" style="font-size: 18px; font-weight: bold;">
                        <span>المبلغ الإجمالي المستحق:</span>
                        <span>${this.formatCurrency(report.totalEarnings)}</span>
                    </div>
                </div>

                <div class="no-print" style="margin-top: 30px; text-align: center;">
                    <button onclick="window.print()">طباعة</button>
                    <button onclick="window.close()">إغلاق</button>
                </div>
            </body>
            </html>
        `);
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Driver Reports Manager
window.DriverReportsManager = new DriverReportsManager();
