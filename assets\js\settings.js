// System Settings Manager
class SettingsManager {
    constructor() {
        this.settings = {};
        this.currentTab = 'company';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSettings();
    }

    setupEventListeners() {
        // Tab switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('.settings-tab') || e.target.closest('.settings-tab')) {
                e.preventDefault();
                const tab = e.target.closest('.settings-tab').getAttribute('data-tab');
                this.switchTab(tab);
            }
            if (e.target.matches('.save-settings-btn') || e.target.closest('.save-settings-btn')) {
                e.preventDefault();
                this.saveSettings();
            }
            if (e.target.matches('.reset-settings-btn') || e.target.closest('.reset-settings-btn')) {
                e.preventDefault();
                this.resetSettings();
            }
            if (e.target.matches('.backup-data-btn') || e.target.closest('.backup-data-btn')) {
                e.preventDefault();
                this.backupData();
            }
            if (e.target.matches('.restore-data-btn') || e.target.closest('.restore-data-btn')) {
                e.preventDefault();
                this.restoreData();
            }
        });

        // Form changes
        document.addEventListener('change', (e) => {
            if (e.target.closest('.settings-form')) {
                this.markAsChanged();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const settingsHTML = `
            <div class="settings-header">
                <div class="settings-title">
                    <h1>الإعدادات العامة</h1>
                    <p>إدارة إعدادات النظام والشركة</p>
                </div>
                <div class="settings-actions">
                    <button class="neu-btn primary save-settings-btn">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <button class="neu-btn secondary reset-settings-btn">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="settings-container">
                <div class="settings-sidebar">
                    <div class="settings-tabs">
                        <button class="settings-tab active" data-tab="company">
                            <i class="fas fa-building"></i>
                            <span>معلومات الشركة</span>
                        </button>
                        <button class="settings-tab" data-tab="system">
                            <i class="fas fa-cog"></i>
                            <span>إعدادات النظام</span>
                        </button>
                        <button class="settings-tab" data-tab="users">
                            <i class="fas fa-users"></i>
                            <span>إدارة المستخدمين</span>
                        </button>
                        <button class="settings-tab" data-tab="notifications">
                            <i class="fas fa-bell"></i>
                            <span>إعدادات الإشعارات</span>
                        </button>
                        <button class="settings-tab" data-tab="security">
                            <i class="fas fa-shield-alt"></i>
                            <span>الأمان والحماية</span>
                        </button>
                        <button class="settings-tab" data-tab="backup">
                            <i class="fas fa-database"></i>
                            <span>النسخ الاحتياطية</span>
                        </button>
                    </div>
                </div>

                <div class="settings-content">
                    <div id="company-settings" class="settings-panel active">
                        <h2>معلومات الشركة</h2>
                        <form class="settings-form">
                            <div class="form-section">
                                <h3>البيانات الأساسية</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="company-name">اسم الشركة:</label>
                                        <input type="text" id="company-name" class="neu-input" value="شركة التوصيل العراقية">
                                    </div>
                                    <div class="form-group">
                                        <label for="company-name-en">اسم الشركة (إنجليزي):</label>
                                        <input type="text" id="company-name-en" class="neu-input" value="Iraqi Delivery Company">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="company-phone">هاتف الشركة:</label>
                                        <input type="tel" id="company-phone" class="neu-input" value="+964-1-234-5678">
                                    </div>
                                    <div class="form-group">
                                        <label for="company-email">البريد الإلكتروني:</label>
                                        <input type="email" id="company-email" class="neu-input" value="<EMAIL>">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="company-address">عنوان الشركة:</label>
                                    <textarea id="company-address" class="neu-input" rows="3">بغداد، العراق - شارع الرشيد، مجمع الأعمال التجاري</textarea>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>الإعدادات المالية</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="default-delivery-fee">رسوم التوصيل الافتراضية (د.ع):</label>
                                        <input type="number" id="default-delivery-fee" class="neu-input" value="5000" min="1000" step="500">
                                    </div>
                                    <div class="form-group">
                                        <label for="default-commission">العمولة الافتراضية للمندوبين (د.ع):</label>
                                        <input type="number" id="default-commission" class="neu-input" value="15000" min="5000" step="1000">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="tax-rate">معدل الضريبة (%):</label>
                                        <input type="number" id="tax-rate" class="neu-input" value="0" min="0" max="30" step="0.5">
                                    </div>
                                    <div class="form-group">
                                        <label for="currency-symbol">رمز العملة:</label>
                                        <input type="text" id="currency-symbol" class="neu-input" value="د.ع" readonly>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div id="system-settings" class="settings-panel">
                        <h2>إعدادات النظام</h2>
                        <form class="settings-form">
                            <div class="form-section">
                                <h3>الإعدادات العامة</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="system-language">لغة النظام:</label>
                                        <select id="system-language" class="neu-select">
                                            <option value="ar" selected>العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="date-format">تنسيق التاريخ:</label>
                                        <select id="date-format" class="neu-select">
                                            <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                                            <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                            <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="time-format">تنسيق الوقت:</label>
                                        <select id="time-format" class="neu-select">
                                            <option value="24" selected>24 ساعة</option>
                                            <option value="12">12 ساعة</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="timezone">المنطقة الزمنية:</label>
                                        <select id="timezone" class="neu-select">
                                            <option value="Asia/Baghdad" selected>بغداد (GMT+3)</option>
                                            <option value="Asia/Kuwait">الكويت (GMT+3)</option>
                                            <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>إعدادات الأداء</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="auto-refresh">التحديث التلقائي:</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="auto-refresh" checked>
                                            <label for="auto-refresh" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="refresh-interval">فترة التحديث (ثانية):</label>
                                        <input type="number" id="refresh-interval" class="neu-input" value="30" min="10" max="300">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="items-per-page">عدد العناصر في الصفحة:</label>
                                        <select id="items-per-page" class="neu-select">
                                            <option value="10" selected>10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="enable-sounds">تفعيل الأصوات:</label>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="enable-sounds" checked>
                                            <label for="enable-sounds" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div id="users-settings" class="settings-panel">
                        <h2>إدارة المستخدمين</h2>
                        <div class="users-management">
                            <div class="users-header">
                                <h3>قائمة المستخدمين</h3>
                                <button class="neu-btn primary add-user-btn">
                                    <i class="fas fa-user-plus"></i>
                                    إضافة مستخدم
                                </button>
                            </div>
                            <div class="users-table-wrapper">
                                <table class="users-table">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>الاسم الكامل</th>
                                            <th>الدور</th>
                                            <th>آخر دخول</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="users-tbody">
                                        <tr>
                                            <td>admin</td>
                                            <td>مدير النظام</td>
                                            <td><span class="role-badge admin">مدير</span></td>
                                            <td>2024-12-27 14:30</td>
                                            <td><span class="status-badge active">نشط</span></td>
                                            <td>
                                                <button class="action-btn edit-user-btn" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn reset-password-btn" title="إعادة تعيين كلمة المرور">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>employee1</td>
                                            <td>موظف الاستقبال</td>
                                            <td><span class="role-badge employee">موظف</span></td>
                                            <td>2024-12-27 12:15</td>
                                            <td><span class="status-badge active">نشط</span></td>
                                            <td>
                                                <button class="action-btn edit-user-btn" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="action-btn reset-password-btn" title="إعادة تعيين كلمة المرور">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                <button class="action-btn deactivate-user-btn" title="إيقاف">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div id="notifications-settings" class="settings-panel">
                        <h2>إعدادات الإشعارات</h2>
                        <form class="settings-form">
                            <div class="form-section">
                                <h3>الإشعارات العامة</h3>
                                <div class="notification-setting">
                                    <div class="setting-info">
                                        <h4>إشعارات الطلبات الجديدة</h4>
                                        <p>إشعار فوري عند استلام طلب جديد</p>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="new-order-notifications" checked>
                                        <label for="new-order-notifications" class="toggle-label"></label>
                                    </div>
                                </div>
                                <div class="notification-setting">
                                    <div class="setting-info">
                                        <h4>إشعارات تغيير حالة الطلب</h4>
                                        <p>إشعار عند تغيير حالة أي طلب</p>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="order-status-notifications" checked>
                                        <label for="order-status-notifications" class="toggle-label"></label>
                                    </div>
                                </div>
                                <div class="notification-setting">
                                    <div class="setting-info">
                                        <h4>إشعارات المدفوعات</h4>
                                        <p>إشعار عند استلام أو إرسال مدفوعات</p>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="payment-notifications" checked>
                                        <label for="payment-notifications" class="toggle-label"></label>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div id="security-settings" class="settings-panel">
                        <h2>الأمان والحماية</h2>
                        <form class="settings-form">
                            <div class="form-section">
                                <h3>إعدادات كلمة المرور</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="min-password-length">الحد الأدنى لطول كلمة المرور:</label>
                                        <input type="number" id="min-password-length" class="neu-input" value="8" min="6" max="20">
                                    </div>
                                    <div class="form-group">
                                        <label for="password-expiry">انتهاء صلاحية كلمة المرور (أيام):</label>
                                        <input type="number" id="password-expiry" class="neu-input" value="90" min="30" max="365">
                                    </div>
                                </div>
                                <div class="security-setting">
                                    <div class="setting-info">
                                        <h4>طلب أحرف خاصة في كلمة المرور</h4>
                                        <p>يجب أن تحتوي كلمة المرور على أحرف خاصة</p>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="require-special-chars" checked>
                                        <label for="require-special-chars" class="toggle-label"></label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h3>إعدادات الجلسة</h3>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="session-timeout">انتهاء الجلسة (دقيقة):</label>
                                        <input type="number" id="session-timeout" class="neu-input" value="60" min="15" max="480">
                                    </div>
                                    <div class="form-group">
                                        <label for="max-login-attempts">محاولات تسجيل الدخول القصوى:</label>
                                        <input type="number" id="max-login-attempts" class="neu-input" value="5" min="3" max="10">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div id="backup-settings" class="settings-panel">
                        <h2>النسخ الاحتياطية</h2>
                        <div class="backup-management">
                            <div class="backup-section">
                                <h3>النسخ الاحتياطية التلقائية</h3>
                                <div class="backup-setting">
                                    <div class="setting-info">
                                        <h4>تفعيل النسخ الاحتياطية التلقائية</h4>
                                        <p>إنشاء نسخة احتياطية تلقائياً بشكل دوري</p>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="auto-backup" checked>
                                        <label for="auto-backup" class="toggle-label"></label>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="backup-frequency">تكرار النسخ الاحتياطية:</label>
                                        <select id="backup-frequency" class="neu-select">
                                            <option value="daily" selected>يومي</option>
                                            <option value="weekly">أسبوعي</option>
                                            <option value="monthly">شهري</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="backup-time">وقت النسخ الاحتياطي:</label>
                                        <input type="time" id="backup-time" class="neu-input" value="02:00">
                                    </div>
                                </div>
                            </div>

                            <div class="backup-actions">
                                <h3>إجراءات النسخ الاحتياطية</h3>
                                <div class="backup-buttons">
                                    <button class="neu-btn primary backup-data-btn">
                                        <i class="fas fa-download"></i>
                                        إنشاء نسخة احتياطية الآن
                                    </button>
                                    <button class="neu-btn secondary restore-data-btn">
                                        <i class="fas fa-upload"></i>
                                        استعادة من نسخة احتياطية
                                    </button>
                                </div>
                                <div class="backup-info">
                                    <p><strong>آخر نسخة احتياطية:</strong> 2024-12-27 02:00:00</p>
                                    <p><strong>حجم النسخة:</strong> 2.5 MB</p>
                                    <p><strong>عدد النسخ المحفوظة:</strong> 7 نسخ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        pageContent.innerHTML = settingsHTML;
        this.loadSettings();
    }

    loadSettings() {
        // Load settings from localStorage or default values
        this.settings = {
            company: {
                name: 'شركة التوصيل العراقية',
                nameEn: 'Iraqi Delivery Company',
                phone: '+964-1-234-5678',
                email: '<EMAIL>',
                address: 'بغداد، العراق - شارع الرشيد، مجمع الأعمال التجاري',
                defaultDeliveryFee: 5000,
                defaultCommission: 15000,
                taxRate: 0,
                currencySymbol: 'د.ع'
            },
            system: {
                language: 'ar',
                dateFormat: 'dd/mm/yyyy',
                timeFormat: '24',
                timezone: 'Asia/Baghdad',
                autoRefresh: true,
                refreshInterval: 30,
                itemsPerPage: 10,
                enableSounds: true
            },
            notifications: {
                newOrderNotifications: true,
                orderStatusNotifications: true,
                paymentNotifications: true
            },
            security: {
                minPasswordLength: 8,
                passwordExpiry: 90,
                requireSpecialChars: true,
                sessionTimeout: 60,
                maxLoginAttempts: 5
            },
            backup: {
                autoBackup: true,
                backupFrequency: 'daily',
                backupTime: '02:00'
            }
        };

        // Load from localStorage if available
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        }

        this.populateForm();
    }

    populateForm() {
        // Populate company settings
        if (document.getElementById('company-name')) {
            document.getElementById('company-name').value = this.settings.company.name;
            document.getElementById('company-name-en').value = this.settings.company.nameEn;
            document.getElementById('company-phone').value = this.settings.company.phone;
            document.getElementById('company-email').value = this.settings.company.email;
            document.getElementById('company-address').value = this.settings.company.address;
            document.getElementById('default-delivery-fee').value = this.settings.company.defaultDeliveryFee;
            document.getElementById('default-commission').value = this.settings.company.defaultCommission;
            document.getElementById('tax-rate').value = this.settings.company.taxRate;
            document.getElementById('currency-symbol').value = this.settings.company.currencySymbol;
        }

        // Populate system settings
        if (document.getElementById('system-language')) {
            document.getElementById('system-language').value = this.settings.system.language;
            document.getElementById('date-format').value = this.settings.system.dateFormat;
            document.getElementById('time-format').value = this.settings.system.timeFormat;
            document.getElementById('timezone').value = this.settings.system.timezone;
            document.getElementById('auto-refresh').checked = this.settings.system.autoRefresh;
            document.getElementById('refresh-interval').value = this.settings.system.refreshInterval;
            document.getElementById('items-per-page').value = this.settings.system.itemsPerPage;
            document.getElementById('enable-sounds').checked = this.settings.system.enableSounds;
        }

        // Populate notification settings
        if (document.getElementById('new-order-notifications')) {
            document.getElementById('new-order-notifications').checked = this.settings.notifications.newOrderNotifications;
            document.getElementById('order-status-notifications').checked = this.settings.notifications.orderStatusNotifications;
            document.getElementById('payment-notifications').checked = this.settings.notifications.paymentNotifications;
        }

        // Populate security settings
        if (document.getElementById('min-password-length')) {
            document.getElementById('min-password-length').value = this.settings.security.minPasswordLength;
            document.getElementById('password-expiry').value = this.settings.security.passwordExpiry;
            document.getElementById('require-special-chars').checked = this.settings.security.requireSpecialChars;
            document.getElementById('session-timeout').value = this.settings.security.sessionTimeout;
            document.getElementById('max-login-attempts').value = this.settings.security.maxLoginAttempts;
        }

        // Populate backup settings
        if (document.getElementById('auto-backup')) {
            document.getElementById('auto-backup').checked = this.settings.backup.autoBackup;
            document.getElementById('backup-frequency').value = this.settings.backup.backupFrequency;
            document.getElementById('backup-time').value = this.settings.backup.backupTime;
        }
    }

    switchTab(tabName) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.settings-tab').forEach(tab => tab.classList.remove('active'));
        document.querySelectorAll('.settings-panel').forEach(panel => panel.classList.remove('active'));

        // Add active class to selected tab and panel
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}-settings`).classList.add('active');

        this.currentTab = tabName;
    }

    saveSettings() {
        // Collect form data
        const formData = this.collectFormData();

        // Validate data
        if (!this.validateSettings(formData)) {
            return;
        }

        // Update settings object
        this.settings = { ...this.settings, ...formData };

        // Save to localStorage
        localStorage.setItem('systemSettings', JSON.stringify(this.settings));

        // Apply settings to system
        this.applySettings();

        this.showNotification('تم حفظ الإعدادات بنجاح', 'success');
        this.markAsSaved();
    }

    collectFormData() {
        const formData = {
            company: {},
            system: {},
            notifications: {},
            security: {},
            backup: {}
        };

        // Collect company data
        if (document.getElementById('company-name')) {
            formData.company = {
                name: document.getElementById('company-name').value,
                nameEn: document.getElementById('company-name-en').value,
                phone: document.getElementById('company-phone').value,
                email: document.getElementById('company-email').value,
                address: document.getElementById('company-address').value,
                defaultDeliveryFee: parseInt(document.getElementById('default-delivery-fee').value),
                defaultCommission: parseInt(document.getElementById('default-commission').value),
                taxRate: parseFloat(document.getElementById('tax-rate').value),
                currencySymbol: document.getElementById('currency-symbol').value
            };
        }

        return formData;
    }

    validateSettings(formData) {
        // Validate company settings
        if (formData.company) {
            if (!formData.company.name || formData.company.name.trim() === '') {
                this.showNotification('اسم الشركة مطلوب', 'error');
                return false;
            }
            if (!formData.company.email || !this.isValidEmail(formData.company.email)) {
                this.showNotification('البريد الإلكتروني غير صحيح', 'error');
                return false;
            }
            if (formData.company.defaultDeliveryFee < 1000) {
                this.showNotification('رسوم التوصيل يجب أن تكون 1000 د.ع على الأقل', 'error');
                return false;
            }
        }

        return true;
    }

    applySettings() {
        // Apply settings to the current system
        if (this.settings.system.autoRefresh && window.DashboardManager) {
            window.DashboardManager.toggleAutoRefresh(true);
        }

        // Update page title with company name
        document.title = `${this.settings.company.name} - نظام إدارة التوصيل`;
    }

    resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
            localStorage.removeItem('systemSettings');
            this.loadSettings();
            this.showNotification('تم إعادة تعيين الإعدادات', 'success');
        }
    }

    backupData() {
        const backupData = {
            settings: this.settings,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification('تم إنشاء النسخة الاحتياطية', 'success');
    }

    restoreData() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const backupData = JSON.parse(e.target.result);
                        if (backupData.settings) {
                            this.settings = backupData.settings;
                            localStorage.setItem('systemSettings', JSON.stringify(this.settings));
                            this.populateForm();
                            this.showNotification('تم استعادة البيانات بنجاح', 'success');
                        } else {
                            this.showNotification('ملف النسخة الاحتياطية غير صحيح', 'error');
                        }
                    } catch (error) {
                        this.showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    markAsChanged() {
        const saveBtn = document.querySelector('.save-settings-btn');
        if (saveBtn) {
            saveBtn.classList.add('changed');
            saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات *';
        }
    }

    markAsSaved() {
        const saveBtn = document.querySelector('.save-settings-btn');
        if (saveBtn) {
            saveBtn.classList.remove('changed');
            saveBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Settings Manager
window.SettingsManager = new SettingsManager();
