// Comprehensive Reports System
class ReportsManager {
    constructor() {
        this.currentPeriod = 'daily';
        this.currentDate = new Date();
        this.reportData = {};
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Period filter
        document.addEventListener('change', (e) => {
            if (e.target.id === 'report-period') {
                this.currentPeriod = e.target.value;
                this.updateReports();
            }
            if (e.target.id === 'report-date') {
                this.currentDate = new Date(e.target.value);
                this.updateReports();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-pdf-btn') || e.target.closest('.export-pdf-btn')) {
                e.preventDefault();
                this.exportToPDF();
            }
            if (e.target.matches('.export-excel-btn') || e.target.closest('.export-excel-btn')) {
                e.preventDefault();
                this.exportToExcel();
            }
            if (e.target.matches('.export-csv-btn') || e.target.closest('.export-csv-btn')) {
                e.preventDefault();
                this.exportToCSV();
            }
            if (e.target.matches('.print-report-btn') || e.target.closest('.print-report-btn')) {
                e.preventDefault();
                this.printReport();
            }
            if (e.target.matches('.refresh-reports-btn') || e.target.closest('.refresh-reports-btn')) {
                e.preventDefault();
                this.refreshReports();
            }
            if (e.target.matches('.custom-report-btn') || e.target.closest('.custom-report-btn')) {
                e.preventDefault();
                this.showCustomReportModal();
            }
            if (e.target.matches('.schedule-report-btn') || e.target.closest('.schedule-report-btn')) {
                e.preventDefault();
                this.showScheduleReportModal();
            }
            if (e.target.matches('.compare-periods-btn') || e.target.closest('.compare-periods-btn')) {
                e.preventDefault();
                this.showComparePeriods();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const reportsHTML = `
            <div class="reports-header">
                <div class="reports-title">
                    <h1>التقارير الشاملة</h1>
                    <p>تقارير مفصلة لجميع عمليات الشركة</p>
                </div>
                <div class="reports-actions">
                    <button class="neu-btn primary export-pdf-btn">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                    <button class="neu-btn secondary export-excel-btn">
                        <i class="fas fa-file-excel"></i>
                        تصدير Excel
                    </button>
                    <button class="neu-btn success export-csv-btn">
                        <i class="fas fa-file-csv"></i>
                        تصدير CSV
                    </button>
                    <button class="neu-btn warning custom-report-btn">
                        <i class="fas fa-cog"></i>
                        تقرير مخصص
                    </button>
                    <button class="neu-btn info schedule-report-btn">
                        <i class="fas fa-clock"></i>
                        جدولة التقرير
                    </button>
                    <button class="neu-btn compare-periods-btn">
                        <i class="fas fa-chart-line"></i>
                        مقارنة الفترات
                    </button>
                    <button class="neu-btn print-report-btn">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                    <button class="neu-btn refresh-reports-btn">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <div class="reports-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>نوع التقرير:</label>
                        <select id="report-period" class="neu-select">
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly" selected>شهري</option>
                            <option value="quarterly">ربع سنوي</option>
                            <option value="yearly">سنوي</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>التاريخ:</label>
                        <input type="date" id="report-date" class="neu-input" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                    <div class="filter-group">
                        <label>المنطقة:</label>
                        <select id="region-filter" class="neu-select">
                            <option value="">جميع المناطق</option>
                            <option value="1">بغداد - الكرخ</option>
                            <option value="2">بغداد - الرصافة</option>
                            <option value="3">بغداد - الصدر</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="reports-overview">
                <div class="overview-card total-orders">
                    <div class="overview-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="report-total-orders">0</h3>
                        <p>إجمالي الطلبات</p>
                        <span class="trend positive" id="orders-trend">+12%</span>
                    </div>
                </div>
                <div class="overview-card total-revenue">
                    <div class="overview-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="report-total-revenue">0 د.ع</h3>
                        <p>إجمالي الإيرادات</p>
                        <span class="trend positive" id="revenue-trend">+8%</span>
                    </div>
                </div>
                <div class="overview-card success-rate">
                    <div class="overview-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="report-success-rate">0%</h3>
                        <p>معدل النجاح</p>
                        <span class="trend positive" id="success-trend">+3%</span>
                    </div>
                </div>
                <div class="overview-card avg-delivery-time">
                    <div class="overview-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="overview-info">
                        <h3 id="report-avg-time">0</h3>
                        <p>متوسط وقت التوصيل</p>
                        <span class="trend negative" id="time-trend">-15 دقيقة</span>
                    </div>
                </div>
            </div>

            <div class="reports-charts">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>الطلبات والإيرادات</h3>
                        <div class="chart-legend">
                            <span class="legend-item orders"><span class="color-box"></span>الطلبات</span>
                            <span class="legend-item revenue"><span class="color-box"></span>الإيرادات</span>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="orders-revenue-chart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>توزيع الطلبات حسب الحالة</h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="orders-status-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="reports-details">
                <div class="detail-section">
                    <h3>تقرير الطلبات المفصل</h3>
                    <div class="detail-table-wrapper">
                        <table class="detail-table">
                            <thead>
                                <tr>
                                    <th>الفترة</th>
                                    <th>عدد الطلبات</th>
                                    <th>طلبات مسلمة</th>
                                    <th>طلبات معلقة</th>
                                    <th>طلبات مؤجلة</th>
                                    <th>معدل النجاح</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody id="orders-detail-tbody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="detail-section">
                    <h3>تقرير أداء المندوبين</h3>
                    <div class="detail-table-wrapper">
                        <table class="detail-table">
                            <thead>
                                <tr>
                                    <th>المندوب</th>
                                    <th>عدد الطلبات</th>
                                    <th>طلبات مكتملة</th>
                                    <th>معدل النجاح</th>
                                    <th>متوسط وقت التوصيل</th>
                                    <th>التقييم</th>
                                    <th>العمولة المستحقة</th>
                                </tr>
                            </thead>
                            <tbody id="drivers-detail-tbody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="detail-section">
                    <h3>تقرير المناطق</h3>
                    <div class="detail-table-wrapper">
                        <table class="detail-table">
                            <thead>
                                <tr>
                                    <th>المنطقة</th>
                                    <th>عدد الطلبات</th>
                                    <th>الإيرادات</th>
                                    <th>متوسط رسوم التوصيل</th>
                                    <th>عدد المندوبين</th>
                                    <th>معدل النجاح</th>
                                </tr>
                            </thead>
                            <tbody id="regions-detail-tbody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        pageContent.innerHTML = reportsHTML;
        this.updateReports();
        this.initializeCharts();
    }

    loadSampleData() {
        this.reportData = {
            daily: {
                overview: {
                    totalOrders: 45,
                    totalRevenue: 2250000,
                    successRate: 91.1,
                    avgDeliveryTime: '45 دقيقة'
                },
                ordersDetail: [
                    { period: 'اليوم', total: 45, delivered: 41, pending: 3, postponed: 1, successRate: 91.1, revenue: 2250000 }
                ],
                driversDetail: [
                    { name: 'أحمد محمد', orders: 12, completed: 11, successRate: 91.7, avgTime: '42 دقيقة', rating: 4.8, commission: 165000 },
                    { name: 'محمد علي', orders: 10, completed: 9, successRate: 90.0, avgTime: '48 دقيقة', rating: 4.6, commission: 135000 }
                ],
                regionsDetail: [
                    { name: 'بغداد - الكرخ', orders: 18, revenue: 900000, avgFee: 5000, drivers: 3, successRate: 94.4 },
                    { name: 'بغداد - الرصافة', orders: 15, revenue: 750000, avgFee: 5000, drivers: 2, successRate: 86.7 }
                ]
            },
            monthly: {
                overview: {
                    totalOrders: 1247,
                    totalRevenue: 62350000,
                    successRate: 87.3,
                    avgDeliveryTime: '52 دقيقة'
                },
                ordersDetail: [
                    { period: 'ديسمبر 2024', total: 1247, delivered: 1089, pending: 135, postponed: 23, successRate: 87.3, revenue: 62350000 },
                    { period: 'نوفمبر 2024', total: 1156, delivered: 1012, pending: 98, postponed: 46, successRate: 87.5, revenue: 57800000 },
                    { period: 'أكتوبر 2024', total: 1089, delivered: 945, pending: 112, postponed: 32, successRate: 86.8, revenue: 54450000 }
                ],
                driversDetail: [
                    { name: 'أحمد محمد', orders: 245, completed: 230, successRate: 93.9, avgTime: '45 دقيقة', rating: 4.8, commission: 3450000 },
                    { name: 'محمد علي', orders: 189, completed: 175, successRate: 92.6, avgTime: '48 دقيقة', rating: 4.6, commission: 2625000 },
                    { name: 'عبدالله حسن', orders: 198, completed: 182, successRate: 91.9, avgTime: '50 دقيقة', rating: 4.7, commission: 2730000 }
                ],
                regionsDetail: [
                    { name: 'بغداد - الكرخ', orders: 456, revenue: 22800000, avgFee: 5000, drivers: 3, successRate: 89.5 },
                    { name: 'بغداد - الرصافة', orders: 398, revenue: 19900000, avgFee: 5000, drivers: 2, successRate: 85.2 },
                    { name: 'بغداد - الصدر', orders: 393, revenue: 19650000, avgFee: 5000, drivers: 2, successRate: 87.0 }
                ]
            },
            yearly: {
                overview: {
                    totalOrders: 14567,
                    totalRevenue: 728350000,
                    successRate: 86.8,
                    avgDeliveryTime: '54 دقيقة'
                },
                ordersDetail: [
                    { period: '2024', total: 14567, delivered: 12644, pending: 1456, postponed: 467, successRate: 86.8, revenue: 728350000 },
                    { period: '2023', total: 12890, delivered: 11234, pending: 1234, postponed: 422, successRate: 87.2, revenue: 644500000 }
                ],
                driversDetail: [
                    { name: 'أحمد محمد', orders: 2940, completed: 2756, successRate: 93.7, avgTime: '46 دقيقة', rating: 4.8, commission: 41340000 },
                    { name: 'محمد علي', orders: 2267, completed: 2098, successRate: 92.5, avgTime: '49 دقيقة', rating: 4.6, commission: 31470000 },
                    { name: 'عبدالله حسن', orders: 2378, completed: 2184, successRate: 91.8, avgTime: '51 دقيقة', rating: 4.7, commission: 32760000 }
                ],
                regionsDetail: [
                    { name: 'بغداد - الكرخ', orders: 5327, revenue: 266350000, avgFee: 5000, drivers: 3, successRate: 88.9 },
                    { name: 'بغداد - الرصافة', orders: 4654, revenue: 232700000, avgFee: 5000, drivers: 2, successRate: 84.7 },
                    { name: 'بغداد - الصدر', orders: 4586, revenue: 229300000, avgFee: 5000, drivers: 2, successRate: 86.5 }
                ]
            }
        };
    }

    updateReports() {
        const data = this.reportData[this.currentPeriod];
        if (!data) return;

        // Update overview cards
        document.getElementById('report-total-orders').textContent = this.formatNumber(data.overview.totalOrders);
        document.getElementById('report-total-revenue').textContent = this.formatCurrency(data.overview.totalRevenue);
        document.getElementById('report-success-rate').textContent = this.formatNumber(data.overview.successRate) + '%';
        document.getElementById('report-avg-time').textContent = data.overview.avgDeliveryTime;

        // Update detail tables
        this.updateOrdersDetailTable(data.ordersDetail);
        this.updateDriversDetailTable(data.driversDetail);
        this.updateRegionsDetailTable(data.regionsDetail);

        // Update charts
        this.updateCharts(data);
    }

    updateOrdersDetailTable(data) {
        const tbody = document.getElementById('orders-detail-tbody');
        if (!tbody) return;

        tbody.innerHTML = data.map(row => `
            <tr>
                <td>${row.period}</td>
                <td>${this.formatNumber(row.total)}</td>
                <td>${this.formatNumber(row.delivered)}</td>
                <td>${this.formatNumber(row.pending)}</td>
                <td>${this.formatNumber(row.postponed)}</td>
                <td>${this.formatNumber(row.successRate)}%</td>
                <td>${this.formatCurrency(row.revenue)}</td>
            </tr>
        `).join('');
    }

    updateDriversDetailTable(data) {
        const tbody = document.getElementById('drivers-detail-tbody');
        if (!tbody) return;

        tbody.innerHTML = data.map(row => `
            <tr>
                <td>${row.name}</td>
                <td>${this.formatNumber(row.orders)}</td>
                <td>${this.formatNumber(row.completed)}</td>
                <td>${this.formatNumber(row.successRate)}%</td>
                <td>${row.avgTime}</td>
                <td>
                    <div class="rating-display">
                        ${this.renderStars(row.rating)}
                        <span>${this.formatNumber(row.rating)}</span>
                    </div>
                </td>
                <td>${this.formatCurrency(row.commission)}</td>
            </tr>
        `).join('');
    }

    updateRegionsDetailTable(data) {
        const tbody = document.getElementById('regions-detail-tbody');
        if (!tbody) return;

        tbody.innerHTML = data.map(row => `
            <tr>
                <td>${row.name}</td>
                <td>${this.formatNumber(row.orders)}</td>
                <td>${this.formatCurrency(row.revenue)}</td>
                <td>${this.formatCurrency(row.avgFee)}</td>
                <td>${this.formatNumber(row.drivers)}</td>
                <td>${this.formatNumber(row.successRate)}%</td>
            </tr>
        `).join('');
    }

    initializeCharts() {
        // Initialize charts with Chart.js (mock implementation)
        this.createOrdersRevenueChart();
        this.createOrdersStatusChart();
    }

    createOrdersRevenueChart() {
        const canvas = document.getElementById('orders-revenue-chart');
        if (!canvas) return;

        // Mock chart implementation
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#3498db';
        ctx.fillRect(50, 50, 100, 100);
        ctx.fillStyle = '#e74c3c';
        ctx.fillRect(200, 75, 100, 75);

        // Add labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = '14px Arial';
        ctx.fillText('الطلبات', 70, 170);
        ctx.fillText('الإيرادات', 220, 170);
    }

    createOrdersStatusChart() {
        const canvas = document.getElementById('orders-status-chart');
        if (!canvas) return;

        // Mock pie chart implementation
        const ctx = canvas.getContext('2d');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = 80;

        // Draw pie slices
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 1.4);
        ctx.lineTo(centerX, centerY);
        ctx.fillStyle = '#27ae60';
        ctx.fill();

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, Math.PI * 1.4, Math.PI * 1.8);
        ctx.lineTo(centerX, centerY);
        ctx.fillStyle = '#f39c12';
        ctx.fill();

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, Math.PI * 1.8, Math.PI * 2);
        ctx.lineTo(centerX, centerY);
        ctx.fillStyle = '#e74c3c';
        ctx.fill();
    }

    updateCharts(data) {
        // Update charts with new data
        this.createOrdersRevenueChart();
        this.createOrdersStatusChart();
    }

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        let starsHTML = '';

        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }

        if (hasHalfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }

        return starsHTML;
    }

    refreshReports() {
        this.updateReports();
        this.showNotification('تم تحديث التقارير بنجاح', 'success');
    }

    exportToPDF() {
        this.showNotification('سيتم إضافة تصدير PDF قريباً', 'info');
    }

    exportToExcel() {
        // Generate CSV data
        const csvData = this.generateCSVData();
        this.downloadCSV(csvData, `report_${this.currentPeriod}_${new Date().toISOString().split('T')[0]}.csv`);
    }

    generateCSVData() {
        const data = this.reportData[this.currentPeriod];
        let csvContent = 'التقرير الشامل - ' + this.currentPeriod + '\n\n';

        // Overview section
        csvContent += 'الملخص العام\n';
        csvContent += 'إجمالي الطلبات,' + data.overview.totalOrders + '\n';
        csvContent += 'إجمالي الإيرادات,' + data.overview.totalRevenue + '\n';
        csvContent += 'معدل النجاح,' + data.overview.successRate + '%\n';
        csvContent += 'متوسط وقت التوصيل,' + data.overview.avgDeliveryTime + '\n\n';

        // Orders detail
        csvContent += 'تفاصيل الطلبات\n';
        csvContent += 'الفترة,إجمالي الطلبات,طلبات مسلمة,طلبات معلقة,طلبات مؤجلة,معدل النجاح,الإيرادات\n';
        data.ordersDetail.forEach(row => {
            csvContent += `${row.period},${row.total},${row.delivered},${row.pending},${row.postponed},${row.successRate}%,${row.revenue}\n`;
        });

        return csvContent;
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    printReport() {
        window.print();
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    exportToCSV() {
        const reportData = this.generateReportData();
        let csvContent = 'التاريخ,الطلبات,الإيرادات,المصروفات,صافي الربح\n';

        reportData.forEach(row => {
            csvContent += `${row.date},${row.orders},${row.revenue},${row.expenses},${row.profit}\n`;
        });

        this.downloadFile(csvContent, `report_${this.currentPeriod}_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
        this.showNotification('تم تصدير التقرير كملف CSV', 'success');
    }

    showCustomReportModal() {
        if (window.ModalManager) {
            window.ModalManager.showCustomReportModal();
        } else {
            alert('سيتم إضافة نافذة التقرير المخصص قريباً');
        }
    }

    showScheduleReportModal() {
        if (window.ModalManager) {
            window.ModalManager.showScheduleReportModal();
        } else {
            alert('سيتم إضافة نافذة جدولة التقرير قريباً');
        }
    }

    showComparePeriods() {
        const currentData = this.reportData[this.currentPeriod];
        const previousData = this.getPreviousPeriodData();

        if (!previousData) {
            alert('لا توجد بيانات للفترة السابقة للمقارنة');
            return;
        }

        const comparison = this.calculateComparison(currentData, previousData);
        this.displayComparison(comparison);
    }

    getPreviousPeriodData() {
        // Simulate previous period data
        const current = this.reportData[this.currentPeriod];
        return {
            totalOrders: Math.floor(current.totalOrders * 0.85),
            totalRevenue: Math.floor(current.totalRevenue * 0.9),
            totalExpenses: Math.floor(current.totalExpenses * 0.95),
            netProfit: Math.floor(current.netProfit * 0.8)
        };
    }

    calculateComparison(current, previous) {
        return {
            orders: {
                current: current.totalOrders,
                previous: previous.totalOrders,
                change: ((current.totalOrders - previous.totalOrders) / previous.totalOrders * 100).toFixed(1)
            },
            revenue: {
                current: current.totalRevenue,
                previous: previous.totalRevenue,
                change: ((current.totalRevenue - previous.totalRevenue) / previous.totalRevenue * 100).toFixed(1)
            },
            profit: {
                current: current.netProfit,
                previous: previous.netProfit,
                change: ((current.netProfit - previous.netProfit) / previous.netProfit * 100).toFixed(1)
            }
        };
    }

    displayComparison(comparison) {
        const comparisonText = `مقارنة الفترات:\n\nالطلبات: ${this.formatNumber(comparison.orders.current)} (${comparison.orders.change}%)\nالإيرادات: ${this.formatCurrency(comparison.revenue.current)} (${comparison.revenue.change}%)\nصافي الربح: ${this.formatCurrency(comparison.profit.current)} (${comparison.profit.change}%)`;
        alert(comparisonText);
    }

    generateReportData() {
        const data = [];
        const days = this.currentPeriod === 'daily' ? 1 : this.currentPeriod === 'weekly' ? 7 : 30;

        for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - i);

            data.push({
                date: date.toISOString().split('T')[0],
                orders: Math.floor(Math.random() * 50) + 10,
                revenue: Math.floor(Math.random() * 500000) + 100000,
                expenses: Math.floor(Math.random() * 200000) + 50000,
                profit: Math.floor(Math.random() * 300000) + 50000
            });
        }

        return data.reverse();
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Reports Manager
window.ReportsManager = new ReportsManager();
