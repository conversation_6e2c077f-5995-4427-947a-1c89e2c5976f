# دليل البناء السريع - نظام إدارة شركة التوصيل العراقية
## Quick Build Guide - Iraqi Delivery Management System

---

## 🚀 **البناء السريع في 5 دقائق**

### **الخطوة 1: تثبيت Node.js**
```bash
# تحميل من: https://nodejs.org/
# اختر النسخة LTS (الموصى بها)
node --version  # للتحقق من التثبيت
npm --version   # للتحقق من npm
```

### **الخطوة 2: تثبيت التبعيات**
```bash
# في مجلد المشروع
npm install
```

### **الخطوة 3: بناء التطبيق**
```bash
# بناء لـ Windows (الأكثر شيوعاً)
npm run build-win

# أو بناء لجميع الأنظمة
npm run build
```

### **الخطوة 4: العثور على الملفات**
```
dist/
├── نظام إدارة شركة التوصيل العراقية Setup 1.0.0.exe  ← ملف التثبيت
├── نظام إدارة شركة التوصيل العراقية 1.0.0.exe        ← النسخة المحمولة
```

---

## ⚡ **أوامر سريعة**

```bash
# تشغيل في وضع التطوير
npm start

# بناء Windows فقط
npm run build-win

# بناء macOS فقط
npm run build-mac

# بناء Linux فقط
npm run build-linux

# بناء جميع الأنظمة
npm run build

# تنظيف وإعادة بناء
npm run clean && npm run build
```

---

## 🛠️ **استكشاف الأخطاء الشائعة**

### **خطأ: "npm command not found"**
```bash
# الحل: تثبيت Node.js من https://nodejs.org/
# ثم إعادة تشغيل Command Prompt
```

### **خطأ: "electron-builder not found"**
```bash
# الحل: تثبيت التبعيات
npm install
# أو تثبيت عالمياً
npm install -g electron-builder
```

### **خطأ في البناء: "ENOSPC"**
```bash
# الحل: زيادة مساحة المراقبة (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### **خطأ: "Certificate not found"**
```bash
# الحل: تعطيل توقيع الكود مؤقتاً
# في package.json، أضف:
"win": {
  "certificateFile": null,
  "certificatePassword": null
}
```

---

## 📁 **هيكل الملفات المطلوبة**

```
المشروع/
├── main.js              ← ملف التطبيق الرئيسي
├── preload.js           ← ملف الأمان
├── package.json         ← إعدادات المشروع
├── splash.html          ← شاشة البداية
├── index.html           ← الصفحة الرئيسية
├── installer.nsh        ← إعدادات المثبت
├── assets/              ← الملفات الثابتة
│   ├── css/            ← ملفات التنسيق
│   ├── js/             ← ملفات JavaScript
│   ├── icons/          ← أيقونات التطبيق
│   └── images/         ← الصور
└── dist/               ← ملفات البناء (تُنشأ تلقائياً)
```

---

## 🎯 **إعدادات البناء السريعة**

### **للبناء السريع (Windows فقط):**
```json
{
  "build": {
    "win": {
      "target": "nsis"
    }
  }
}
```

### **للبناء الكامل:**
```json
{
  "build": {
    "win": {
      "target": ["nsis", "portable"]
    },
    "mac": {
      "target": "dmg"
    },
    "linux": {
      "target": ["AppImage", "deb"]
    }
  }
}
```

---

## 📦 **أحجام الملفات المتوقعة**

| النظام | نوع الملف | الحجم التقريبي |
|--------|-----------|----------------|
| Windows | Setup.exe | ~150 MB |
| Windows | Portable.exe | ~180 MB |
| macOS | .dmg | ~120 MB |
| Linux | .AppImage | ~130 MB |
| Linux | .deb | ~120 MB |

---

## ⏱️ **أوقات البناء المتوقعة**

| النظام | الوقت (جهاز متوسط) |
|--------|-------------------|
| Windows فقط | 2-3 دقائق |
| جميع الأنظمة | 8-12 دقيقة |
| أول بناء | 15-20 دقيقة |

---

## 🔧 **تحسين سرعة البناء**

### **1. استخدام cache:**
```bash
# تفعيل cache
npm config set cache-min 86400
```

### **2. بناء متوازي:**
```json
{
  "build": {
    "buildDependenciesFromSource": false,
    "nodeGypRebuild": false
  }
}
```

### **3. استبعاد ملفات غير ضرورية:**
```json
{
  "build": {
    "files": [
      "**/*",
      "!node_modules/**/*",
      "!dist/**/*",
      "!*.md",
      "!.git/**/*"
    ]
  }
}
```

---

## 🎨 **تخصيص الأيقونات**

### **إنشاء الأيقونات:**
1. **Windows:** ملف ICO بحجم 256x256
2. **macOS:** ملف ICNS بحجم 512x512  
3. **Linux:** ملف PNG بحجم 512x512

### **أدوات مفيدة:**
- **ICO:** https://icoconvert.com/
- **ICNS:** https://iconverticons.com/
- **PNG:** أي محرر صور

---

## 📋 **قائمة التحقق قبل البناء**

- [ ] ✅ تم تثبيت Node.js
- [ ] ✅ تم تشغيل `npm install`
- [ ] ✅ تم اختبار التطبيق (`npm start`)
- [ ] ✅ تم إضافة الأيقونات المطلوبة
- [ ] ✅ تم تحديث معلومات package.json
- [ ] ✅ تم فحص المساحة المتاحة (2GB+)
- [ ] ✅ تم إغلاق برامج مكافحة الفيروسات مؤقتاً

---

## 🚀 **بناء للإنتاج**

### **إعدادات الإنتاج:**
```json
{
  "build": {
    "compression": "maximum",
    "asar": true,
    "publish": {
      "provider": "github"
    }
  }
}
```

### **توقيع الكود (للنشر التجاري):**
```json
{
  "build": {
    "win": {
      "certificateFile": "path/to/certificate.p12",
      "certificatePassword": "password"
    }
  }
}
```

---

## 📞 **الحصول على المساعدة**

إذا واجهت أي مشاكل:

1. **تحقق من الوثائق:** https://www.electron.build/
2. **GitHub Issues:** https://github.com/electron-userland/electron-builder/issues
3. **الدعم المباشر:** <EMAIL>

---

## 🎉 **تهانينا!**

بعد اتباع هذه الخطوات، ستحصل على:

- ✅ **ملف exe قابل للتشغيل** على Windows
- ✅ **مثبت احترافي** مع شعار الشركة
- ✅ **نسخة محمولة** لا تحتاج تثبيت
- ✅ **تطبيق جاهز للتوزيع** على العملاء

---

**🚀 استمتع بتطبيق سطح المكتب الجديد! 🚀**
