<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة التوصيل العراقية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <link rel="stylesheet" href="assets/css/design-fixes.css">
    <link rel="stylesheet" href="assets/css/dashboard-fixes.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>نظام إدارة شركة التوصيل</h2>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Login Page -->
    <div id="login-page" class="login-page">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="logo">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h1>نظام إدارة التوصيل</h1>
                    <p>مرحباً بك في نظام إدارة شركة التوصيل</p>
                </div>
                
                <form id="login-form" class="login-form">
                    <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="username" name="username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="user-type">نوع المستخدم</label>
                        <div class="select-group">
                            <select id="user-type" name="user_type" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="admin">مدير</option>
                                <option value="employee">موظف</option>
                                <option value="driver">مندوب</option>
                                <option value="company">شركة</option>
                            </select>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember-me">
                            <span class="checkmark"></span>
                            تذكرني
                        </label>
                        <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                    </div>
                    
                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="login-footer">
                    <p>&copy; 2024 نظام إدارة شركة التوصيل. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="dashboard hidden">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-truck"></i>
                    <span>نظام التوصيل</span>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item active">
                        <a href="#" data-page="dashboard" class="nav-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="orders" class="nav-link">
                            <i class="fas fa-box"></i>
                            <span>إدارة الطلبات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="postponed-orders" class="nav-link">
                            <i class="fas fa-clock"></i>
                            <span>الطلبات المؤجلة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="drivers" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span>إدارة المندوبين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="customers" class="nav-link">
                            <i class="fas fa-user-friends"></i>
                            <span>إدارة العملاء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="finance" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span>الإدارة المالية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="receipt-barcode-manager.html" target="_blank" class="nav-link">
                            <i class="fas fa-receipt"></i>
                            <span>إدارة أرقام الوصل</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="tracking" class="nav-link">
                            <i class="fas fa-route"></i>
                            <span>تتبع الطلبات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="regions" class="nav-link">
                            <i class="fas fa-map"></i>
                            <span>إدارة المناطق</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="billing" class="nav-link">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>نظام الفواتير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="returns" class="nav-link">
                            <i class="fas fa-undo"></i>
                            <span>المرتجعات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="notifications" class="nav-link">
                            <i class="fas fa-bell"></i>
                            <span>الإشعارات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="settings" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" data-page="reports" class="nav-link">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="current-user-name">المدير</span>
                        <span class="user-role" id="current-user-role">مدير النظام</span>
                    </div>
                </div>
                <button class="logout-btn" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">لوحة التحكم</h1>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="header-btn notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        
                        <button class="header-btn search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-menu-toggle">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="user-menu-dropdown">
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-user"></i>
                                    الملف الشخصي
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    الإعدادات
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="#" class="dropdown-item logout-item">
                                    <i class="fas fa-sign-out-alt"></i>
                                    تسجيل الخروج
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content" id="page-content">
                <!-- Dashboard content will be loaded here -->
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/js/ui-fixes.js"></script>
    <script src="assets/js/app.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/activity-logger.js"></script>
    <script src="assets/js/barcode-generator.js"></script>
    <script src="assets/js/modals.js"></script>
    <script src="assets/js/orders.js"></script>
    <script src="assets/js/drivers.js"></script>
    <script src="assets/js/customers.js"></script>
    <script src="assets/js/finance.js"></script>
    <script src="assets/js/tracking.js"></script>
    <script src="assets/js/regions.js"></script>
    <script src="assets/js/billing.js"></script>
    <script src="assets/js/driver-reports.js"></script>
    <script src="assets/js/notifications.js"></script>
    <script src="assets/js/returns.js"></script>
    <script src="assets/js/settings.js"></script>
    <script src="assets/js/reports.js"></script>
    <script src="assets/js/postponed-orders.js"></script>
    <script src="assets/js/receipt-system.js"></script>
    <script src="assets/js/advanced-barcode.js"></script>
    <script src="assets/js/receipt-api-client.js"></script>
    <script src="assets/js/advanced-receipt-features.js"></script>

    <!-- Electron Desktop Integration -->
    <script>
        // فحص إذا كان التطبيق يعمل في Electron
        if (typeof window.electronAPI !== 'undefined') {
            console.log('Running in Electron Desktop App');

            // إضافة ميزات سطح المكتب
            document.addEventListener('DOMContentLoaded', function() {
                // تحسين الطباعة لسطح المكتب
                if (window.printAPI) {
                    const originalPrint = window.print;
                    window.print = function() {
                        window.printAPI.printPage();
                    };
                }

                // إضافة اختصارات لوحة المفاتيح
                document.addEventListener('keydown', function(e) {
                    // Ctrl+S للحفظ
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        saveAllData();
                    }

                    // Ctrl+P للطباعة
                    if (e.ctrlKey && e.key === 'p') {
                        e.preventDefault();
                        window.print();
                    }

                    // F5 لإعادة التحميل
                    if (e.key === 'F5') {
                        e.preventDefault();
                        location.reload();
                    }
                });
            });

            // حفظ جميع البيانات
            async function saveAllData() {
                if (window.deliverySystemAPI) {
                    try {
                        const allData = {
                            orders: JSON.parse(localStorage.getItem('orders') || '[]'),
                            drivers: JSON.parse(localStorage.getItem('drivers') || '[]'),
                            customers: JSON.parse(localStorage.getItem('customers') || '[]'),
                            regions: JSON.parse(localStorage.getItem('regions') || '[]'),
                            settings: JSON.parse(localStorage.getItem('settings') || '{}')
                        };

                        const result = await window.deliverySystemAPI.exportData(allData, 'system_data');
                        if (result.success) {
                            showNotification('تم حفظ البيانات بنجاح', 'success');
                        }
                    } catch (error) {
                        showNotification('فشل في حفظ البيانات: ' + error.message, 'error');
                    }
                }
            }

            // تحسين الإشعارات لسطح المكتب
            const originalShowNotification = window.showNotification || function() {};
            window.showNotification = function(message, type = 'info') {
                // إظهار إشعار النظام
                if (window.deliverySystemAPI && window.deliverySystemAPI.showNotification) {
                    window.deliverySystemAPI.showNotification('نظام إدارة التوصيل', message);
                }

                // إظهار الإشعار في التطبيق
                originalShowNotification(message, type);
            };
        } else {
            console.log('Running in Web Browser');
        }
    </script>
</body>
</html>
