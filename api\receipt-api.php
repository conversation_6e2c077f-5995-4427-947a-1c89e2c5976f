<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration
require_once 'config.php';

class ReceiptAPI {
    private $conn;
    private $table_name = "receipt_numbers";
    
    public function __construct($db) {
        $this->conn = $db;
        $this->createTableIfNotExists();
    }
    
    private function createTableIfNotExists() {
        $query = "CREATE TABLE IF NOT EXISTS " . $this->table_name . " (
            id INT AUTO_INCREMENT PRIMARY KEY,
            receipt_number VARCHAR(20) NOT NULL UNIQUE,
            order_id VARCHAR(20) DEFAULT NULL,
            format_type VARCHAR(50) DEFAULT 'IQ-YYYY-XXXXXX',
            sequence_number INT NOT NULL,
            year_created INT NOT NULL,
            status ENUM('active', 'used', 'cancelled', 'archived') DEFAULT 'active',
            barcode_type VARCHAR(20) DEFAULT 'code128',
            barcode_data TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_by VARCHAR(50) DEFAULT NULL,
            notes TEXT DEFAULT NULL,
            INDEX idx_receipt_number (receipt_number),
            INDEX idx_order_id (order_id),
            INDEX idx_year_created (year_created),
            INDEX idx_status (status),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->conn->exec($query);
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path_parts = explode('/', trim($path, '/'));
        
        try {
            switch ($method) {
                case 'GET':
                    if (isset($_GET['action'])) {
                        switch ($_GET['action']) {
                            case 'list':
                                return $this->getReceiptNumbers();
                            case 'search':
                                return $this->searchReceiptNumbers();
                            case 'validate':
                                return $this->validateReceiptNumber();
                            case 'stats':
                                return $this->getStatistics();
                            case 'export':
                                return $this->exportReceiptNumbers();
                            default:
                                return $this->errorResponse('Invalid action', 400);
                        }
                    }
                    return $this->getReceiptNumbers();
                    
                case 'POST':
                    if (isset($_GET['action'])) {
                        switch ($_GET['action']) {
                            case 'generate':
                                return $this->generateReceiptNumber();
                            case 'batch':
                                return $this->batchGenerateReceiptNumbers();
                            case 'import':
                                return $this->importReceiptNumbers();
                            default:
                                return $this->errorResponse('Invalid action', 400);
                        }
                    }
                    return $this->createReceiptNumber();
                    
                case 'PUT':
                    return $this->updateReceiptNumber();
                    
                case 'DELETE':
                    return $this->deleteReceiptNumber();
                    
                default:
                    return $this->errorResponse('Method not allowed', 405);
            }
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
    
    private function generateReceiptNumber() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $format = $input['format'] ?? 'IQ-{YEAR}-{SEQUENCE}';
        $manual = $input['manual'] ?? false;
        $customNumber = $input['customNumber'] ?? '';
        $orderId = $input['orderId'] ?? null;
        $createdBy = $input['createdBy'] ?? 'system';
        
        if ($manual && !empty($customNumber)) {
            // Manual receipt number
            if (!$this->validateReceiptFormat($customNumber)) {
                return $this->errorResponse('Invalid receipt number format', 400);
            }
            
            if ($this->receiptNumberExists($customNumber)) {
                return $this->errorResponse('Receipt number already exists', 409);
            }
            
            $receiptNumber = $customNumber;
            $sequenceNumber = $this->extractSequenceFromReceipt($receiptNumber);
        } else {
            // Auto-generate receipt number
            $receiptNumber = $this->generateAutoReceiptNumber($format);
            $sequenceNumber = $this->extractSequenceFromReceipt($receiptNumber);
        }
        
        // Insert into database
        $query = "INSERT INTO " . $this->table_name . " 
                  (receipt_number, order_id, format_type, sequence_number, year_created, created_by) 
                  VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        $year = date('Y');
        
        if ($stmt->execute([$receiptNumber, $orderId, $format, $sequenceNumber, $year, $createdBy])) {
            return $this->successResponse([
                'receipt_number' => $receiptNumber,
                'order_id' => $orderId,
                'sequence_number' => $sequenceNumber,
                'year' => $year,
                'created_at' => date('Y-m-d H:i:s')
            ], 'Receipt number generated successfully');
        } else {
            return $this->errorResponse('Failed to save receipt number', 500);
        }
    }
    
    private function generateAutoReceiptNumber($format) {
        $year = date('Y');
        $maxAttempts = 1000;
        $attempts = 0;
        
        do {
            $sequence = $this->getNextSequenceNumber($year);
            $receiptNumber = str_replace(
                ['{YEAR}', '{SEQUENCE}'],
                [$year, str_pad($sequence, 6, '0', STR_PAD_LEFT)],
                $format
            );
            $attempts++;
        } while ($this->receiptNumberExists($receiptNumber) && $attempts < $maxAttempts);
        
        if ($attempts >= $maxAttempts) {
            throw new Exception('Unable to generate unique receipt number');
        }
        
        return $receiptNumber;
    }
    
    private function getNextSequenceNumber($year) {
        $query = "SELECT MAX(sequence_number) as max_seq FROM " . $this->table_name . " WHERE year_created = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$year]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return ($result['max_seq'] ?? 0) + 1;
    }
    
    private function validateReceiptFormat($receiptNumber) {
        // Validate format: IQ-YYYY-XXXXXX or similar patterns
        return preg_match('/^[A-Z]{2,3}-\d{4}-\d{6}$/', $receiptNumber);
    }
    
    private function extractSequenceFromReceipt($receiptNumber) {
        $parts = explode('-', $receiptNumber);
        return isset($parts[2]) ? intval($parts[2]) : 0;
    }
    
    private function receiptNumberExists($receiptNumber) {
        $query = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE receipt_number = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$receiptNumber]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function getReceiptNumbers() {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        $status = $_GET['status'] ?? '';
        $year = $_GET['year'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        if (!empty($status)) {
            $whereConditions[] = "status = ?";
            $params[] = $status;
        }
        
        if (!empty($year)) {
            $whereConditions[] = "year_created = ?";
            $params[] = $year;
        }
        
        if (!empty($search)) {
            $whereConditions[] = "(receipt_number LIKE ? OR order_id LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
        
        // Get total count
        $countQuery = "SELECT COUNT(*) FROM " . $this->table_name . " $whereClause";
        $countStmt = $this->conn->prepare($countQuery);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetchColumn();
        
        // Get data
        $query = "SELECT * FROM " . $this->table_name . " $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $receipts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $this->successResponse([
            'receipts' => $receipts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $totalCount,
                'pages' => ceil($totalCount / $limit)
            ]
        ]);
    }
    
    private function searchReceiptNumbers() {
        $query = $_GET['q'] ?? '';
        
        if (empty($query)) {
            return $this->errorResponse('Search query is required', 400);
        }
        
        $sql = "SELECT * FROM " . $this->table_name . " 
                WHERE receipt_number LIKE ? OR order_id LIKE ? 
                ORDER BY created_at DESC LIMIT 50";
        
        $stmt = $this->conn->prepare($sql);
        $searchTerm = "%$query%";
        $stmt->execute([$searchTerm, $searchTerm]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $this->successResponse(['results' => $results]);
    }
    
    private function validateReceiptNumber() {
        $receiptNumber = $_GET['receipt_number'] ?? '';
        
        if (empty($receiptNumber)) {
            return $this->errorResponse('Receipt number is required', 400);
        }
        
        $isValid = $this->validateReceiptFormat($receiptNumber);
        $exists = $this->receiptNumberExists($receiptNumber);
        
        return $this->successResponse([
            'receipt_number' => $receiptNumber,
            'is_valid_format' => $isValid,
            'exists' => $exists,
            'available' => $isValid && !$exists
        ]);
    }
    
    private function getStatistics() {
        $year = $_GET['year'] ?? date('Y');
        
        $stats = [];
        
        // Total receipts
        $query = "SELECT COUNT(*) FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_receipts'] = $stmt->fetchColumn();
        
        // Current year receipts
        $query = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE year_created = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$year]);
        $stats['current_year_receipts'] = $stmt->fetchColumn();
        
        // Today's receipts
        $query = "SELECT COUNT(*) FROM " . $this->table_name . " WHERE DATE(created_at) = CURDATE()";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['today_receipts'] = $stmt->fetchColumn();
        
        // Status breakdown
        $query = "SELECT status, COUNT(*) as count FROM " . $this->table_name . " GROUP BY status";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $statusBreakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stats['status_breakdown'] = array_column($statusBreakdown, 'count', 'status');
        
        // Monthly breakdown for current year
        $query = "SELECT MONTH(created_at) as month, COUNT(*) as count 
                  FROM " . $this->table_name . " 
                  WHERE year_created = ? 
                  GROUP BY MONTH(created_at) 
                  ORDER BY month";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$year]);
        $monthlyBreakdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stats['monthly_breakdown'] = $monthlyBreakdown;
        
        return $this->successResponse($stats);
    }
    
    private function updateReceiptNumber() {
        $input = json_decode(file_get_contents('php://input'), true);
        $receiptNumber = $input['receipt_number'] ?? '';
        $orderId = $input['order_id'] ?? null;
        $status = $input['status'] ?? 'active';
        $notes = $input['notes'] ?? '';
        
        if (empty($receiptNumber)) {
            return $this->errorResponse('Receipt number is required', 400);
        }
        
        $query = "UPDATE " . $this->table_name . " 
                  SET order_id = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP 
                  WHERE receipt_number = ?";
        
        $stmt = $this->conn->prepare($query);
        
        if ($stmt->execute([$orderId, $status, $notes, $receiptNumber])) {
            return $this->successResponse(['receipt_number' => $receiptNumber], 'Receipt number updated successfully');
        } else {
            return $this->errorResponse('Failed to update receipt number', 500);
        }
    }
    
    private function deleteReceiptNumber() {
        $receiptNumber = $_GET['receipt_number'] ?? '';
        
        if (empty($receiptNumber)) {
            return $this->errorResponse('Receipt number is required', 400);
        }
        
        // Archive instead of delete
        $query = "UPDATE " . $this->table_name . " 
                  SET status = 'archived', updated_at = CURRENT_TIMESTAMP 
                  WHERE receipt_number = ?";
        
        $stmt = $this->conn->prepare($query);
        
        if ($stmt->execute([$receiptNumber])) {
            return $this->successResponse(['receipt_number' => $receiptNumber], 'Receipt number archived successfully');
        } else {
            return $this->errorResponse('Failed to archive receipt number', 500);
        }
    }
    
    private function successResponse($data, $message = 'Success') {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    private function errorResponse($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// Initialize database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $receiptAPI = new ReceiptAPI($db);
    $receiptAPI->handleRequest();
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database connection failed: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
