// Customers Management System
class CustomersManager {
    constructor() {
        this.customers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.filters = {
            type: '',
            region: '',
            search: '',
            status: ''
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Search functionality
        document.addEventListener('input', (e) => {
            if (e.target.id === 'customers-search') {
                this.filters.search = e.target.value;
                this.filterCustomers();
            }
        });

        // Filter by type, region, and status
        document.addEventListener('change', (e) => {
            if (e.target.id === 'type-filter') {
                this.filters.type = e.target.value;
                this.filterCustomers();
            }
            if (e.target.id === 'region-filter') {
                this.filters.region = e.target.value;
                this.filterCustomers();
            }
            if (e.target.id === 'status-filter') {
                this.filters.status = e.target.value;
                this.filterCustomers();
            }
        });

        // Action buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-customer-btn') || e.target.closest('.add-customer-btn')) {
                e.preventDefault();
                this.showAddCustomerModal();
            }
            if (e.target.matches('.edit-customer-btn') || e.target.closest('.edit-customer-btn')) {
                e.preventDefault();
                const customerId = e.target.closest('.edit-customer-btn').getAttribute('data-customer-id');
                this.showEditCustomerModal(customerId);
            }
            if (e.target.matches('.view-customer-btn') || e.target.closest('.view-customer-btn')) {
                e.preventDefault();
                const customerId = e.target.closest('.view-customer-btn').getAttribute('data-customer-id');
                this.showCustomerDetails(customerId);
            }
            if (e.target.matches('.view-orders-btn') || e.target.closest('.view-orders-btn')) {
                e.preventDefault();
                const customerId = e.target.closest('.view-orders-btn').getAttribute('data-customer-id');
                this.showCustomerOrders(customerId);
            }
            if (e.target.matches('.toggle-status-btn') || e.target.closest('.toggle-status-btn')) {
                e.preventDefault();
                const customerId = e.target.closest('.toggle-status-btn').getAttribute('data-customer-id');
                this.toggleCustomerStatus(customerId);
            }
            if (e.target.matches('.bulk-commission-btn') || e.target.closest('.bulk-commission-btn')) {
                e.preventDefault();
                this.showBulkCommissionModal();
            }
            if (e.target.matches('.customer-analytics-btn') || e.target.closest('.customer-analytics-btn')) {
                e.preventDefault();
                this.showCustomerAnalytics();
            }
        });

        // Pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.page-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.getAttribute('data-page'));
                this.currentPage = page;
                this.renderCustomers();
            }
        });
    }

    loadContent() {
        const pageContent = document.getElementById('page-content');
        if (!pageContent) return;

        const customersHTML = `
            <div class="customers-header">
                <div class="customers-title">
                    <h1>إدارة العملاء</h1>
                    <p>عرض وإدارة قاعدة بيانات العملاء</p>
                </div>
                <div class="customers-actions">
                    <button class="neu-btn primary add-customer-btn">
                        <i class="fas fa-user-plus"></i>
                        إضافة عميل
                    </button>
                    <button class="neu-btn success bulk-commission-btn">
                        <i class="fas fa-money-bill-wave"></i>
                        تحديث العمولات
                    </button>
                    <button class="neu-btn warning customer-analytics-btn">
                        <i class="fas fa-chart-pie"></i>
                        تحليل العملاء
                    </button>
                    <button class="neu-btn secondary export-btn">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
            </div>

            <div class="customers-filters">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="customers-search" class="neu-input" placeholder="اسم العميل أو رقم الهاتف...">
                    </div>
                    <div class="filter-group">
                        <label>نوع العميل:</label>
                        <select id="type-filter" class="neu-select">
                            <option value="">جميع الأنواع</option>
                            <option value="individual">فردي</option>
                            <option value="company">شركة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>المنطقة:</label>
                        <select id="region-filter" class="neu-select">
                            <option value="">جميع المناطق</option>
                            <option value="1">بغداد - الكرخ</option>
                            <option value="2">بغداد - الرصافة</option>
                            <option value="3">بغداد - الصدر</option>
                            <option value="4">بغداد - الكاظمية</option>
                            <option value="5">بغداد - الأعظمية</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="status-filter" class="neu-select">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="customers-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="total-customers">0</h3>
                        <p>إجمالي العملاء</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="active-customers">0</h3>
                        <p>عملاء نشطين</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="company-customers">0</h3>
                        <p>شركات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="individual-customers">0</h3>
                        <p>أفراد</p>
                    </div>
                </div>
            </div>

            <div class="customers-table-container">
                <div class="table-header">
                    <h3>قائمة العملاء</h3>
                    <div class="table-actions">
                        <button class="neu-btn small refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="table-wrapper">
                    <table class="customers-table" id="customers-table">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>النوع</th>
                                <th>الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>المنطقة</th>
                                <th>الطلبات</th>
                                <th>إجمالي المبالغ</th>
                                <th>عمولة العميل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-tbody">
                            <!-- Customers will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="table-pagination" id="customers-pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        pageContent.innerHTML = customersHTML;
        this.renderCustomers();
        this.updateStats();
    }

    loadSampleData() {
        this.customers = [
            {
                id: 1,
                name: 'فاطمة أحمد',
                type: 'individual',
                companyName: null,
                phone: '+9647501111111',
                email: '<EMAIL>',
                address: 'حي الكرادة، شارع أبو نواس',
                regionId: 1,
                regionName: 'بغداد - الكرخ',
                specialRate: null,
                isActive: true,
                joinDate: '2024-01-15',
                lastOrderDate: '2024-12-25',
                totalOrders: 12,
                totalAmount: 850000,
                averageOrderValue: 70833
            },
            {
                id: 2,
                name: 'سارة محمد',
                type: 'individual',
                companyName: null,
                phone: '+9647502222222',
                email: '<EMAIL>',
                address: 'حي الجادرية، شارع الجامعة',
                regionId: 2,
                regionName: 'بغداد - الرصافة',
                specialRate: null,
                isActive: true,
                joinDate: '2024-02-20',
                lastOrderDate: '2024-12-26',
                totalOrders: 8,
                totalAmount: 620000,
                averageOrderValue: 77500
            },
            {
                id: 3,
                name: 'أحمد البغدادي',
                type: 'company',
                companyName: 'مطعم بغداد الأصيل',
                phone: '+9647503333333',
                email: '<EMAIL>',
                address: 'شارع المتنبي، الرصافة',
                regionId: 1,
                regionName: 'بغداد - الكرخ',
                commissionAmount: 4000,
                isActive: true,
                joinDate: '2023-11-10',
                lastOrderDate: '2024-12-26',
                totalOrders: 45,
                totalAmount: 2250000,
                averageOrderValue: 50000
            },
            {
                id: 4,
                name: 'مدير العمليات',
                type: 'company',
                companyName: 'شركة التوصيل السريع',
                phone: '+9647504444444',
                email: '<EMAIL>',
                address: 'حي الجادرية، مجمع الأعمال',
                regionId: 1,
                regionName: 'بغداد - الكرخ',
                commissionAmount: 3500,
                isActive: true,
                joinDate: '2024-01-05',
                lastOrderDate: '2024-12-27',
                totalOrders: 23,
                totalAmount: 1150000,
                averageOrderValue: 50000
            },
            {
                id: 5,
                name: 'محمد النوري',
                type: 'company',
                companyName: 'صيدلية دجلة',
                phone: '+9647505555555',
                email: '<EMAIL>',
                address: 'شارع الرشيد، الكرخ',
                regionId: 3,
                regionName: 'بغداد - الصدر',
                commissionAmount: 4500,
                isActive: true,
                joinDate: '2024-03-12',
                lastOrderDate: '2024-12-27',
                totalOrders: 18,
                totalAmount: 900000,
                averageOrderValue: 50000
            },
            {
                id: 6,
                name: 'علي حسن',
                type: 'individual',
                companyName: null,
                phone: '+9647506666666',
                email: '<EMAIL>',
                address: 'حي الكاظمية، شارع الكاظم',
                regionId: 4,
                regionName: 'بغداد - الكاظمية',
                specialRate: null,
                isActive: false,
                joinDate: '2024-06-15',
                lastOrderDate: '2024-11-20',
                totalOrders: 3,
                totalAmount: 180000,
                averageOrderValue: 60000
            }
        ];
    }

    renderCustomers() {
        const tbody = document.getElementById('customers-tbody');
        if (!tbody) return;

        const filteredCustomers = this.getFilteredCustomers();
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const customersToShow = filteredCustomers.slice(startIndex, endIndex);

        tbody.innerHTML = customersToShow.map(customer => `
            <tr>
                <td>
                    <div class="customer-info">
                        <div class="customer-avatar">
                            <i class="fas fa-${customer.type === 'company' ? 'building' : 'user'}"></i>
                        </div>
                        <div class="customer-details">
                            <strong>${customer.type === 'company' ? customer.companyName : customer.name}</strong>
                            <small>${customer.type === 'company' ? customer.name : customer.email}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="type-badge ${customer.type}">
                        ${customer.type === 'company' ? 'شركة' : 'فردي'}
                    </span>
                </td>
                <td>${customer.phone}</td>
                <td>${customer.email}</td>
                <td>${customer.regionName}</td>
                <td>
                    <div class="orders-info">
                        <strong>${customer.totalOrders}</strong>
                        <small>آخر طلب: ${this.formatDate(customer.lastOrderDate)}</small>
                    </div>
                </td>
                <td>
                    <div class="amount-info">
                        <strong>${this.formatCurrency(customer.totalAmount)}</strong>
                        <small>متوسط: ${this.formatCurrency(customer.averageOrderValue)}</small>
                    </div>
                </td>
                <td>
                    ${customer.commissionAmount ?
                        `<span class="commission-amount">${this.formatCurrency(customer.commissionAmount)}</span>` :
                        '<span class="no-commission">لا توجد</span>'
                    }
                </td>
                <td>
                    <span class="status-badge ${customer.isActive ? 'active' : 'inactive'}">
                        ${customer.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn view-customer-btn" data-customer-id="${customer.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn edit-customer-btn" data-customer-id="${customer.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view-orders-btn" data-customer-id="${customer.id}" title="عرض الطلبات">
                            <i class="fas fa-box"></i>
                        </button>
                        <button class="action-btn toggle-status-btn" data-customer-id="${customer.id}" title="تغيير الحالة">
                            <i class="fas fa-${customer.isActive ? 'pause' : 'play'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        this.renderPagination(filteredCustomers.length);
    }

    getFilteredCustomers() {
        return this.customers.filter(customer => {
            const matchesSearch = !this.filters.search || 
                customer.name.toLowerCase().includes(this.filters.search.toLowerCase()) ||
                customer.phone.includes(this.filters.search) ||
                (customer.companyName && customer.companyName.toLowerCase().includes(this.filters.search.toLowerCase()));
            
            const matchesType = !this.filters.type || customer.type === this.filters.type;
            const matchesRegion = !this.filters.region || customer.regionId.toString() === this.filters.region;
            const matchesStatus = !this.filters.status || 
                (this.filters.status === 'active' && customer.isActive) ||
                (this.filters.status === 'inactive' && !customer.isActive);

            return matchesSearch && matchesType && matchesRegion && matchesStatus;
        });
    }

    renderPagination(totalItems) {
        const pagination = document.getElementById('customers-pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination-info">';
        paginationHTML += `عرض ${((this.currentPage - 1) * this.itemsPerPage) + 1} - ${Math.min(this.currentPage * this.itemsPerPage, totalItems)} من ${totalItems}`;
        paginationHTML += '</div><div class="pagination-buttons">';

        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">السابق</button>`;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">التالي</button>`;
        }

        paginationHTML += '</div>';
        pagination.innerHTML = paginationHTML;
    }

    updateStats() {
        const totalCustomers = document.getElementById('total-customers');
        const activeCustomers = document.getElementById('active-customers');
        const companyCustomers = document.getElementById('company-customers');
        const individualCustomers = document.getElementById('individual-customers');

        if (totalCustomers) totalCustomers.textContent = this.customers.length;
        if (activeCustomers) activeCustomers.textContent = this.customers.filter(c => c.isActive).length;
        if (companyCustomers) companyCustomers.textContent = this.customers.filter(c => c.type === 'company').length;
        if (individualCustomers) individualCustomers.textContent = this.customers.filter(c => c.type === 'individual').length;
    }

    filterCustomers() {
        this.currentPage = 1;
        this.renderCustomers();
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount).replace('IQD', 'د.ع');
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    showAddCustomerModal() {
        if (window.ModalManager) {
            window.ModalManager.showAddCustomerModal();
        } else {
            alert('سيتم إضافة نافذة إضافة عميل جديد قريباً');
        }
    }

    showEditCustomerModal(customerId) {
        if (window.ModalManager) {
            window.ModalManager.showEditCustomerModal(customerId);
        } else {
            alert(`سيتم إضافة نافذة تعديل العميل ${customerId} قريباً`);
        }
    }

    showCustomerDetails(customerId) {
        if (window.ModalManager) {
            window.ModalManager.showCustomerDetailsModal(customerId);
        } else {
            alert(`سيتم إضافة نافذة تفاصيل العميل ${customerId} قريباً`);
        }
    }

    showCustomerOrders(customerId) {
        alert(`سيتم إضافة نافذة طلبات العميل ${customerId} قريباً`);
    }

    // Convert Arabic numerals to English numerals
    convertArabicToEnglishNumbers(text) {
        if (typeof text !== 'string') {
            text = String(text);
        }

        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        for (let i = 0; i < arabicNumbers.length; i++) {
            text = text.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
        }

        return text;
    }

    // Format numbers to always show English numerals
    formatNumber(number) {
        if (typeof number === 'number') {
            return this.convertArabicToEnglishNumbers(number.toLocaleString('en-US'));
        }
        return this.convertArabicToEnglishNumbers(String(number));
    }

    formatCurrency(amount) {
        const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
        return this.convertArabicToEnglishNumbers(formatted) + ' د.ع';
    }

    toggleCustomerStatus(customerId) {
        const customer = this.customers.find(c => c.id == customerId);
        if (customer) {
            customer.isActive = !customer.isActive;
            this.renderCustomers();
            this.updateStats();
            this.showNotification(`تم ${customer.isActive ? 'تفعيل' : 'إيقاف'} العميل`, 'success');
        }
    }

    showBulkCommissionModal() {
        const newCommission = prompt('أدخل العمولة الجديدة لجميع العملاء (بالدينار العراقي):');
        if (newCommission && !isNaN(newCommission)) {
            const commission = parseFloat(newCommission);
            const confirmUpdate = confirm(`هل تريد تحديث عمولة جميع العملاء إلى ${this.formatCurrency(commission)}؟`);

            if (confirmUpdate) {
                this.customers.forEach(customer => {
                    customer.commissionAmount = commission;
                });
                this.renderCustomers();
                this.showNotification(`تم تحديث عمولة ${this.customers.length} عميل`, 'success');
            }
        }
    }

    showCustomerAnalytics() {
        const analytics = this.calculateCustomerAnalytics();
        const analyticsText = `تحليل العملاء:\n\nإجمالي العملاء: ${analytics.totalCustomers}\nالعملاء النشطين: ${analytics.activeCustomers}\nأفضل عميل: ${analytics.topCustomer.name}\nمتوسط قيمة الطلب: ${this.formatCurrency(analytics.averageOrderValue)}\nإجمالي الإيرادات: ${this.formatCurrency(analytics.totalRevenue)}`;
        alert(analyticsText);
    }

    calculateCustomerAnalytics() {
        const totalCustomers = this.customers.length;
        const activeCustomers = this.customers.filter(c => c.isActive).length;

        const totalRevenue = this.customers.reduce((sum, c) => sum + c.totalAmount, 0);
        const totalOrders = this.customers.reduce((sum, c) => sum + c.totalOrders, 0);

        const topCustomer = this.customers.reduce((top, current) =>
            current.totalAmount > top.totalAmount ? current : top
        );

        return {
            totalCustomers,
            activeCustomers,
            topCustomer: {
                name: topCustomer.name,
                totalAmount: topCustomer.totalAmount
            },
            averageOrderValue: totalOrders > 0 ? Math.round(totalRevenue / totalOrders) : 0,
            totalRevenue
        };
    }

    showNotification(message, type = 'info') {
        if (window.app) {
            window.app.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Initialize Customers Manager
window.CustomersManager = new CustomersManager();
