<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لوحة التحكم - نظام إدارة شركة التوصيل العراقية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/neumorphism.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-test {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .feature-test h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-working { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-pending { background: #f39c12; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-cogs"></i> اختبار لوحة التحكم الرئيسية</h1>
            <p>اختبار شامل لجميع الأزرار والنوافذ والوظائف</p>
            <button class="test-button" onclick="runAllTests()">
                <i class="fas fa-play"></i> تشغيل جميع الاختبارات
            </button>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-tachometer-alt"></i> اختبار بطاقات الإحصائيات</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>إجمالي الطلبات <span class="status-indicator status-pending" id="stat-total-orders"></span></h4>
                    <button class="test-button" onclick="testStatCard('total-orders')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>طلبات مسلمة <span class="status-indicator status-pending" id="stat-delivered"></span></h4>
                    <button class="test-button" onclick="testStatCard('delivered')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>طلبات معلقة <span class="status-indicator status-pending" id="stat-pending"></span></h4>
                    <button class="test-button" onclick="testStatCard('pending')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إجمالي الإيرادات <span class="status-indicator status-pending" id="stat-revenue"></span></h4>
                    <button class="test-button" onclick="testStatCard('revenue')">اختبار</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-bolt"></i> اختبار الإجراءات السريعة</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>إضافة طلب جديد <span class="status-indicator status-pending" id="action-new-order"></span></h4>
                    <button class="test-button" onclick="testQuickAction('new-order')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إضافة مندوب <span class="status-indicator status-pending" id="action-add-driver"></span></h4>
                    <button class="test-button" onclick="testQuickAction('add-driver')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إضافة عميل <span class="status-indicator status-pending" id="action-add-customer"></span></h4>
                    <button class="test-button" onclick="testQuickAction('add-customer')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>إدارة المناطق <span class="status-indicator status-pending" id="action-regions"></span></h4>
                    <button class="test-button" onclick="testQuickAction('view-regions')">اختبار</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-bars"></i> اختبار التنقل</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>صفحة الطلبات <span class="status-indicator status-pending" id="nav-orders"></span></h4>
                    <button class="test-button" onclick="testNavigation('orders')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>صفحة المندوبين <span class="status-indicator status-pending" id="nav-drivers"></span></h4>
                    <button class="test-button" onclick="testNavigation('drivers')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>صفحة العملاء <span class="status-indicator status-pending" id="nav-customers"></span></h4>
                    <button class="test-button" onclick="testNavigation('customers')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>الصفحة المالية <span class="status-indicator status-pending" id="nav-finance"></span></h4>
                    <button class="test-button" onclick="testNavigation('finance')">اختبار</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3><i class="fas fa-window-maximize"></i> اختبار النوافذ المنبثقة</h3>
            <div class="test-grid">
                <div class="feature-test">
                    <h4>نافذة إضافة طلب <span class="status-indicator status-pending" id="modal-add-order"></span></h4>
                    <button class="test-button" onclick="testModal('add-order')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>نافذة إضافة مندوب <span class="status-indicator status-pending" id="modal-add-driver"></span></h4>
                    <button class="test-button" onclick="testModal('add-driver')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>نافذة إضافة عميل <span class="status-indicator status-pending" id="modal-add-customer"></span></h4>
                    <button class="test-button" onclick="testModal('add-customer')">اختبار</button>
                </div>
                <div class="feature-test">
                    <h4>نافذة البحث <span class="status-indicator status-pending" id="modal-search"></span></h4>
                    <button class="test-button" onclick="testModal('search')">اختبار</button>
                </div>
            </div>
        </div>

        <div id="test-results"></div>
    </div>

    <!-- تحميل جميع ملفات النظام -->
    <script src="assets/js/app.js"></script>
    <script src="assets/js/dashboard.js"></script>
    <script src="assets/js/modals.js"></script>
    <script src="assets/js/orders.js"></script>
    <script src="assets/js/customers.js"></script>

    <script>
        let testResults = [];

        function runAllTests() {
            testResults = [];
            
            // اختبار بطاقات الإحصائيات
            testStatCard('total-orders');
            testStatCard('delivered');
            testStatCard('pending');
            testStatCard('revenue');
            
            // اختبار الإجراءات السريعة
            testQuickAction('new-order');
            testQuickAction('add-driver');
            testQuickAction('add-customer');
            testQuickAction('view-regions');
            
            // اختبار التنقل
            testNavigation('orders');
            testNavigation('drivers');
            testNavigation('customers');
            testNavigation('finance');
            
            // اختبار النوافذ المنبثقة
            testModal('add-order');
            testModal('add-driver');
            testModal('add-customer');
            testModal('search');
            
            // عرض النتائج
            setTimeout(showResults, 2000);
        }

        function testStatCard(type) {
            try {
                // محاكاة النقر على بطاقة الإحصائيات
                const event = new Event('click', { bubbles: true });
                const mockCard = { 
                    getAttribute: (attr) => {
                        if (attr === 'data-type') return 'orders';
                        if (attr === 'data-filter') return type;
                        return null;
                    }
                };
                
                if (window.app && window.app.handleStatCardClick) {
                    window.app.handleStatCardClick(mockCard);
                    updateStatus(`stat-${type}`, 'working');
                    addTestResult(`بطاقة ${type}`, true, 'تعمل بشكل صحيح');
                } else {
                    updateStatus(`stat-${type}`, 'error');
                    addTestResult(`بطاقة ${type}`, false, 'النظام غير متاح');
                }
            } catch (error) {
                updateStatus(`stat-${type}`, 'error');
                addTestResult(`بطاقة ${type}`, false, error.message);
            }
        }

        function testQuickAction(action) {
            try {
                if (window.app && window.app.handleQuickAction) {
                    window.app.handleQuickAction(action);
                    updateStatus(`action-${action.replace('-', '')}`, 'working');
                    addTestResult(`إجراء ${action}`, true, 'تم تنفيذه بنجاح');
                } else {
                    updateStatus(`action-${action.replace('-', '')}`, 'error');
                    addTestResult(`إجراء ${action}`, false, 'النظام غير متاح');
                }
            } catch (error) {
                updateStatus(`action-${action.replace('-', '')}`, 'error');
                addTestResult(`إجراء ${action}`, false, error.message);
            }
        }

        function testNavigation(page) {
            try {
                if (window.app && window.app.loadPage) {
                    window.app.loadPage(page);
                    updateStatus(`nav-${page}`, 'working');
                    addTestResult(`تنقل ${page}`, true, 'تم التنقل بنجاح');
                } else {
                    updateStatus(`nav-${page}`, 'error');
                    addTestResult(`تنقل ${page}`, false, 'النظام غير متاح');
                }
            } catch (error) {
                updateStatus(`nav-${page}`, 'error');
                addTestResult(`تنقل ${page}`, false, error.message);
            }
        }

        function testModal(modalType) {
            try {
                const modalManager = window.ModalManager ? new ModalManager() : null;
                if (modalManager) {
                    switch (modalType) {
                        case 'add-order':
                            modalManager.showAddOrderModal();
                            break;
                        case 'add-driver':
                            modalManager.showAddDriverModal();
                            break;
                        case 'add-customer':
                            modalManager.showAddCustomerModal();
                            break;
                        case 'search':
                            modalManager.showSearchModal();
                            break;
                    }
                    updateStatus(`modal-${modalType.replace('-', '')}`, 'working');
                    addTestResult(`نافذة ${modalType}`, true, 'تم فتحها بنجاح');
                } else {
                    updateStatus(`modal-${modalType.replace('-', '')}`, 'error');
                    addTestResult(`نافذة ${modalType}`, false, 'ModalManager غير متاح');
                }
            } catch (error) {
                updateStatus(`modal-${modalType.replace('-', '')}`, 'error');
                addTestResult(`نافذة ${modalType}`, false, error.message);
            }
        }

        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = `status-indicator status-${status}`;
            }
        }

        function addTestResult(testName, passed, details) {
            testResults.push({
                name: testName,
                passed: passed,
                details: details,
                timestamp: new Date().toLocaleString('ar-IQ')
            });
        }

        function showResults() {
            const resultsContainer = document.getElementById('test-results');
            let html = '<div class="test-section"><h3>نتائج الاختبار</h3>';
            
            const passedTests = testResults.filter(t => t.passed).length;
            const totalTests = testResults.length;
            
            html += `<p><strong>النتيجة الإجمالية: ${passedTests}/${totalTests} اختبار ناجح</strong></p>`;
            
            testResults.forEach(result => {
                const resultClass = result.passed ? 'test-pass' : 'test-fail';
                const icon = result.passed ? 'fa-check' : 'fa-times';
                html += `
                    <div class="test-result ${resultClass}">
                        <i class="fas ${icon}"></i>
                        ${result.name}: ${result.details}
                        <small style="display: block; margin-top: 5px;">${result.timestamp}</small>
                    </div>
                `;
            });
            
            html += '</div>';
            resultsContainer.innerHTML = html;
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            if (!window.app) {
                window.app = new DeliveryManagementSystem();
            }
        });
    </script>
</body>
</html>
